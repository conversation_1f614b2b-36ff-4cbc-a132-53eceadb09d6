import{r as s,j as e}from"./react-vendor-CyNirxNk.js";import{u as a,c as t,s as c,a as l,j as n,k as d,h as i,g as r,b as m}from"./store-basicstore-DgVIqOAD.js";import{u as x,a as o}from"./store-settingsstore-CkjrIQl7.js";import{u as h,f as b}from"./store-apistore-B0ecmVeH.js";function j(){const j=a(t),N=a(c),u=a(l),g=a(n),v=a(d),p=a(i),f=a(r),y=a(m),w=x(o),C=h(b),{increment:k,decrement:S,incrementByAmount:D,setName:E,addItem:T,simulateAsyncOperation:I,clearError:A,reset:P}=a(),{setTheme:L}=x(),{fetchUsers:O,clearRequestLogs:R}=h(),[U,M]=s.useState([]),[W,$]=s.useState({devtoolsEnabled:!1,persistEnabled:!1,subscribersCount:0,immerEnabled:!1});s.useEffect(()=>{const s=a.subscribe(t,s=>{M(e=>[...e,{id:Date.now(),type:"count",message:`计数器变更为: ${s}`,timestamp:(new Date).toLocaleTimeString()}])}),e=a.subscribe(n,s=>{M(e=>[...e,{id:Date.now(),type:"user",message:`用户信息更新: ${s.name}`,timestamp:(new Date).toLocaleTimeString()}])}),c=x.subscribe(o,s=>{M(e=>[...e,{id:Date.now(),type:"theme",message:`主题切换为: ${s}`,timestamp:(new Date).toLocaleTimeString()}])});return $({devtoolsEnabled:!0,persistEnabled:!0,subscribersCount:3,immerEnabled:!0}),()=>{s(),e(),c()}},[]);const B=()=>{M([])};return e.jsxs("div",{className:"middleware-demo-page",children:[e.jsxs("div",{className:"card-header",children:[e.jsx("h1",{className:"card-title",children:"🔧 中间件演示"}),e.jsx("p",{className:"card-description",children:"展示Zustand的各种中间件功能，包括devtools、subscribeWithSelector、immer、persist等"})]}),e.jsxs("div",{className:"card mb-lg",children:[e.jsx("h2",{className:"card-title",children:"📊 中间件状态"}),e.jsxs("div",{className:"grid grid-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold "+(W.devtoolsEnabled?"text-success":"text-danger"),children:W.devtoolsEnabled?"✅":"❌"}),e.jsx("div",{className:"text-sm text-secondary",children:"DevTools"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold "+(W.persistEnabled?"text-success":"text-danger"),children:W.persistEnabled?"✅":"❌"}),e.jsx("div",{className:"text-sm text-secondary",children:"Persist"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-info",children:W.subscribersCount}),e.jsx("div",{className:"text-sm text-secondary",children:"订阅者"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold "+(W.immerEnabled?"text-success":"text-danger"),children:W.immerEnabled?"✅":"❌"}),e.jsx("div",{className:"text-sm text-secondary",children:"Immer"})]})]})]}),e.jsxs("div",{className:"grid grid-2",children:[e.jsxs("div",{className:"card",children:[e.jsx("h2",{className:"card-title",children:"🛠️ DevTools 中间件"}),e.jsx("p",{className:"text-sm text-secondary mb-md",children:"DevTools中间件允许在浏览器开发者工具中调试状态变化"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{children:"状态跟踪"}),e.jsx("span",{className:"badge badge-success",children:"已启用"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{children:"时间旅行"}),e.jsx("span",{className:"badge badge-success",children:"支持"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{children:"Action 记录"}),e.jsx("span",{className:"badge badge-success",children:"已启用"})]})]}),e.jsxs("div",{className:"mt-md",children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"测试Actions"}),e.jsxs("div",{className:"flex gap-sm flex-wrap",children:[e.jsx("button",{onClick:k,className:"btn btn-primary btn-sm",children:"增加计数"}),e.jsx("button",{onClick:S,className:"btn btn-secondary btn-sm",children:"减少计数"}),e.jsx("button",{onClick:()=>E("DevTools Test"),className:"btn btn-info btn-sm",children:"设置名称"}),e.jsx("button",{onClick:()=>T("DevTools项目"),className:"btn btn-success btn-sm",children:"添加项目"})]})]}),e.jsx("div",{className:"mt-md p-sm bg-surface rounded",children:e.jsx("div",{className:"text-xs text-secondary",children:"💡 打开浏览器开发者工具 → Redux DevTools 查看状态变化"})})]}),e.jsxs("div",{className:"card",children:[e.jsx("h2",{className:"card-title",children:"👂 SubscribeWithSelector"}),e.jsx("p",{className:"text-sm text-secondary mb-md",children:"精确订阅特定状态变化，避免不必要的重新渲染"}),e.jsxs("div",{className:"mb-md",children:[e.jsxs("h3",{className:"font-semibold mb-sm",children:["订阅日志 (",U.length,")"]}),e.jsxs("div",{className:"max-h-32 overflow-y-auto space-y-1",children:[U.slice(-5).map(s=>e.jsxs("div",{className:"text-xs p-xs bg-surface rounded",children:[e.jsx("span",{className:"badge badge-sm "+("count"===s.type?"badge-primary":"user"===s.type?"badge-success":"badge-info"),children:s.type}),e.jsx("span",{className:"ml-xs",children:s.message}),e.jsx("span",{className:"text-muted ml-xs",children:s.timestamp})]},s.id)),0===U.length&&e.jsx("div",{className:"text-center text-muted py-sm",children:"暂无订阅日志"})]})]}),e.jsxs("div",{className:"flex gap-sm",children:[e.jsx("button",{onClick:B,className:"btn btn-warning btn-sm",children:"清除日志"}),e.jsx("button",{onClick:()=>D(Math.floor(10*Math.random())),className:"btn btn-primary btn-sm",children:"随机增加"})]})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"🔄 Immer 中间件"}),e.jsx("p",{className:"text-sm text-secondary mb-md",children:'使用Immer进行不可变状态更新，可以直接"修改"状态对象'}),e.jsxs("div",{className:"grid grid-2",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"当前状态"}),e.jsx("pre",{className:"bg-surface p-md rounded text-sm overflow-auto",children:JSON.stringify({count:j,name:N,itemsCount:u.length,user:{name:g.name,age:g.age}},null,2)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"Immer操作"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("button",{onClick:()=>{a.getState().batchUpdate({count:j+10,name:"Batch Updated",items:[...u,{id:Date.now(),name:"批量添加项目",completed:!1}]})},className:"btn btn-primary w-full",children:"🔄 批量更新 (Immer)"}),e.jsx("button",{onClick:()=>a.getState().updateUser({age:g.age+1,profile:{...g.profile,lastLogin:(new Date).toISOString()}}),className:"btn btn-success w-full",children:"👤 更新用户信息"}),e.jsx("button",{onClick:async()=>{try{await I(),await O()}catch(s){}},disabled:f,className:"btn btn-info w-full",children:f?"⏳ 处理中...":"🚀 异步操作测试"})]})]})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"💾 Persist 中间件"}),e.jsx("p",{className:"text-sm text-secondary mb-md",children:"自动将状态持久化到本地存储，页面刷新后自动恢复"}),e.jsxs("div",{className:"grid grid-3",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"主题持久化"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("p",{className:"text-sm",children:["当前主题: ",e.jsx("span",{className:"badge badge-info",children:w})]}),e.jsxs("div",{className:"flex gap-xs",children:[e.jsx("button",{onClick:()=>L("light"),className:"btn btn-sm btn-secondary",children:"☀️"}),e.jsx("button",{onClick:()=>L("dark"),className:"btn btn-sm btn-secondary",children:"🌙"}),e.jsx("button",{onClick:()=>L("auto"),className:"btn btn-sm btn-secondary",children:"🔄"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"统计信息"}),e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsxs("div",{children:["历史记录: ",v.length," 条"]}),e.jsxs("div",{children:["操作总数: ",y.totalOperations]}),e.jsxs("div",{children:["错误次数: ",y.errorCount]}),e.jsxs("div",{children:["成功率: ",y.successRate,"%"]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"存储管理"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("button",{onClick:()=>localStorage.clear(),className:"btn btn-warning btn-sm w-full",children:"🗑️ 清除所有存储"}),e.jsx("button",{onClick:()=>window.location.reload(),className:"btn btn-info btn-sm w-full",children:"🔄 重新加载页面"})]})]})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"⚠️ 错误处理"}),e.jsxs("div",{className:"grid grid-2",children:[e.jsxs("div",{children:[p&&e.jsxs("div",{className:"alert alert-danger",children:[e.jsx("strong",{children:"错误:"})," ",p,e.jsx("button",{onClick:A,className:"btn btn-sm btn-danger ml-sm",children:"清除错误"})]}),!p&&e.jsx("div",{className:"alert alert-success",children:"当前没有错误"})]}),e.jsx("div",{children:e.jsx("button",{onClick:()=>{try{throw new Error("测试错误处理")}catch(s){}},className:"btn btn-danger",children:"🚨 模拟错误"})})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"🌐 API请求中间件"}),e.jsx("p",{className:"text-sm text-secondary mb-md",children:"API请求的拦截器和日志记录中间件"}),e.jsxs("div",{className:"grid grid-2",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"最近请求日志"}),e.jsxs("div",{className:"max-h-32 overflow-y-auto space-y-1",children:[C.slice(-3).map(s=>e.jsxs("div",{className:"text-xs p-xs bg-surface rounded",children:[e.jsx("span",{className:"badge badge-sm "+("cached"===s.status?"badge-info":"number"==typeof s.status&&s.status<400?"badge-success":"badge-danger"),children:s.method}),e.jsx("span",{className:"ml-xs",children:s.url})]},s.id)),0===C.length&&e.jsx("div",{className:"text-center text-muted py-sm",children:"暂无请求日志"})]})]}),e.jsx("div",{children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("button",{onClick:O,className:"btn btn-primary btn-sm w-full",children:"📡 发起API请求"}),e.jsx("button",{onClick:R,className:"btn btn-warning btn-sm w-full",children:"🗑️ 清除请求日志"})]})})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"📖 中间件说明"}),e.jsxs("div",{className:"grid grid-2",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-md",children:"🔧 核心中间件"}),e.jsxs("ul",{className:"text-sm text-secondary space-y-1",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"devtools:"})," Redux DevTools集成"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"subscribeWithSelector:"})," 选择性订阅"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"immer:"})," 不可变状态更新"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"persist:"})," 状态持久化"]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-md",children:"⚡ 自定义中间件"}),e.jsxs("ul",{className:"text-sm text-secondary space-y-1",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"错误处理:"})," 统一错误捕获和处理"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"日志记录:"})," 操作历史跟踪"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"API拦截:"})," 请求响应拦截"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"性能监控:"})," 状态变更性能统计"]})]})]})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"🔄 重置操作"}),e.jsxs("div",{className:"flex gap-sm",children:[e.jsx("button",{onClick:P,className:"btn btn-warning",children:"🔄 重置基础Store"}),e.jsx("button",{onClick:()=>{P(),B(),R()},className:"btn btn-danger",children:"🗑️ 重置所有"})]})]})]})}export{j as M};
//# sourceMappingURL=page-middlewaredemo-CV2OZ4zC.js.map
