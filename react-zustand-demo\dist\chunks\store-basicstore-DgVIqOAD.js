import{c as t,d as e,s as n,i as a}from"./react-vendor-CyNirxNk.js";const s=t(e(n(a((t,e)=>({count:0,name:"",items:[],user:{name:"",email:""},isLoading:!1,error:null,history:[],increment:()=>t(t=>{t.count+=1,t.history.push({action:"increment",timestamp:(new Date).toISOString(),value:t.count})}),decrement:()=>t(t=>{t.count-=1,t.history.push({action:"decrement",timestamp:(new Date).toISOString(),value:t.count})}),incrementByAmount:e=>t(t=>{t.count+=e,t.history.push({action:"incrementByAmount",timestamp:(new Date).toISOString(),value:t.count,amount:e})}),resetCount:()=>t(t=>{t.count=0,t.history.push({action:"resetCount",timestamp:(new Date).toISOString(),value:0})}),setName:e=>t(t=>{t.name=e,t.history.push({action:"setName",timestamp:(new Date).toISOString(),value:e})}),addItem:e=>t(t=>{const n={id:Date.now(),text:e,createdAt:(new Date).toISOString()};t.items.push(n),t.history.push({action:"addItem",timestamp:(new Date).toISOString(),value:e})}),removeItem:e=>t(t=>{const n=t.items.findIndex(t=>t.id===e);if(n>-1){const e=t.items[n];t.items.splice(n,1),t.history.push({action:"removeItem",timestamp:(new Date).toISOString(),value:e.text})}}),updateItem:(e,n)=>t(t=>{const a=t.items.find(t=>t.id===e);if(a){const e=a.text;a.text=n,a.updatedAt=(new Date).toISOString(),t.history.push({action:"updateItem",timestamp:(new Date).toISOString(),value:`${e} → ${n}`})}}),updateUser:e=>t(t=>{Object.assign(t.user,e),t.history.push({action:"updateUser",timestamp:(new Date).toISOString(),value:e})}),batchUpdate:e=>t(t=>{void 0!==e.count&&(t.count=e.count),void 0!==e.name&&(t.name=e.name),void 0!==e.user&&Object.assign(t.user,e.user),t.history.push({action:"batchUpdate",timestamp:(new Date).toISOString(),value:e})}),simulateAsyncOperation:async(e=2e3)=>{t(t=>{t.isLoading=!0,t.error=null});try{await new Promise((t,n)=>{setTimeout(()=>{Math.random()>.8?n(new Error("模拟的异步操作失败")):t()},e)}),t(t=>{t.isLoading=!1,t.count+=10,t.history.push({action:"simulateAsyncOperation",timestamp:(new Date).toISOString(),value:"异步操作成功"})})}catch(n){t(t=>{t.isLoading=!1,t.error=n.message,t.history.push({action:"simulateAsyncOperation",timestamp:(new Date).toISOString(),value:`异步操作失败: ${n.message}`})})}},clearError:()=>t(t=>{t.error=null}),clearHistory:()=>t(t=>{t.history=[]}),reset:()=>t(t=>{t.count=0,t.name="",t.items=[],t.user={name:"",email:""},t.isLoading=!1,t.error=null,t.history=[{action:"reset",timestamp:(new Date).toISOString(),value:"状态已重置"}]}),getDoubleCount:()=>2*e().count,getTripleCount:()=>3*e().count,getFormattedUser:()=>{const{user:t}=e();return t.name&&t.email?`${t.name} (${t.email})`:"未设置用户信息"},getRecentHistory:()=>e().history.slice(-10),getStats:()=>{const t=e();return{totalItems:t.items.length,totalActions:t.history.length,lastAction:t.history[t.history.length-1]?.action||"none",hasError:!!t.error,isActive:t.count>0||t.items.length>0}}}))))),i=t=>t.count,o=t=>t.getDoubleCount(),r=t=>t.getTripleCount(),m=t=>t.name,u=t=>t.items,c=t=>t.user,h=t=>t.getFormattedUser(),l=t=>t.isLoading,p=t=>t.error,g=t=>t.history,d=t=>t.getRecentHistory(),S=t=>t.getStats();export{u as a,S as b,i as c,o as d,r as e,h as f,l as g,p as h,d as i,c as j,g as k,m as s,s as u};
//# sourceMappingURL=store-basicstore-DgVIqOAD.js.map
