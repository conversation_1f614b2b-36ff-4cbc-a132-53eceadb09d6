import{c as e,d as t,p as a,b as n,s as o,i as s}from"./react-vendor-CyNirxNk.js";const i=e(t(a(o(s((e,t)=>({theme:"auto",language:"zh",notifications:!0,autoSave:!0,fontSize:14,layout:"default",preferences:{showWelcome:!0,enableAnimations:!0,soundEnabled:!1,emailNotifications:!0,pushNotifications:!1,autoRefresh:!0,showTooltips:!0,compactMode:!1},advancedSettings:{debugMode:!1,experimentalFeatures:!1,performanceMode:!1,logLevel:"info",cacheSize:100,requestTimeout:1e4,retryAttempts:3,enableMetrics:!0},customSettings:{shortcuts:{save:"Ctrl+S",search:"Ctrl+F",help:"F1",console:"Ctrl+`"},colors:{primary:"#0066cc",secondary:"#6c757d",success:"#28a745",warning:"#ffc107",danger:"#dc3545"},colorScheme:{primary:"#0066cc",secondary:"#6c757d",background:"#ffffff",surface:"#f8f9fa",text:"#212529"},dashboard:{widgets:["stats","recent","quick-actions"],layout:"grid",density:"normal"}},lastSaved:null,isDirty:!1,version:"1.0.0",setTheme:t=>e(e=>{e.theme=t,e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1)}),setLanguage:t=>e(e=>{e.language=t,e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1)}),toggleNotifications:()=>e(e=>{e.notifications=!e.notifications,e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1)}),toggleAutoSave:()=>e(e=>{e.autoSave=!e.autoSave,e.isDirty=!0,e.autoSave&&(e.lastSaved=(new Date).toISOString(),e.isDirty=!1)}),setFontSize:t=>e(e=>{t>=10&&t<=24&&(e.fontSize=t,e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1))}),setLayout:t=>e(e=>{e.layout=t,e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1)}),updatePreference:(t,a)=>e(e=>{t in e.preferences&&(e.preferences[t]=a,e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1))}),updatePreferences:t=>e(e=>{Object.assign(e.preferences,t),e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1)}),togglePreference:t=>e(e=>{t in e.preferences&&"boolean"==typeof e.preferences[t]&&(e.preferences[t]=!e.preferences[t],e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1))}),updateAdvancedSetting:(t,a)=>e(e=>{t in e.advancedSettings&&(e.advancedSettings[t]=a,e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1))}),updateAdvancedSettings:t=>e(e=>{Object.assign(e.advancedSettings,t),e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1)}),toggleAdvancedSetting:t=>e(e=>{t in e.advancedSettings&&"boolean"==typeof e.advancedSettings[t]&&(e.advancedSettings[t]=!e.advancedSettings[t],e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1))}),updateCustomSetting:(t,a,n)=>e(e=>{t in e.customSettings&&a in e.customSettings[t]&&(e.customSettings[t][a]=n,e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1))}),updateShortcut:(t,a)=>e(e=>{t in e.customSettings.shortcuts&&(e.customSettings.shortcuts[t]=a,e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1))}),updateColor:(t,a)=>e(e=>{t in e.customSettings.colors&&(e.customSettings.colors[t]=a,e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1)),t in e.customSettings.colorScheme&&(e.customSettings.colorScheme[t]=a,e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1))}),batchUpdate:t=>e(e=>{t.theme&&(e.theme=t.theme),t.language&&(e.language=t.language),t.fontSize&&(e.fontSize=t.fontSize),t.layout&&(e.layout=t.layout),"boolean"==typeof t.notifications&&(e.notifications=t.notifications),"boolean"==typeof t.autoSave&&(e.autoSave=t.autoSave),t.preferences&&Object.assign(e.preferences,t.preferences),t.advancedSettings&&Object.assign(e.advancedSettings,t.advancedSettings),t.customSettings&&Object.assign(e.customSettings,t.customSettings),e.isDirty=!0,e.lastSaved=e.autoSave?(new Date).toISOString():e.lastSaved,e.autoSave&&(e.isDirty=!1)}),applyPreset:a=>e(()=>{const e={developer:{theme:"dark",fontSize:14,layout:"compact",preferences:{enableAnimations:!1,showWelcome:!1,soundEnabled:!1,compactMode:!0},advancedSettings:{debugMode:!0,experimentalFeatures:!0,performanceMode:!0,logLevel:"debug",enableMetrics:!0}},designer:{theme:"light",fontSize:16,layout:"wide",preferences:{enableAnimations:!0,showWelcome:!0,soundEnabled:!0,compactMode:!1},advancedSettings:{debugMode:!1,experimentalFeatures:!1,performanceMode:!1,logLevel:"info",enableMetrics:!1}},minimal:{theme:"auto",fontSize:14,layout:"default",preferences:{enableAnimations:!1,showWelcome:!1,soundEnabled:!1,compactMode:!0},advancedSettings:{debugMode:!1,experimentalFeatures:!1,performanceMode:!0,logLevel:"warn",enableMetrics:!1}},accessibility:{theme:"auto",fontSize:18,layout:"wide",preferences:{enableAnimations:!1,showWelcome:!0,soundEnabled:!0,compactMode:!1},advancedSettings:{debugMode:!1,experimentalFeatures:!1,performanceMode:!1,logLevel:"info",enableMetrics:!1}}}[a];e&&t().batchUpdate(e)}),exportSettings:()=>{const e=t();return{theme:e.theme,language:e.language,notifications:e.notifications,autoSave:e.autoSave,fontSize:e.fontSize,layout:e.layout,preferences:e.preferences,advancedSettings:e.advancedSettings,customSettings:e.customSettings,exportedAt:(new Date).toISOString(),version:e.version}},importSettings:t=>e(e=>{try{t.theme&&["light","dark","auto"].includes(t.theme)&&(e.theme=t.theme),t.language&&["zh","en"].includes(t.language)&&(e.language=t.language),"boolean"==typeof t.notifications&&(e.notifications=t.notifications),"boolean"==typeof t.autoSave&&(e.autoSave=t.autoSave),t.fontSize&&t.fontSize>=10&&t.fontSize<=24&&(e.fontSize=t.fontSize),t.layout&&["default","compact","wide"].includes(t.layout)&&(e.layout=t.layout),t.preferences&&"object"==typeof t.preferences&&Object.assign(e.preferences,t.preferences),t.advancedSettings&&"object"==typeof t.advancedSettings&&Object.assign(e.advancedSettings,t.advancedSettings),t.customSettings&&"object"==typeof t.customSettings&&Object.assign(e.customSettings,t.customSettings),e.lastSaved=(new Date).toISOString(),e.isDirty=!1}catch(a){throw new Error("导入设置失败：文件格式错误")}}),saveSettings:()=>e(e=>{e.lastSaved=(new Date).toISOString(),e.isDirty=!1}),resetToDefaults:()=>e(e=>{const t=e.autoSave;e.theme="auto",e.language="zh",e.notifications=!0,e.fontSize=14,e.layout="default",e.preferences={showWelcome:!0,enableAnimations:!0,soundEnabled:!1,emailNotifications:!0,pushNotifications:!1,autoRefresh:!0,showTooltips:!0,compactMode:!1},e.advancedSettings={debugMode:!1,experimentalFeatures:!1,performanceMode:!1,logLevel:"info",cacheSize:100,requestTimeout:1e4,retryAttempts:3,enableMetrics:!0},e.customSettings={shortcuts:{save:"Ctrl+S",search:"Ctrl+F",help:"F1",console:"Ctrl+`"},colors:{primary:"#0066cc",secondary:"#6c757d",success:"#28a745",warning:"#ffc107",danger:"#dc3545"},dashboard:{widgets:["stats","recent","quick-actions"],layout:"grid",density:"normal"}},e.autoSave=t,e.lastSaved=(new Date).toISOString(),e.isDirty=!1}),reset:()=>e(e=>{e.theme="auto",e.language="zh",e.notifications=!0,e.autoSave=!0,e.fontSize=14,e.layout="default",e.preferences={showWelcome:!0,enableAnimations:!0,soundEnabled:!1,emailNotifications:!0,pushNotifications:!1,autoRefresh:!0,showTooltips:!0,compactMode:!1},e.advancedSettings={debugMode:!1,experimentalFeatures:!1,performanceMode:!1,logLevel:"info",cacheSize:100,requestTimeout:1e4,retryAttempts:3,enableMetrics:!0},e.customSettings={shortcuts:{save:"Ctrl+S",search:"Ctrl+F",help:"F1",console:"Ctrl+`"},colors:{primary:"#0066cc",secondary:"#6c757d",success:"#28a745",warning:"#ffc107",danger:"#dc3545"},dashboard:{widgets:["stats","recent","quick-actions"],layout:"grid",density:"normal"}},e.lastSaved=null,e.isDirty=!1,e.version="1.0.0"}),getThemeLabel:()=>{const e=t().theme;return{light:"浅色",dark:"深色",auto:"自动"}[e]||e},getLanguageLabel:()=>{const e=t().language;return{zh:"中文",en:"English"}[e]||e},getLayoutLabel:()=>{const e=t().layout;return{default:"默认",compact:"紧凑",wide:"宽屏"}[e]||e},getAllSettings:()=>{const e=t();return{theme:e.theme,language:e.language,notifications:e.notifications,autoSave:e.autoSave,fontSize:e.fontSize,layout:e.layout,preferences:e.preferences,advancedSettings:e.advancedSettings,customSettings:e.customSettings}},getSettingsSummary:()=>{const e=t();return{theme:e.getThemeLabel(),language:e.getLanguageLabel(),layout:e.getLayoutLabel(),fontSize:`${e.fontSize}px`,notifications:e.notifications?"开启":"关闭",autoSave:e.autoSave?"开启":"关闭",lastSaved:e.lastSaved,isDirty:e.isDirty,enabledPreferences:Object.keys(e.preferences).filter(t=>e.preferences[t]).length,debugMode:e.advancedSettings.debugMode?"开启":"关闭"}}}))),{name:"zustand-settings-storage",storage:n(()=>localStorage),partialize:e=>({theme:e.theme,language:e.language,notifications:e.notifications,autoSave:e.autoSave,fontSize:e.fontSize,layout:e.layout,preferences:e.preferences,advancedSettings:e.advancedSettings,customSettings:e.customSettings,version:e.version}),version:1,migrate:(e,t)=>(0===t&&(e.version="1.0.0"),e)}))),r=e=>e.theme,c=e=>e.language,S=e=>e.notifications,u=e=>e.autoSave,l=e=>e.fontSize,d=e=>e.layout,g=e=>e.preferences,v=e=>e.advancedSettings,f=e=>e.getThemeLabel(),m=e=>e.getLanguageLabel(),y=e=>e.getLayoutLabel(),p=e=>e.getAllSettings(),h=e=>e.getSettingsSummary(),b=e=>e.customSettings.shortcuts,D=e=>e.customSettings.colorScheme;export{r as a,h as b,S as c,u as d,l as e,d as f,g,v as h,b as i,D as j,f as k,m as l,y as m,p as n,c as s,i as u};
//# sourceMappingURL=store-settingsstore-CkjrIQl7.js.map
