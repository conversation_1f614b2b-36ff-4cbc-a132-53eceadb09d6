import { create } from 'zustand'
import { devtools, subscribeWithSelector, persist, createJSONStorage } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

// 设置store - 展示状态持久化和本地存储
export const useSettingsStore = create(
  devtools(
    persist(
      subscribeWithSelector(
        immer((set, get) => ({
          // 基础设置
          theme: 'auto', // light, dark, auto
          language: 'zh', // zh, en
          notifications: true,
          autoSave: true,
          fontSize: 14,
          layout: 'default', // default, compact, wide

          // 偏好设置
          preferences: {
            showWelcome: true,
            enableAnimations: true,
            soundEnabled: false,
            emailNotifications: true,
            pushNotifications: false,
            autoRefresh: true,
            showTooltips: true,
            compactMode: false,
          },

          // 高级设置
          advancedSettings: {
            debugMode: false,
            experimentalFeatures: false,
            performanceMode: false,
            logLevel: 'info', // debug, info, warn, error
            cacheSize: 100,
            requestTimeout: 10000,
            retryAttempts: 3,
            enableMetrics: true,
          },

          // 用户自定义设置
          customSettings: {
            shortcuts: {
              save: 'Ctrl+S',
              search: 'Ctrl+F',
              help: 'F1',
              console: 'Ctrl+`',
            },
            colors: {
              primary: '#0066cc',
              secondary: '#6c757d',
              success: '#28a745',
              warning: '#ffc107',
              danger: '#dc3545',
            },
            colorScheme: {
              primary: '#0066cc',
              secondary: '#6c757d',
              background: '#ffffff',
              surface: '#f8f9fa',
              text: '#212529',
            },
            dashboard: {
              widgets: ['stats', 'recent', 'quick-actions'],
              layout: 'grid',
              density: 'normal',
            },
          },

          // 元数据
          lastSaved: null,
          isDirty: false,
          version: '1.0.0',

          // 基础设置操作
          setTheme: (theme) => set((state) => {
            state.theme = theme
            state.isDirty = true
            state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
            if (state.autoSave) state.isDirty = false
          }),

          setLanguage: (language) => set((state) => {
            state.language = language
            state.isDirty = true
            state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
            if (state.autoSave) state.isDirty = false
          }),

          toggleNotifications: () => set((state) => {
            state.notifications = !state.notifications
            state.isDirty = true
            state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
            if (state.autoSave) state.isDirty = false
          }),

          toggleAutoSave: () => set((state) => {
            state.autoSave = !state.autoSave
            state.isDirty = true
            if (state.autoSave) {
              state.lastSaved = new Date().toISOString()
              state.isDirty = false
            }
          }),

          setFontSize: (size) => set((state) => {
            if (size >= 10 && size <= 24) {
              state.fontSize = size
              state.isDirty = true
              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
              if (state.autoSave) state.isDirty = false
            }
          }),

          setLayout: (layout) => set((state) => {
            state.layout = layout
            state.isDirty = true
            state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
            if (state.autoSave) state.isDirty = false
          }),

          // 偏好设置操作
          updatePreference: (key, value) => set((state) => {
            if (key in state.preferences) {
              state.preferences[key] = value
              state.isDirty = true
              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
              if (state.autoSave) state.isDirty = false
            }
          }),

          updatePreferences: (preferences) => set((state) => {
            Object.assign(state.preferences, preferences)
            state.isDirty = true
            state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
            if (state.autoSave) state.isDirty = false
          }),

          togglePreference: (key) => set((state) => {
            if (key in state.preferences && typeof state.preferences[key] === 'boolean') {
              state.preferences[key] = !state.preferences[key]
              state.isDirty = true
              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
              if (state.autoSave) state.isDirty = false
            }
          }),

          // 高级设置操作
          updateAdvancedSetting: (key, value) => set((state) => {
            if (key in state.advancedSettings) {
              state.advancedSettings[key] = value
              state.isDirty = true
              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
              if (state.autoSave) state.isDirty = false
            }
          }),

          updateAdvancedSettings: (settings) => set((state) => {
            Object.assign(state.advancedSettings, settings)
            state.isDirty = true
            state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
            if (state.autoSave) state.isDirty = false
          }),

          toggleAdvancedSetting: (key) => set((state) => {
            if (key in state.advancedSettings && typeof state.advancedSettings[key] === 'boolean') {
              state.advancedSettings[key] = !state.advancedSettings[key]
              state.isDirty = true
              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
              if (state.autoSave) state.isDirty = false
            }
          }),

          // 自定义设置操作
          updateCustomSetting: (category, key, value) => set((state) => {
            if (category in state.customSettings && key in state.customSettings[category]) {
              state.customSettings[category][key] = value
              state.isDirty = true
              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
              if (state.autoSave) state.isDirty = false
            }
          }),

          updateShortcut: (action, shortcut) => set((state) => {
            if (action in state.customSettings.shortcuts) {
              state.customSettings.shortcuts[action] = shortcut
              state.isDirty = true
              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
              if (state.autoSave) state.isDirty = false
            }
          }),

          updateColor: (colorName, color) => set((state) => {
            if (colorName in state.customSettings.colors) {
              state.customSettings.colors[colorName] = color
              state.isDirty = true
              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
              if (state.autoSave) state.isDirty = false
            }
            if (colorName in state.customSettings.colorScheme) {
              state.customSettings.colorScheme[colorName] = color
              state.isDirty = true
              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
              if (state.autoSave) state.isDirty = false
            }
          }),

          // 批量操作
          batchUpdate: (updates) => set((state) => {
            // 基础设置
            if (updates.theme) state.theme = updates.theme
            if (updates.language) state.language = updates.language
            if (updates.fontSize) state.fontSize = updates.fontSize
            if (updates.layout) state.layout = updates.layout
            if (typeof updates.notifications === 'boolean') state.notifications = updates.notifications
            if (typeof updates.autoSave === 'boolean') state.autoSave = updates.autoSave

            // 偏好设置
            if (updates.preferences) {
              Object.assign(state.preferences, updates.preferences)
            }

            // 高级设置
            if (updates.advancedSettings) {
              Object.assign(state.advancedSettings, updates.advancedSettings)
            }

            // 自定义设置
            if (updates.customSettings) {
              Object.assign(state.customSettings, updates.customSettings)
            }

            state.isDirty = true
            state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved
            if (state.autoSave) state.isDirty = false
          }),

          // 预设配置
          applyPreset: (presetName) => set(() => {
            const presets = {
              developer: {
                theme: 'dark',
                fontSize: 14,
                layout: 'compact',
                preferences: {
                  enableAnimations: false,
                  showWelcome: false,
                  soundEnabled: false,
                  compactMode: true,
                },
                advancedSettings: {
                  debugMode: true,
                  experimentalFeatures: true,
                  performanceMode: true,
                  logLevel: 'debug',
                  enableMetrics: true,
                }
              },
              designer: {
                theme: 'light',
                fontSize: 16,
                layout: 'wide',
                preferences: {
                  enableAnimations: true,
                  showWelcome: true,
                  soundEnabled: true,
                  compactMode: false,
                },
                advancedSettings: {
                  debugMode: false,
                  experimentalFeatures: false,
                  performanceMode: false,
                  logLevel: 'info',
                  enableMetrics: false,
                }
              },
              minimal: {
                theme: 'auto',
                fontSize: 14,
                layout: 'default',
                preferences: {
                  enableAnimations: false,
                  showWelcome: false,
                  soundEnabled: false,
                  compactMode: true,
                },
                advancedSettings: {
                  debugMode: false,
                  experimentalFeatures: false,
                  performanceMode: true,
                  logLevel: 'warn',
                  enableMetrics: false,
                }
              },
              accessibility: {
                theme: 'auto',
                fontSize: 18,
                layout: 'wide',
                preferences: {
                  enableAnimations: false,
                  showWelcome: true,
                  soundEnabled: true,
                  compactMode: false,
                },
                advancedSettings: {
                  debugMode: false,
                  experimentalFeatures: false,
                  performanceMode: false,
                  logLevel: 'info',
                  enableMetrics: false,
                }
              }
            }

            const preset = presets[presetName]
            if (preset) {
              get().batchUpdate(preset)
            }
          }),

          // 导入导出
          exportSettings: () => {
            const state = get()
            return {
              theme: state.theme,
              language: state.language,
              notifications: state.notifications,
              autoSave: state.autoSave,
              fontSize: state.fontSize,
              layout: state.layout,
              preferences: state.preferences,
              advancedSettings: state.advancedSettings,
              customSettings: state.customSettings,
              exportedAt: new Date().toISOString(),
              version: state.version,
            }
          },

          importSettings: (importedSettings) => set((state) => {
            try {
              // 验证并导入设置
              if (importedSettings.theme && ['light', 'dark', 'auto'].includes(importedSettings.theme)) {
                state.theme = importedSettings.theme
              }
              
              if (importedSettings.language && ['zh', 'en'].includes(importedSettings.language)) {
                state.language = importedSettings.language
              }
              
              if (typeof importedSettings.notifications === 'boolean') {
                state.notifications = importedSettings.notifications
              }
              
              if (typeof importedSettings.autoSave === 'boolean') {
                state.autoSave = importedSettings.autoSave
              }
              
              if (importedSettings.fontSize && importedSettings.fontSize >= 10 && importedSettings.fontSize <= 24) {
                state.fontSize = importedSettings.fontSize
              }
              
              if (importedSettings.layout && ['default', 'compact', 'wide'].includes(importedSettings.layout)) {
                state.layout = importedSettings.layout
              }
              
              if (importedSettings.preferences && typeof importedSettings.preferences === 'object') {
                Object.assign(state.preferences, importedSettings.preferences)
              }
              
              if (importedSettings.advancedSettings && typeof importedSettings.advancedSettings === 'object') {
                Object.assign(state.advancedSettings, importedSettings.advancedSettings)
              }
              
              if (importedSettings.customSettings && typeof importedSettings.customSettings === 'object') {
                Object.assign(state.customSettings, importedSettings.customSettings)
              }
              
              state.lastSaved = new Date().toISOString()
              state.isDirty = false
            } catch (error) {
              console.error('导入设置失败:', error)
              throw new Error('导入设置失败：文件格式错误')
            }
          }),

          // 手动保存
          saveSettings: () => set((state) => {
            state.lastSaved = new Date().toISOString()
            state.isDirty = false
          }),

          // 重置为默认设置
          resetToDefaults: () => set((state) => {
            const autoSave = state.autoSave // 保留自动保存设置
            
            // 重置基础设置
            state.theme = 'auto'
            state.language = 'zh'
            state.notifications = true
            state.fontSize = 14
            state.layout = 'default'
            
            // 重置偏好设置
            state.preferences = {
              showWelcome: true,
              enableAnimations: true,
              soundEnabled: false,
              emailNotifications: true,
              pushNotifications: false,
              autoRefresh: true,
              showTooltips: true,
              compactMode: false,
            }
            
            // 重置高级设置
            state.advancedSettings = {
              debugMode: false,
              experimentalFeatures: false,
              performanceMode: false,
              logLevel: 'info',
              cacheSize: 100,
              requestTimeout: 10000,
              retryAttempts: 3,
              enableMetrics: true,
            }
            
            // 重置自定义设置
            state.customSettings = {
              shortcuts: {
                save: 'Ctrl+S',
                search: 'Ctrl+F',
                help: 'F1',
                console: 'Ctrl+`',
              },
              colors: {
                primary: '#0066cc',
                secondary: '#6c757d',
                success: '#28a745',
                warning: '#ffc107',
                danger: '#dc3545',
              },
              dashboard: {
                widgets: ['stats', 'recent', 'quick-actions'],
                layout: 'grid',
                density: 'normal',
              },
            }
            
            state.autoSave = autoSave
            state.lastSaved = new Date().toISOString()
            state.isDirty = false
          }),

          // 重置（包括自动保存）
          reset: () => set((state) => {
            state.theme = 'auto'
            state.language = 'zh'
            state.notifications = true
            state.autoSave = true
            state.fontSize = 14
            state.layout = 'default'
            state.preferences = {
              showWelcome: true,
              enableAnimations: true,
              soundEnabled: false,
              emailNotifications: true,
              pushNotifications: false,
              autoRefresh: true,
              showTooltips: true,
              compactMode: false,
            }
            state.advancedSettings = {
              debugMode: false,
              experimentalFeatures: false,
              performanceMode: false,
              logLevel: 'info',
              cacheSize: 100,
              requestTimeout: 10000,
              retryAttempts: 3,
              enableMetrics: true,
            }
            state.customSettings = {
              shortcuts: {
                save: 'Ctrl+S',
                search: 'Ctrl+F',
                help: 'F1',
                console: 'Ctrl+`',
              },
              colors: {
                primary: '#0066cc',
                secondary: '#6c757d',
                success: '#28a745',
                warning: '#ffc107',
                danger: '#dc3545',
              },
              dashboard: {
                widgets: ['stats', 'recent', 'quick-actions'],
                layout: 'grid',
                density: 'normal',
              },
            }
            state.lastSaved = null
            state.isDirty = false
            state.version = '1.0.0'
          }),

          // 计算属性
          getThemeLabel: () => {
            const theme = get().theme
            const labels = { light: '浅色', dark: '深色', auto: '自动' }
            return labels[theme] || theme
          },

          getLanguageLabel: () => {
            const language = get().language
            const labels = { zh: '中文', en: 'English' }
            return labels[language] || language
          },

          getLayoutLabel: () => {
            const layout = get().layout
            const labels = { default: '默认', compact: '紧凑', wide: '宽屏' }
            return labels[layout] || layout
          },

          getAllSettings: () => {
            const state = get()
            return {
              theme: state.theme,
              language: state.language,
              notifications: state.notifications,
              autoSave: state.autoSave,
              fontSize: state.fontSize,
              layout: state.layout,
              preferences: state.preferences,
              advancedSettings: state.advancedSettings,
              customSettings: state.customSettings,
            }
          },

          getSettingsSummary: () => {
            const state = get()
            return {
              theme: state.getThemeLabel(),
              language: state.getLanguageLabel(),
              layout: state.getLayoutLabel(),
              fontSize: `${state.fontSize}px`,
              notifications: state.notifications ? '开启' : '关闭',
              autoSave: state.autoSave ? '开启' : '关闭',
              lastSaved: state.lastSaved,
              isDirty: state.isDirty,
              enabledPreferences: Object.keys(state.preferences).filter(key => state.preferences[key]).length,
              debugMode: state.advancedSettings.debugMode ? '开启' : '关闭',
            }
          }
        })),
        { name: 'settings-store' }
      ),
      {
        name: 'zustand-settings-storage',
        storage: createJSONStorage(() => localStorage),
        partialize: (state) => ({
          theme: state.theme,
          language: state.language,
          notifications: state.notifications,
          autoSave: state.autoSave,
          fontSize: state.fontSize,
          layout: state.layout,
          preferences: state.preferences,
          advancedSettings: state.advancedSettings,
          customSettings: state.customSettings,
          version: state.version,
        }),
        version: 1,
        migrate: (persistedState, version) => {
          // 处理版本迁移
          if (version === 0) {
            // 从旧版本迁移
            persistedState.version = '1.0.0'
          }
          return persistedState
        },
      }
    )
  )
)

// Selectors
export const selectTheme = (state) => state.theme
export const selectLanguage = (state) => state.language
export const selectNotifications = (state) => state.notifications
export const selectAutoSave = (state) => state.autoSave
export const selectFontSize = (state) => state.fontSize
export const selectLayout = (state) => state.layout
export const selectPreferences = (state) => state.preferences
export const selectAdvancedSettings = (state) => state.advancedSettings
export const selectCustomSettings = (state) => state.customSettings
export const selectLastSaved = (state) => state.lastSaved
export const selectIsDirty = (state) => state.isDirty

// 标签选择器
export const selectThemeLabel = (state) => {
  const labels = { light: '浅色', dark: '深色', auto: '跟随系统' }
  return labels[state.theme] || '未知'
}
export const selectLanguageLabel = (state) => {
  const labels = { 'zh-CN': '简体中文', 'en-US': 'English', 'ja-JP': '日本語' }
  return labels[state.language] || '未知'
}
export const selectLayoutLabel = (state) => {
  const labels = { compact: '紧凑', normal: '标准', comfortable: '舒适' }
  return labels[state.layout] || '未知'
}

// 复合选择器
export const selectAllSettings = (state) => ({
  theme: state.theme,
  language: state.language,
  fontSize: state.fontSize,
  layout: state.layout,
  autoSave: state.autoSave,
  notifications: state.notifications,
  preferences: state.preferences,
  advancedSettings: state.advancedSettings,
  customSettings: state.customSettings
})
export const selectSettingsSummary = (state) => ({
  theme: state.theme,
  language: state.language,
  totalPreferences: Object.keys(state.preferences).length,
  enabledPreferences: Object.values(state.preferences).filter(Boolean).length,
  lastSaved: state.lastSaved,
  isDirty: state.isDirty
})

// 特定偏好选择器
export const selectShowWelcome = (state) => state.preferences.showWelcome
export const selectEnableAnimations = (state) => state.preferences.enableAnimations
export const selectSoundEnabled = (state) => state.preferences.soundEnabled

// 特定高级设置选择器
export const selectDebugMode = (state) => state.advancedSettings.debugMode
export const selectExperimentalFeatures = (state) => state.advancedSettings.experimentalFeatures
export const selectPerformanceMode = (state) => state.advancedSettings.performanceMode
export const selectLogLevel = (state) => state.advancedSettings.logLevel

// 自定义设置选择器
export const selectShortcuts = (state) => state.customSettings.shortcuts
export const selectColors = (state) => state.customSettings.colors
export const selectColorScheme = (state) => state.customSettings.colorScheme
export const selectDashboard = (state) => state.customSettings.dashboard

// 订阅示例
export const subscribeToTheme = (callback) => {
  return useSettingsStore.subscribe(
    (state) => state.theme,
    callback
  )
}

export const subscribeToLanguage = (callback) => {
  return useSettingsStore.subscribe(
    (state) => state.language,
    callback
  )
}

export const subscribeToSettingsChange = (callback) => {
  return useSettingsStore.subscribe(
    (state) => [state.theme, state.language, state.fontSize, state.layout],
    callback
  )
} 