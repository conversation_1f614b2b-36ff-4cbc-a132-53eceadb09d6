import{j as s,L as e}from"./react-vendor-CyNirxNk.js";import{u as t,b as c}from"./store-basicstore-DgVIqOAD.js";import{u as i,b as a}from"./store-todosstore-DE5vS_Wk.js";import{u as l,a as r}from"./store-apistore-B0ecmVeH.js";import{u as n,b as d}from"./store-settingsstore-CkjrIQl7.js";function o(){const o=t(c),m=i(a),x=l(r),h=n(d);return s.jsxs("div",{className:"home-page",children:[s.jsx("section",{className:"hero-section mb-xl",children:s.jsxs("div",{className:"card text-center",children:[s.jsx("h1",{className:"text-2xl font-bold mb-md",children:"🐻 React + Zustand 全方向API演示"}),s.jsx("p",{className:"text-lg text-secondary mb-lg",children:"一个全面展示 Zustand 状态管理功能的完整演示应用，从基础用法到高级特性的全方位学习资源"}),s.jsxs("div",{className:"flex justify-center gap-md",children:[s.jsx(e,{to:"/basic-store",className:"btn btn-primary btn-lg",children:"🚀 开始探索"}),s.jsx("a",{href:"https://github.com/pmndrs/zustand",target:"_blank",rel:"noopener noreferrer",className:"btn btn-outline btn-lg",children:"📚 Zustand文档"})]})]})}),s.jsxs("section",{className:"stats-section mb-xl",children:[s.jsx("h2",{className:"text-xl font-semibold mb-lg",children:"📊 实时状态统计"}),s.jsxs("div",{className:"grid grid-4",children:[s.jsxs("div",{className:"card text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-primary mb-sm",children:o.totalActions}),s.jsx("div",{className:"text-sm text-secondary",children:"基础操作次数"}),s.jsxs("div",{className:"text-xs text-muted mt-sm",children:["最后操作: ",o.lastAction]})]}),s.jsxs("div",{className:"card text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-success mb-sm",children:m.total}),s.jsx("div",{className:"text-sm text-secondary",children:"待办事项总数"}),s.jsxs("div",{className:"text-xs text-muted mt-sm",children:["完成率: ",m.completionRate,"%"]})]}),s.jsxs("div",{className:"card text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-warning mb-sm",children:x.totalRequests}),s.jsx("div",{className:"text-sm text-secondary",children:"API请求次数"}),s.jsxs("div",{className:"text-xs text-muted mt-sm",children:["缓存大小: ",x.cacheSize]})]}),s.jsxs("div",{className:"card text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-info mb-sm",children:h.theme}),s.jsx("div",{className:"text-sm text-secondary",children:"当前主题"}),s.jsxs("div",{className:"text-xs text-muted mt-sm",children:["语言: ",h.language]})]})]})]}),s.jsxs("section",{className:"features-section mb-xl",children:[s.jsx("h2",{className:"text-xl font-semibold mb-lg",children:"✨ Zustand 核心特性"}),s.jsx("div",{className:"grid grid-3",children:[{icon:"🐻",title:"Zustand",description:"轻量级的状态管理库，无样板代码，TypeScript友好",highlights:["简单易用","性能优秀","无样板代码"]},{icon:"🔄",title:"状态订阅",description:"细粒度的状态订阅，支持选择器和中间件",highlights:["选择器优化","中间件支持","订阅管理"]},{icon:"🎯",title:"不可变更新",description:"使用Immer中间件实现直观的不可变状态更新",highlights:["Immer集成","直观语法","性能优化"]},{icon:"🔧",title:"中间件系统",description:"丰富的中间件生态，支持devtools、持久化等功能",highlights:["DevTools","状态持久化","自定义中间件"]},{icon:"💾",title:"状态持久化",description:"自动状态持久化，支持localStorage、sessionStorage等",highlights:["自动保存","选择性持久化","版本迁移"]},{icon:"🌐",title:"异步处理",description:"原生支持异步操作，无需额外的异步处理库",highlights:["原生异步","错误处理","加载状态"]}].map((e,t)=>s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"flex items-start gap-md mb-md",children:[s.jsx("span",{className:"text-2xl",children:e.icon}),s.jsxs("div",{children:[s.jsx("h3",{className:"font-semibold mb-sm",style:{color:"var(--color-primary)"},children:e.title}),s.jsx("p",{className:"text-sm text-secondary mb-sm",children:e.description})]})]}),s.jsx("div",{className:"flex gap-xs flex-wrap",children:e.highlights.map((e,t)=>s.jsx("span",{className:"badge badge-primary",children:e},t))})]},t))})]}),s.jsxs("section",{className:"examples-section mb-xl",children:[s.jsx("h2",{className:"text-xl font-semibold mb-lg",children:"🎯 功能演示页面"}),s.jsx("div",{className:"grid grid-2",children:[{title:"基础Store演示",path:"/basic-store",icon:"📦",description:"计数器、状态管理、异步操作等基础功能",color:"var(--color-success)"},{title:"Todos管理",path:"/todos-management",icon:"📝",description:"复杂的列表管理、过滤、批量操作",color:"var(--color-info)"},{title:"API集成",path:"/api-integration",icon:"🌐",description:"与JSONPlaceholder API的完整集成示例",color:"var(--color-warning)"},{title:"设置演示",path:"/settings-demo",icon:"⚙️",description:"复杂设置管理、持久化、导入导出",color:"var(--color-primary)"},{title:"中间件演示",path:"/middleware-demo",icon:"🔧",description:"自定义中间件、状态监控、日志记录",color:"var(--color-danger)"},{title:"高级特性",path:"/advanced-features",icon:"🚀",description:"性能优化、状态派生、订阅模式",color:"#9c27b0"}].map((t,c)=>s.jsxs(e,{to:t.path,className:"card",style:{textDecoration:"none",color:"inherit"},children:[s.jsxs("div",{className:"flex items-start gap-md mb-md",children:[s.jsx("span",{className:"text-2xl p-sm",style:{backgroundColor:t.color,color:"white",borderRadius:"var(--border-radius-md)"},children:t.icon}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h3",{className:"font-semibold mb-sm",children:t.title}),s.jsx("p",{className:"text-sm text-secondary",children:t.description})]})]}),s.jsx("div",{className:"flex justify-end",children:s.jsx("span",{className:"text-sm text-primary",children:"探索 →"})})]},c))})]}),s.jsxs("section",{className:"tech-stack-section mb-xl",children:[s.jsx("h2",{className:"text-xl font-semibold mb-lg",children:"🛠️ 技术栈"}),s.jsx("div",{className:"grid grid-3",children:[{name:"React",version:"19.1.0",icon:"⚛️",description:"前端框架"},{name:"Zustand",version:"5.0.6",icon:"🐻",description:"状态管理"},{name:"React Router",version:"7.7.0",icon:"🛣️",description:"路由管理"},{name:"Axios",version:"1.10.0",icon:"🌐",description:"HTTP客户端"},{name:"Immer",version:"10.1.1",icon:"🔄",description:"不可变更新"},{name:"Vite",version:"7.0.5",icon:"⚡",description:"构建工具"}].map((e,t)=>s.jsxs("div",{className:"card text-center",children:[s.jsx("div",{className:"text-2xl mb-sm",children:e.icon}),s.jsx("h3",{className:"font-semibold mb-xs",children:e.name}),s.jsxs("div",{className:"text-sm text-secondary mb-xs",children:["v",e.version]}),s.jsx("div",{className:"text-xs text-muted",children:e.description})]},t))})]}),s.jsxs("section",{className:"concepts-section mb-xl",children:[s.jsx("h2",{className:"text-xl font-semibold mb-lg",children:"📖 Zustand 核心概念"}),s.jsxs("div",{className:"grid grid-2",children:[s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"font-semibold mb-md",style:{color:"var(--color-success)"},children:"🏪 Store（状态存储）"}),s.jsx("p",{className:"text-sm text-secondary mb-sm",children:"使用create函数创建状态存储，支持状态和行为的统一管理"}),s.jsx("div",{className:"code-block text-sm",children:"const useStore = create((set) => ({\n  count: 0,\n  increment: () => set((state) => ({ \n    count: state.count + 1 \n  })),\n}))"})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"font-semibold mb-md",style:{color:"var(--color-warning)"},children:"🎯 Selectors（选择器）"}),s.jsx("p",{className:"text-sm text-secondary mb-sm",children:"高效的状态选择，自动订阅变化，支持计算属性"}),s.jsx("div",{className:"code-block text-sm",children:"// 基础选择\nconst count = useStore(state => state.count)\n\n// 计算属性\nconst doubleCount = useStore(state => state.count * 2)"})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"font-semibold mb-md",style:{color:"var(--color-info)"},children:"🔄 Immer Integration"}),s.jsx("p",{className:"text-sm text-secondary mb-sm",children:"使用Immer中间件实现直观的不可变更新语法"}),s.jsx("div",{className:"code-block text-sm",children:"const useStore = create(immer((set) => ({\n  todos: [],\n  addTodo: (text) => set((state) => {\n    state.todos.push({ id: Date.now(), text })\n  })\n})))"})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"font-semibold mb-md",style:{color:"var(--color-danger)"},children:"🔧 Middleware（中间件）"}),s.jsx("p",{className:"text-sm text-secondary mb-sm",children:"丰富的中间件系统，支持devtools、持久化、订阅等功能"}),s.jsx("div",{className:"code-block text-sm",children:"const useStore = create(\n  devtools(\n    persist(\n      subscribeWithSelector(/* store */),\n      { name: 'my-store' }\n    )\n  )\n)"})]})]})]}),s.jsx("section",{className:"quick-start-section",children:s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-semibold mb-lg",children:"🚀 快速开始"}),s.jsxs("div",{className:"grid grid-2",children:[s.jsxs("div",{children:[s.jsx("h3",{className:"font-semibold mb-md",children:"📖 学习路径"}),s.jsxs("ol",{className:"text-sm text-secondary space-y-1",children:[s.jsxs("li",{children:["1. 访问 ",s.jsx(e,{to:"/basic-store",className:"text-primary",children:"基础Store"})," 了解基本概念"]}),s.jsxs("li",{children:["2. 探索 ",s.jsx(e,{to:"/todos-management",className:"text-primary",children:"Todos管理"})," 学习复杂状态"]}),s.jsxs("li",{children:["3. 查看 ",s.jsx(e,{to:"/api-integration",className:"text-primary",children:"API集成"})," 掌握异步操作"]}),s.jsxs("li",{children:["4. 体验 ",s.jsx(e,{to:"/settings-demo",className:"text-primary",children:"设置演示"})," 了解持久化"]}),s.jsxs("li",{children:["5. 学习 ",s.jsx(e,{to:"/advanced-features",className:"text-primary",children:"高级特性"})," 提升技能"]})]})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"font-semibold mb-md",children:"🎯 核心优势"}),s.jsxs("ul",{className:"text-sm text-secondary space-y-1",children:[s.jsxs("li",{children:["• ",s.jsx("strong",{children:"轻量级"}),"：只有2.6kb（gzipped）"]}),s.jsxs("li",{children:["• ",s.jsx("strong",{children:"无样板代码"}),"：简洁的API设计"]}),s.jsxs("li",{children:["• ",s.jsx("strong",{children:"TypeScript支持"}),"：完整的类型推断"]}),s.jsxs("li",{children:["• ",s.jsx("strong",{children:"性能优秀"}),"：精确的重新渲染"]}),s.jsxs("li",{children:["• ",s.jsx("strong",{children:"中间件丰富"}),"：强大的扩展能力"]})]})]})]})]})})]})}export{o as H};
//# sourceMappingURL=page-home-5NslTj78.js.map
