<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="React + Zustand 全方向API演示项目，展示Zustand状态管理库的核心功能和最佳实践" />
    <meta name="keywords" content="React, Zustand, 状态管理, JavaScript, 前端开发" />
    <title>React + Zustand 全方向API演示</title>
    <script type="module" crossorigin src="/assets/index-BjLTm0a8.js"></script>
    <link rel="modulepreload" crossorigin href="/chunks/react-vendor-CyNirxNk.js">
    <link rel="modulepreload" crossorigin href="/chunks/store-settingsstore-CkjrIQl7.js">
    <link rel="modulepreload" crossorigin href="/chunks/store-basicstore-DgVIqOAD.js">
    <link rel="modulepreload" crossorigin href="/chunks/store-todosstore-DE5vS_Wk.js">
    <link rel="modulepreload" crossorigin href="/chunks/store-apistore-B0ecmVeH.js">
    <link rel="modulepreload" crossorigin href="/chunks/page-home-5NslTj78.js">
    <link rel="modulepreload" crossorigin href="/chunks/page-basicstore-ECzgDzuM.js">
    <link rel="modulepreload" crossorigin href="/chunks/page-todosmanagement-DW3Rbh2M.js">
    <link rel="modulepreload" crossorigin href="/chunks/page-apiintegration-CR3Uz1sg.js">
    <link rel="modulepreload" crossorigin href="/chunks/page-settingsdemo-B25qVxJ9.js">
    <link rel="modulepreload" crossorigin href="/chunks/page-middlewaredemo-CV2OZ4zC.js">
    <link rel="modulepreload" crossorigin href="/chunks/page-advancedfeatures-BaD2xKca.js">
    <link rel="stylesheet" crossorigin href="/styles/index-BTnFGSb7.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
