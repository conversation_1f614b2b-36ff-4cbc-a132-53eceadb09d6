{"version": 3, "file": "store-apistore-B0ecmVeH.js", "sources": ["../../src/stores/apiStore.js"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools, subscribeWithSelector } from 'zustand/middleware'\r\nimport { immer } from 'zustand/middleware/immer'\r\nimport axios from 'axios'\r\n\r\n// 创建axios实例\r\nconst api = axios.create({\r\n  baseURL: 'https://jsonplaceholder.typicode.com',\r\n  timeout: 10000,\r\n})\r\n\r\n// 请求拦截器\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    console.log('🚀 发送请求:', config.method.toUpperCase(), config.url)\r\n    return config\r\n  },\r\n  (error) => {\r\n    console.error('❌ 请求配置错误:', error)\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// 响应拦截器\r\napi.interceptors.response.use(\r\n  (response) => {\r\n    console.log('✅ 响应成功:', response.status, response.config.url)\r\n    return response\r\n  },\r\n  (error) => {\r\n    console.error('❌ 响应错误:', error.response?.status, error.message)\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// API store - 展示异步操作和HTTP请求管理\r\nexport const useApiStore = create(\r\n  devtools(\r\n    subscribeWithSelector(\r\n      immer((set, get) => ({\r\n        // 状态\r\n        users: [],\r\n        currentUser: null,\r\n        posts: [],\r\n        comments: [],\r\n        isLoading: false,\r\n        loadingStates: {}, // 分别跟踪不同操作的加载状态\r\n        errors: {},\r\n        requestLogs: [],\r\n        cache: {}, // 简单的请求缓存\r\n\r\n        // 工具方法\r\n        addRequestLog: (log) => set((state) => {\r\n          state.requestLogs.unshift({\r\n            id: Date.now(),\r\n            timestamp: new Date().toISOString(),\r\n            ...log\r\n          })\r\n          // 保持最近100条日志\r\n          if (state.requestLogs.length > 100) {\r\n            state.requestLogs = state.requestLogs.slice(0, 100)\r\n          }\r\n        }),\r\n\r\n        setLoading: (operation, loading) => set((state) => {\r\n          state.loadingStates[operation] = loading\r\n          // 更新全局loading状态\r\n          state.isLoading = Object.values(state.loadingStates).some(Boolean)\r\n        }),\r\n\r\n        setError: (operation, error) => set((state) => {\r\n          if (error) {\r\n            state.errors[operation] = error\r\n          } else {\r\n            delete state.errors[operation]\r\n          }\r\n        }),\r\n\r\n        // 获取用户列表\r\n        fetchUsers: async () => {\r\n          const cacheKey = 'users'\r\n          const cached = get().cache[cacheKey]\r\n          \r\n          // 检查缓存（5分钟有效期）\r\n          if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {\r\n            set((state) => {\r\n              state.users = cached.data\r\n            })\r\n            get().addRequestLog({\r\n              method: 'GET',\r\n              url: '/users',\r\n              status: 'cached',\r\n              message: '从缓存获取用户列表'\r\n            })\r\n            return cached.data\r\n          }\r\n\r\n          get().setLoading('fetchUsers', true)\r\n          get().setError('fetchUsers', null)\r\n\r\n          try {\r\n            const response = await api.get('/users')\r\n            const users = response.data\r\n\r\n            set((state) => {\r\n              state.users = users\r\n              state.cache[cacheKey] = {\r\n                data: users,\r\n                timestamp: Date.now()\r\n              }\r\n            })\r\n\r\n            get().addRequestLog({\r\n              method: 'GET',\r\n              url: '/users',\r\n              status: response.status,\r\n              message: `成功获取${users.length}个用户`\r\n            })\r\n\r\n            return users\r\n          } catch (error) {\r\n            const errorMessage = error.response?.data?.message || error.message || '获取用户列表失败'\r\n            \r\n            get().setError('fetchUsers', errorMessage)\r\n            get().addRequestLog({\r\n              method: 'GET',\r\n              url: '/users',\r\n              status: error.response?.status || 'error',\r\n              message: errorMessage\r\n            })\r\n            \r\n            throw error\r\n          } finally {\r\n            get().setLoading('fetchUsers', false)\r\n          }\r\n        },\r\n\r\n        // 获取单个用户\r\n        fetchUserById: async (id) => {\r\n          const cacheKey = `user-${id}`\r\n          const cached = get().cache[cacheKey]\r\n          \r\n          if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {\r\n            set((state) => {\r\n              state.currentUser = cached.data\r\n            })\r\n            get().addRequestLog({\r\n              method: 'GET',\r\n              url: `/users/${id}`,\r\n              status: 'cached',\r\n              message: '从缓存获取用户详情'\r\n            })\r\n            return cached.data\r\n          }\r\n\r\n          get().setLoading('fetchUser', true)\r\n          get().setError('fetchUser', null)\r\n\r\n          try {\r\n            const response = await api.get(`/users/${id}`)\r\n            const user = response.data\r\n\r\n            set((state) => {\r\n              state.currentUser = user\r\n              state.cache[cacheKey] = {\r\n                data: user,\r\n                timestamp: Date.now()\r\n              }\r\n            })\r\n\r\n            get().addRequestLog({\r\n              method: 'GET',\r\n              url: `/users/${id}`,\r\n              status: response.status,\r\n              message: `成功获取用户: ${user.name}`\r\n            })\r\n\r\n            return user\r\n          } catch (error) {\r\n            const errorMessage = error.response?.status === 404 \r\n              ? '用户不存在' \r\n              : error.response?.data?.message || error.message || '获取用户详情失败'\r\n            \r\n            get().setError('fetchUser', errorMessage)\r\n            get().addRequestLog({\r\n              method: 'GET',\r\n              url: `/users/${id}`,\r\n              status: error.response?.status || 'error',\r\n              message: errorMessage\r\n            })\r\n            \r\n            throw error\r\n          } finally {\r\n            get().setLoading('fetchUser', false)\r\n          }\r\n        },\r\n\r\n        // 创建用户\r\n        createUser: async (userData) => {\r\n          get().setLoading('createUser', true)\r\n          get().setError('createUser', null)\r\n\r\n          try {\r\n            const response = await api.post('/users', userData)\r\n            const newUser = { ...response.data, id: Date.now() } // 模拟真实ID\r\n\r\n            set((state) => {\r\n              state.users.unshift(newUser)\r\n            })\r\n\r\n            get().addRequestLog({\r\n              method: 'POST',\r\n              url: '/users',\r\n              status: response.status,\r\n              message: `成功创建用户: ${newUser.name}`\r\n            })\r\n\r\n            return newUser\r\n          } catch (error) {\r\n            const errorMessage = error.response?.data?.message || error.message || '创建用户失败'\r\n            \r\n            get().setError('createUser', errorMessage)\r\n            get().addRequestLog({\r\n              method: 'POST',\r\n              url: '/users',\r\n              status: error.response?.status || 'error',\r\n              message: errorMessage\r\n            })\r\n            \r\n            throw error\r\n          } finally {\r\n            get().setLoading('createUser', false)\r\n          }\r\n        },\r\n\r\n        // 更新用户\r\n        updateUser: async (id, userData) => {\r\n          get().setLoading('updateUser', true)\r\n          get().setError('updateUser', null)\r\n\r\n          try {\r\n            const response = await api.put(`/users/${id}`, userData)\r\n            const updatedUser = response.data\r\n\r\n            set((state) => {\r\n              const index = state.users.findIndex(user => user.id === id)\r\n              if (index > -1) {\r\n                state.users[index] = { ...state.users[index], ...updatedUser }\r\n              }\r\n              \r\n              if (state.currentUser && state.currentUser.id === id) {\r\n                state.currentUser = { ...state.currentUser, ...updatedUser }\r\n              }\r\n\r\n              // 清除相关缓存\r\n              delete state.cache[`user-${id}`]\r\n            })\r\n\r\n            get().addRequestLog({\r\n              method: 'PUT',\r\n              url: `/users/${id}`,\r\n              status: response.status,\r\n              message: `成功更新用户: ${updatedUser.name || id}`\r\n            })\r\n\r\n            return updatedUser\r\n          } catch (error) {\r\n            const errorMessage = error.response?.data?.message || error.message || '更新用户失败'\r\n            \r\n            get().setError('updateUser', errorMessage)\r\n            get().addRequestLog({\r\n              method: 'PUT',\r\n              url: `/users/${id}`,\r\n              status: error.response?.status || 'error',\r\n              message: errorMessage\r\n            })\r\n            \r\n            throw error\r\n          } finally {\r\n            get().setLoading('updateUser', false)\r\n          }\r\n        },\r\n\r\n        // 删除用户\r\n        deleteUser: async (id) => {\r\n          get().setLoading('deleteUser', true)\r\n          get().setError('deleteUser', null)\r\n\r\n          try {\r\n            const response = await api.delete(`/users/${id}`)\r\n\r\n            set((state) => {\r\n              state.users = state.users.filter(user => user.id !== id)\r\n              \r\n              if (state.currentUser && state.currentUser.id === id) {\r\n                state.currentUser = null\r\n              }\r\n\r\n              // 清除相关缓存\r\n              delete state.cache[`user-${id}`]\r\n            })\r\n\r\n            get().addRequestLog({\r\n              method: 'DELETE',\r\n              url: `/users/${id}`,\r\n              status: response.status,\r\n              message: `成功删除用户: ${id}`\r\n            })\r\n\r\n            return true\r\n          } catch (error) {\r\n            const errorMessage = error.response?.data?.message || error.message || '删除用户失败'\r\n            \r\n            get().setError('deleteUser', errorMessage)\r\n            get().addRequestLog({\r\n              method: 'DELETE',\r\n              url: `/users/${id}`,\r\n              status: error.response?.status || 'error',\r\n              message: errorMessage\r\n            })\r\n            \r\n            throw error\r\n          } finally {\r\n            get().setLoading('deleteUser', false)\r\n          }\r\n        },\r\n\r\n        // 获取文章列表\r\n        fetchPosts: async (userId = null) => {\r\n          const url = userId ? `/users/${userId}/posts` : '/posts'\r\n          const cacheKey = userId ? `posts-user-${userId}` : 'posts'\r\n          const cached = get().cache[cacheKey]\r\n          \r\n          if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {\r\n            set((state) => {\r\n              state.posts = cached.data\r\n            })\r\n            get().addRequestLog({\r\n              method: 'GET',\r\n              url,\r\n              status: 'cached',\r\n              message: '从缓存获取文章列表'\r\n            })\r\n            return cached.data\r\n          }\r\n\r\n          get().setLoading('fetchPosts', true)\r\n          get().setError('fetchPosts', null)\r\n\r\n          try {\r\n            const response = await api.get(url)\r\n            const posts = response.data\r\n\r\n            set((state) => {\r\n              state.posts = posts\r\n              state.cache[cacheKey] = {\r\n                data: posts,\r\n                timestamp: Date.now()\r\n              }\r\n            })\r\n\r\n            get().addRequestLog({\r\n              method: 'GET',\r\n              url,\r\n              status: response.status,\r\n              message: `成功获取${posts.length}篇文章`\r\n            })\r\n\r\n            return posts\r\n          } catch (error) {\r\n            const errorMessage = error.response?.data?.message || error.message || '获取文章列表失败'\r\n            \r\n            get().setError('fetchPosts', errorMessage)\r\n            get().addRequestLog({\r\n              method: 'GET',\r\n              url,\r\n              status: error.response?.status || 'error',\r\n              message: errorMessage\r\n            })\r\n            \r\n            throw error\r\n          } finally {\r\n            get().setLoading('fetchPosts', false)\r\n          }\r\n        },\r\n\r\n        // 获取评论\r\n        fetchComments: async (postId) => {\r\n          const url = `/posts/${postId}/comments`\r\n          const cacheKey = `comments-${postId}`\r\n          const cached = get().cache[cacheKey]\r\n          \r\n          if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {\r\n            set((state) => {\r\n              state.comments = cached.data\r\n            })\r\n            return cached.data\r\n          }\r\n\r\n          get().setLoading('fetchComments', true)\r\n          get().setError('fetchComments', null)\r\n\r\n          try {\r\n            const response = await api.get(url)\r\n            const comments = response.data\r\n\r\n            set((state) => {\r\n              state.comments = comments\r\n              state.cache[cacheKey] = {\r\n                data: comments,\r\n                timestamp: Date.now()\r\n              }\r\n            })\r\n\r\n            get().addRequestLog({\r\n              method: 'GET',\r\n              url,\r\n              status: response.status,\r\n              message: `成功获取${comments.length}条评论`\r\n            })\r\n\r\n            return comments\r\n          } catch (error) {\r\n            const errorMessage = error.response?.data?.message || error.message || '获取评论失败'\r\n            \r\n            get().setError('fetchComments', errorMessage)\r\n            get().addRequestLog({\r\n              method: 'GET',\r\n              url,\r\n              status: error.response?.status || 'error',\r\n              message: errorMessage\r\n            })\r\n            \r\n            throw error\r\n          } finally {\r\n            get().setLoading('fetchComments', false)\r\n          }\r\n        },\r\n\r\n        // 设置当前用户\r\n        setCurrentUser: (user) => set((state) => {\r\n          state.currentUser = user\r\n        }),\r\n\r\n        // 清除错误\r\n        clearError: (operation = null) => set((state) => {\r\n          if (operation) {\r\n            delete state.errors[operation]\r\n          } else {\r\n            state.errors = {}\r\n          }\r\n        }),\r\n\r\n        // 清除请求日志\r\n        clearRequestLogs: () => set((state) => {\r\n          state.requestLogs = []\r\n        }),\r\n\r\n        // 清除缓存\r\n        clearCache: () => set((state) => {\r\n          state.cache = {}\r\n        }),\r\n\r\n        // 重置store\r\n        reset: () => set((state) => {\r\n          state.users = []\r\n          state.currentUser = null\r\n          state.posts = []\r\n          state.comments = []\r\n          state.isLoading = false\r\n          state.loadingStates = {}\r\n          state.errors = {}\r\n          state.requestLogs = []\r\n          state.cache = {}\r\n        }),\r\n\r\n        // 计算属性\r\n        getStats: () => {\r\n          const state = get()\r\n          return {\r\n            totalUsers: state.users.length,\r\n            totalPosts: state.posts.length,\r\n            totalComments: state.comments.length,\r\n            totalRequests: state.requestLogs.length,\r\n            cacheSize: Object.keys(state.cache).length,\r\n            hasErrors: Object.keys(state.errors).length > 0,\r\n            isAnyLoading: state.isLoading\r\n          }\r\n        },\r\n\r\n        getErrorSummary: () => {\r\n          const errors = get().errors\r\n          return Object.keys(errors).map(operation => ({\r\n            operation,\r\n            error: errors[operation]\r\n          }))\r\n        },\r\n\r\n        getRequestsByMethod: (method) => {\r\n          return get().requestLogs.filter(log => log.method === method)\r\n        },\r\n\r\n        getSuccessfulRequests: () => {\r\n          return get().requestLogs.filter(log => \r\n            typeof log.status === 'number' && log.status >= 200 && log.status < 300\r\n          )\r\n        },\r\n\r\n        getFailedRequests: () => {\r\n          return get().requestLogs.filter(log => \r\n            log.status === 'error' || (typeof log.status === 'number' && log.status >= 400)\r\n          )\r\n        }\r\n      })),\r\n      { name: 'api-store' }\r\n    )\r\n  )\r\n)\r\n\r\n// Selectors\r\nexport const selectUsers = (state) => state.users\r\nexport const selectCurrentUser = (state) => state.currentUser\r\nexport const selectPosts = (state) => state.posts\r\nexport const selectComments = (state) => state.comments\r\nexport const selectIsLoading = (state) => state.isLoading\r\nexport const selectLoadingStates = (state) => state.loadingStates\r\nexport const selectErrors = (state) => state.errors\r\nexport const selectRequestLogs = (state) => state.requestLogs\r\nexport const selectCache = (state) => state.cache\r\nexport const selectStats = (state) => state.getStats()\r\nexport const selectErrorSummary = (state) => state.getErrorSummary()\r\n\r\n// 特定操作的加载状态选择器\r\nexport const selectIsLoadingUsers = (state) => state.loadingStates.fetchUsers || false\r\nexport const selectIsLoadingUser = (state) => state.loadingStates.fetchUser || false\r\nexport const selectIsCreatingUser = (state) => state.loadingStates.createUser || false\r\nexport const selectIsUpdatingUser = (state) => state.loadingStates.updateUser || false\r\nexport const selectIsDeletingUser = (state) => state.loadingStates.deleteUser || false\r\n\r\n// 错误选择器\r\nexport const selectUsersError = (state) => state.errors.fetchUsers\r\nexport const selectUserError = (state) => state.errors.fetchUser\r\nexport const selectCreateUserError = (state) => state.errors.createUser\r\n\r\n// 请求日志选择器\r\nexport const selectSuccessfulRequests = (state) => state.getSuccessfulRequests()\r\nexport const selectFailedRequests = (state) => state.getFailedRequests()\r\nexport const selectRecentRequests = (state) => state.requestLogs.slice(0, 10)\r\n\r\n// 订阅示例\r\nexport const subscribeToUsers = (callback) => {\r\n  return useApiStore.subscribe(\r\n    (state) => state.users,\r\n    callback\r\n  )\r\n}\r\n\r\nexport const subscribeToErrors = (callback) => {\r\n  return useApiStore.subscribe(\r\n    (state) => state.errors,\r\n    callback\r\n  )\r\n} "], "names": ["api", "axios", "create", "baseURL", "timeout", "interceptors", "request", "use", "config", "error", "Promise", "reject", "response", "useApiStore", "devtools", "subscribeWithSelector", "immer", "set", "get", "users", "currentUser", "posts", "comments", "isLoading", "loadingStates", "errors", "requestLogs", "cache", "addRequestLog", "log", "state", "unshift", "id", "Date", "now", "timestamp", "toISOString", "length", "slice", "setLoading", "operation", "loading", "Object", "values", "some", "Boolean", "setError", "fetchUsers", "async", "cache<PERSON>ey", "cached", "data", "method", "url", "status", "message", "errorMessage", "fetchUserById", "user", "name", "createUser", "userData", "post", "newUser", "updateUser", "put", "updatedUser", "index", "findIndex", "deleteUser", "delete", "filter", "fetchPosts", "userId", "fetchComments", "postId", "setCurrentUser", "clearError", "clearRequestLogs", "clearCache", "reset", "getStats", "totalUsers", "totalPosts", "totalComments", "totalRequests", "cacheSize", "keys", "hasErrors", "isAnyLoading", "getError<PERSON><PERSON><PERSON><PERSON>", "map", "getRequestsByMethod", "getSuccessfulRequests", "getFailedRequests", "selectUsers", "selectCurrentUser", "selectPosts", "selectLoadingStates", "selectErrors", "selectRequestLogs", "selectStats"], "mappings": "2EAMA,MAAMA,EAAMC,EAAMC,OAAO,CACvBC,QAAS,uCACTC,QAAS,MAIXJ,EAAIK,aAAaC,QAAQC,IACtBC,GAEQA,EAERC,GAEQC,QAAQC,OAAOF,IAK1BT,EAAIK,aAAaO,SAASL,IACvBK,GAEQA,EAERH,GAEQC,QAAQC,OAAOF,IAKd,MAACI,EAAcX,EACzBY,EACEC,EACEC,EAAM,CAACC,EAAKC,KAAA,CAEVC,MAAO,GACPC,YAAa,KACbC,MAAO,GACPC,SAAU,GACVC,WAAW,EACXC,cAAe,CAAA,EACfC,OAAQ,CAAA,EACRC,YAAa,GACbC,MAAO,CAAA,EAGPC,cAAgBC,GAAQZ,EAAKa,IAC3BA,EAAMJ,YAAYK,QAAQ,CACxBC,GAAIC,KAAKC,MACTC,WAAA,IAAeF,MAAOG,iBACnBP,IAGDC,EAAMJ,YAAYW,OAAS,MAC7BP,EAAMJ,YAAcI,EAAMJ,YAAYY,MAAM,EAAG,QAInDC,WAAY,CAACC,EAAWC,IAAYxB,EAAKa,IACvCA,EAAMN,cAAcgB,GAAaC,EAEjCX,EAAMP,UAAYmB,OAAOC,OAAOb,EAAMN,eAAeoB,KAAKC,WAG5DC,SAAU,CAACN,EAAW/B,IAAUQ,EAAKa,IAC/BrB,EACFqB,EAAML,OAAOe,GAAa/B,SAEnBqB,EAAML,OAAOe,KAKxBO,WAAYC,UACV,MAAMC,EAAW,QACXC,EAAShC,IAAMS,MAAMsB,GAG3B,GAAIC,GAAUjB,KAAKC,MAAQgB,EAAOf,UAAY,IAU5C,OATAlB,EAAKa,IACHA,EAAMX,MAAQ+B,EAAOC,OAEvBjC,IAAMU,cAAc,CAClBwB,OAAQ,MACRC,IAAK,SACLC,OAAQ,SACRC,QAAS,cAEJL,EAAOC,KAGhBjC,IAAMqB,WAAW,cAAc,GAC/BrB,IAAM4B,SAAS,aAAc,MAE7B,IACE,MAAMlC,QAAiBZ,EAAIkB,IAAI,UACzBC,EAAQP,EAASuC,KAiBvB,OAfAlC,EAAKa,IACHA,EAAMX,MAAQA,EACdW,EAAMH,MAAMsB,GAAY,CACtBE,KAAMhC,EACNgB,UAAWF,KAAKC,SAIpBhB,IAAMU,cAAc,CAClBwB,OAAQ,MACRC,IAAK,SACLC,OAAQ1C,EAAS0C,OACjBC,QAAS,OAAOpC,EAAMkB,cAGjBlB,CACT,OAASV,GACP,MAAM+C,EAAe/C,EAAMG,UAAUuC,MAAMI,SAAW9C,EAAM8C,SAAW,WAUvE,MARArC,IAAM4B,SAAS,aAAcU,GAC7BtC,IAAMU,cAAc,CAClBwB,OAAQ,MACRC,IAAK,SACLC,OAAQ7C,EAAMG,UAAU0C,QAAU,QAClCC,QAASC,IAGL/C,CACR,CAAA,QACES,IAAMqB,WAAW,cAAc,EACjC,GAIFkB,cAAeT,MAAOhB,IACpB,MAAMiB,EAAW,QAAQjB,IACnBkB,EAAShC,IAAMS,MAAMsB,GAE3B,GAAIC,GAAUjB,KAAKC,MAAQgB,EAAOf,UAAY,IAU5C,OATAlB,EAAKa,IACHA,EAAMV,YAAc8B,EAAOC,OAE7BjC,IAAMU,cAAc,CAClBwB,OAAQ,MACRC,IAAK,UAAUrB,IACfsB,OAAQ,SACRC,QAAS,cAEJL,EAAOC,KAGhBjC,IAAMqB,WAAW,aAAa,GAC9BrB,IAAM4B,SAAS,YAAa,MAE5B,IACE,MAAMlC,QAAiBZ,EAAIkB,IAAI,UAAUc,KACnC0B,EAAO9C,EAASuC,KAiBtB,OAfAlC,EAAKa,IACHA,EAAMV,YAAcsC,EACpB5B,EAAMH,MAAMsB,GAAY,CACtBE,KAAMO,EACNvB,UAAWF,KAAKC,SAIpBhB,IAAMU,cAAc,CAClBwB,OAAQ,MACRC,IAAK,UAAUrB,IACfsB,OAAQ1C,EAAS0C,OACjBC,QAAS,WAAWG,EAAKC,SAGpBD,CACT,OAASjD,GACP,MAAM+C,EAA0C,MAA3B/C,EAAMG,UAAU0C,OACjC,QACA7C,EAAMG,UAAUuC,MAAMI,SAAW9C,EAAM8C,SAAW,WAUtD,MARArC,IAAM4B,SAAS,YAAaU,GAC5BtC,IAAMU,cAAc,CAClBwB,OAAQ,MACRC,IAAK,UAAUrB,IACfsB,OAAQ7C,EAAMG,UAAU0C,QAAU,QAClCC,QAASC,IAGL/C,CACR,CAAA,QACES,IAAMqB,WAAW,aAAa,EAChC,GAIFqB,WAAYZ,MAAOa,IACjB3C,IAAMqB,WAAW,cAAc,GAC/BrB,IAAM4B,SAAS,aAAc,MAE7B,IACE,MAAMlC,QAAiBZ,EAAI8D,KAAK,SAAUD,GACpCE,EAAU,IAAKnD,EAASuC,KAAMnB,GAAIC,KAAKC,OAa7C,OAXAjB,EAAKa,IACHA,EAAMX,MAAMY,QAAQgC,KAGtB7C,IAAMU,cAAc,CAClBwB,OAAQ,OACRC,IAAK,SACLC,OAAQ1C,EAAS0C,OACjBC,QAAS,WAAWQ,EAAQJ,SAGvBI,CACT,OAAStD,GACP,MAAM+C,EAAe/C,EAAMG,UAAUuC,MAAMI,SAAW9C,EAAM8C,SAAW,SAUvE,MARArC,IAAM4B,SAAS,aAAcU,GAC7BtC,IAAMU,cAAc,CAClBwB,OAAQ,OACRC,IAAK,SACLC,OAAQ7C,EAAMG,UAAU0C,QAAU,QAClCC,QAASC,IAGL/C,CACR,CAAA,QACES,IAAMqB,WAAW,cAAc,EACjC,GAIFyB,WAAYhB,MAAOhB,EAAI6B,KACrB3C,IAAMqB,WAAW,cAAc,GAC/BrB,IAAM4B,SAAS,aAAc,MAE7B,IACE,MAAMlC,QAAiBZ,EAAIiE,IAAI,UAAUjC,IAAM6B,GACzCK,EAActD,EAASuC,KAuB7B,OArBAlC,EAAKa,IACH,MAAMqC,EAAQrC,EAAMX,MAAMiD,UAAUV,GAAQA,EAAK1B,KAAOA,GACpDmC,GAAQ,IACVrC,EAAMX,MAAMgD,GAAS,IAAKrC,EAAMX,MAAMgD,MAAWD,IAG/CpC,EAAMV,aAAeU,EAAMV,YAAYY,KAAOA,IAChDF,EAAMV,YAAc,IAAKU,EAAMV,eAAgB8C,WAI1CpC,EAAMH,MAAM,QAAQK,OAG7Bd,IAAMU,cAAc,CAClBwB,OAAQ,MACRC,IAAK,UAAUrB,IACfsB,OAAQ1C,EAAS0C,OACjBC,QAAS,WAAWW,EAAYP,MAAQ3B,MAGnCkC,CACT,OAASzD,GACP,MAAM+C,EAAe/C,EAAMG,UAAUuC,MAAMI,SAAW9C,EAAM8C,SAAW,SAUvE,MARArC,IAAM4B,SAAS,aAAcU,GAC7BtC,IAAMU,cAAc,CAClBwB,OAAQ,MACRC,IAAK,UAAUrB,IACfsB,OAAQ7C,EAAMG,UAAU0C,QAAU,QAClCC,QAASC,IAGL/C,CACR,CAAA,QACES,IAAMqB,WAAW,cAAc,EACjC,GAIF8B,WAAYrB,MAAOhB,IACjBd,IAAMqB,WAAW,cAAc,GAC/BrB,IAAM4B,SAAS,aAAc,MAE7B,IACE,MAAMlC,QAAiBZ,EAAIsE,OAAO,UAAUtC,KAoB5C,OAlBAf,EAAKa,IACHA,EAAMX,MAAQW,EAAMX,MAAMoD,OAAOb,GAAQA,EAAK1B,KAAOA,GAEjDF,EAAMV,aAAeU,EAAMV,YAAYY,KAAOA,IAChDF,EAAMV,YAAc,aAIfU,EAAMH,MAAM,QAAQK,OAG7Bd,IAAMU,cAAc,CAClBwB,OAAQ,SACRC,IAAK,UAAUrB,IACfsB,OAAQ1C,EAAS0C,OACjBC,QAAS,WAAWvB,OAGf,CACT,OAASvB,GACP,MAAM+C,EAAe/C,EAAMG,UAAUuC,MAAMI,SAAW9C,EAAM8C,SAAW,SAUvE,MARArC,IAAM4B,SAAS,aAAcU,GAC7BtC,IAAMU,cAAc,CAClBwB,OAAQ,SACRC,IAAK,UAAUrB,IACfsB,OAAQ7C,EAAMG,UAAU0C,QAAU,QAClCC,QAASC,IAGL/C,CACR,CAAA,QACES,IAAMqB,WAAW,cAAc,EACjC,GAIFiC,WAAYxB,MAAOyB,EAAS,QAC1B,MAAMpB,EAAMoB,EAAS,UAAUA,UAAiB,SAC1CxB,EAAWwB,EAAS,cAAcA,IAAW,QAC7CvB,EAAShC,IAAMS,MAAMsB,GAE3B,GAAIC,GAAUjB,KAAKC,MAAQgB,EAAOf,UAAY,IAU5C,OATAlB,EAAKa,IACHA,EAAMT,MAAQ6B,EAAOC,OAEvBjC,IAAMU,cAAc,CAClBwB,OAAQ,MACRC,MACAC,OAAQ,SACRC,QAAS,cAEJL,EAAOC,KAGhBjC,IAAMqB,WAAW,cAAc,GAC/BrB,IAAM4B,SAAS,aAAc,MAE7B,IACE,MAAMlC,QAAiBZ,EAAIkB,IAAImC,GACzBhC,EAAQT,EAASuC,KAiBvB,OAfAlC,EAAKa,IACHA,EAAMT,MAAQA,EACdS,EAAMH,MAAMsB,GAAY,CACtBE,KAAM9B,EACNc,UAAWF,KAAKC,SAIpBhB,IAAMU,cAAc,CAClBwB,OAAQ,MACRC,MACAC,OAAQ1C,EAAS0C,OACjBC,QAAS,OAAOlC,EAAMgB,cAGjBhB,CACT,OAASZ,GACP,MAAM+C,EAAe/C,EAAMG,UAAUuC,MAAMI,SAAW9C,EAAM8C,SAAW,WAUvE,MARArC,IAAM4B,SAAS,aAAcU,GAC7BtC,IAAMU,cAAc,CAClBwB,OAAQ,MACRC,MACAC,OAAQ7C,EAAMG,UAAU0C,QAAU,QAClCC,QAASC,IAGL/C,CACR,CAAA,QACES,IAAMqB,WAAW,cAAc,EACjC,GAIFmC,cAAe1B,MAAO2B,IACpB,MAAMtB,EAAM,UAAUsB,aAChB1B,EAAW,YAAY0B,IACvBzB,EAAShC,IAAMS,MAAMsB,GAE3B,GAAIC,GAAUjB,KAAKC,MAAQgB,EAAOf,UAAY,IAI5C,OAHAlB,EAAKa,IACHA,EAAMR,SAAW4B,EAAOC,OAEnBD,EAAOC,KAGhBjC,IAAMqB,WAAW,iBAAiB,GAClCrB,IAAM4B,SAAS,gBAAiB,MAEhC,IACE,MAAMlC,QAAiBZ,EAAIkB,IAAImC,GACzB/B,EAAWV,EAASuC,KAiB1B,OAfAlC,EAAKa,IACHA,EAAMR,SAAWA,EACjBQ,EAAMH,MAAMsB,GAAY,CACtBE,KAAM7B,EACNa,UAAWF,KAAKC,SAIpBhB,IAAMU,cAAc,CAClBwB,OAAQ,MACRC,MACAC,OAAQ1C,EAAS0C,OACjBC,QAAS,OAAOjC,EAASe,cAGpBf,CACT,OAASb,GACP,MAAM+C,EAAe/C,EAAMG,UAAUuC,MAAMI,SAAW9C,EAAM8C,SAAW,SAUvE,MARArC,IAAM4B,SAAS,gBAAiBU,GAChCtC,IAAMU,cAAc,CAClBwB,OAAQ,MACRC,MACAC,OAAQ7C,EAAMG,UAAU0C,QAAU,QAClCC,QAASC,IAGL/C,CACR,CAAA,QACES,IAAMqB,WAAW,iBAAiB,EACpC,GAIFqC,eAAiBlB,GAASzC,EAAKa,IAC7BA,EAAMV,YAAcsC,IAItBmB,WAAY,CAACrC,EAAY,OAASvB,EAAKa,IACjCU,SACKV,EAAML,OAAOe,GAEpBV,EAAML,OAAS,CAAA,IAKnBqD,iBAAkB,IAAM7D,EAAKa,IAC3BA,EAAMJ,YAAc,KAItBqD,WAAY,IAAM9D,EAAKa,IACrBA,EAAMH,MAAQ,CAAA,IAIhBqD,MAAO,IAAM/D,EAAKa,IAChBA,EAAMX,MAAQ,GACdW,EAAMV,YAAc,KACpBU,EAAMT,MAAQ,GACdS,EAAMR,SAAW,GACjBQ,EAAMP,WAAY,EAClBO,EAAMN,cAAgB,CAAA,EACtBM,EAAML,OAAS,CAAA,EACfK,EAAMJ,YAAc,GACpBI,EAAMH,MAAQ,CAAA,IAIhBsD,SAAU,KACR,MAAMnD,EAAQZ,IACd,MAAO,CACLgE,WAAYpD,EAAMX,MAAMkB,OACxB8C,WAAYrD,EAAMT,MAAMgB,OACxB+C,cAAetD,EAAMR,SAASe,OAC9BgD,cAAevD,EAAMJ,YAAYW,OACjCiD,UAAW5C,OAAO6C,KAAKzD,EAAMH,OAAOU,OACpCmD,UAAW9C,OAAO6C,KAAKzD,EAAML,QAAQY,OAAS,EAC9CoD,aAAc3D,EAAMP,YAIxBmE,gBAAiB,KACf,MAAMjE,EAASP,IAAMO,OACrB,OAAOiB,OAAO6C,KAAK9D,GAAQkE,IAAInD,IAAA,CAC7BA,YACA/B,MAAOgB,EAAOe,OAIlBoD,oBAAsBxC,GACblC,IAAMQ,YAAY6C,OAAO1C,GAAOA,EAAIuB,SAAWA,GAGxDyC,sBAAuB,IACd3E,IAAMQ,YAAY6C,OAAO1C,GACR,iBAAfA,EAAIyB,QAAuBzB,EAAIyB,QAAU,KAAOzB,EAAIyB,OAAS,KAIxEwC,kBAAmB,IACV5E,IAAMQ,YAAY6C,OAAO1C,GACf,UAAfA,EAAIyB,QAA6C,iBAAfzB,EAAIyB,QAAuBzB,EAAIyB,QAAU,WAU1EyC,EAAejE,GAAUA,EAAMX,MAC/B6E,EAAqBlE,GAAUA,EAAMV,YACrC6E,EAAenE,GAAUA,EAAMT,MAG/B6E,EAAuBpE,GAAUA,EAAMN,cACvC2E,EAAgBrE,GAAUA,EAAML,OAChC2E,EAAqBtE,GAAUA,EAAMJ,YAErC2E,EAAevE,GAAUA,EAAMmD"}