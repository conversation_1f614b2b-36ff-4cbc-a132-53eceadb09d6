{"version": 3, "file": "page-middlewaredemo-CV2OZ4zC.js", "sources": ["../../src/pages/MiddlewareDemo.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react'\r\nimport { useBasicStore, selectCount, selectName, selectItems, selectUser, selectHistory, selectError, selectIsLoading, selectStats } from '@/stores/basicStore'\r\nimport { useSettingsStore, selectTheme } from '@/stores/settingsStore'\r\nimport { useApiStore, selectRequestLogs } from '@/stores/apiStore'\r\n\r\nfunction MiddlewareDemo() {\r\n  // 使用多个store来展示中间件\r\n  const count = useBasicStore(selectCount)\r\n  const name = useBasicStore(selectName)\r\n  const items = useBasicStore(selectItems)\r\n  const user = useBasicStore(selectUser)\r\n  const history = useBasicStore(selectHistory)\r\n  const error = useBasicStore(selectError)\r\n  const asyncLoading = useBasicStore(selectIsLoading)\r\n  const stats = useBasicStore(selectStats)\r\n  \r\n  const theme = useSettingsStore(selectTheme)\r\n  const requestLogs = useApiStore(selectRequestLogs)\r\n\r\n  // 获取actions\r\n  const {\r\n    increment,\r\n    decrement,\r\n    incrementByAmount,\r\n    setName,\r\n    addItem,\r\n    simulateAsyncOperation,\r\n    clearError,\r\n    reset: resetBasic\r\n  } = useBasicStore()\r\n\r\n  const { setTheme } = useSettingsStore()\r\n  const { fetchUsers, clearRequestLogs } = useApiStore()\r\n\r\n  // 本地状态\r\n  const [subscriptions, setSubscriptions] = useState([])\r\n  const [middlewareStats, setMiddlewareStats] = useState({\r\n    devtoolsEnabled: false,\r\n    persistEnabled: false,\r\n    subscribersCount: 0,\r\n    immerEnabled: false\r\n  })\r\n\r\n  // 订阅示例\r\n  useEffect(() => {\r\n    // 订阅计数器变化\r\n    const unsubscribeCount = useBasicStore.subscribe(\r\n      selectCount,\r\n      (count) => {\r\n        setSubscriptions(prev => [...prev, {\r\n          id: Date.now(),\r\n          type: 'count',\r\n          message: `计数器变更为: ${count}`,\r\n          timestamp: new Date().toLocaleTimeString()\r\n        }])\r\n      }\r\n    )\r\n\r\n    // 订阅用户变化\r\n    const unsubscribeUser = useBasicStore.subscribe(\r\n      selectUser,\r\n      (user) => {\r\n        setSubscriptions(prev => [...prev, {\r\n          id: Date.now(),\r\n          type: 'user',\r\n          message: `用户信息更新: ${user.name}`,\r\n          timestamp: new Date().toLocaleTimeString()\r\n        }])\r\n      }\r\n    )\r\n\r\n    // 订阅主题变化\r\n    const unsubscribeTheme = useSettingsStore.subscribe(\r\n      selectTheme,\r\n      (theme) => {\r\n        setSubscriptions(prev => [...prev, {\r\n          id: Date.now(),\r\n          type: 'theme',\r\n          message: `主题切换为: ${theme}`,\r\n          timestamp: new Date().toLocaleTimeString()\r\n        }])\r\n      }\r\n    )\r\n\r\n    // 更新中间件统计\r\n    setMiddlewareStats({\r\n      devtoolsEnabled: true,\r\n      persistEnabled: true,\r\n      subscribersCount: 3,\r\n      immerEnabled: true\r\n    })\r\n\r\n    return () => {\r\n      unsubscribeCount()\r\n      unsubscribeUser()\r\n      unsubscribeTheme()\r\n    }\r\n  }, [])\r\n\r\n  // 清除订阅日志\r\n  const clearSubscriptions = () => {\r\n    setSubscriptions([])\r\n  }\r\n\r\n  // 模拟批量操作\r\n  const simulateBatchOperations = () => {\r\n    // 使用Immer批量更新\r\n    useBasicStore.getState().batchUpdate({\r\n      count: count + 10,\r\n      name: 'Batch Updated',\r\n      items: [...items, { id: Date.now(), name: '批量添加项目', completed: false }]\r\n    })\r\n  }\r\n\r\n  // 测试异步中间件\r\n  const testAsyncMiddleware = async () => {\r\n    try {\r\n      await simulateAsyncOperation()\r\n      await fetchUsers()\r\n    } catch (error) {\r\n      console.error('异步操作失败:', error)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"middleware-demo-page\">\r\n      <div className=\"card-header\">\r\n        <h1 className=\"card-title\">🔧 中间件演示</h1>\r\n        <p className=\"card-description\">\r\n          展示Zustand的各种中间件功能，包括devtools、subscribeWithSelector、immer、persist等\r\n        </p>\r\n      </div>\r\n\r\n      {/* 中间件状态概览 */}\r\n      <div className=\"card mb-lg\">\r\n        <h2 className=\"card-title\">📊 中间件状态</h2>\r\n        <div className=\"grid grid-4\">\r\n          <div className=\"text-center\">\r\n            <div className={`text-2xl font-bold ${middlewareStats.devtoolsEnabled ? 'text-success' : 'text-danger'}`}>\r\n              {middlewareStats.devtoolsEnabled ? '✅' : '❌'}\r\n            </div>\r\n            <div className=\"text-sm text-secondary\">DevTools</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className={`text-2xl font-bold ${middlewareStats.persistEnabled ? 'text-success' : 'text-danger'}`}>\r\n              {middlewareStats.persistEnabled ? '✅' : '❌'}\r\n            </div>\r\n            <div className=\"text-sm text-secondary\">Persist</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-info\">{middlewareStats.subscribersCount}</div>\r\n            <div className=\"text-sm text-secondary\">订阅者</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className={`text-2xl font-bold ${middlewareStats.immerEnabled ? 'text-success' : 'text-danger'}`}>\r\n              {middlewareStats.immerEnabled ? '✅' : '❌'}\r\n            </div>\r\n            <div className=\"text-sm text-secondary\">Immer</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-2\">\r\n        {/* DevTools 中间件 */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">🛠️ DevTools 中间件</h2>\r\n          <p className=\"text-sm text-secondary mb-md\">\r\n            DevTools中间件允许在浏览器开发者工具中调试状态变化\r\n          </p>\r\n          \r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex justify-between items-center\">\r\n              <span>状态跟踪</span>\r\n              <span className=\"badge badge-success\">已启用</span>\r\n            </div>\r\n            <div className=\"flex justify-between items-center\">\r\n              <span>时间旅行</span>\r\n              <span className=\"badge badge-success\">支持</span>\r\n            </div>\r\n            <div className=\"flex justify-between items-center\">\r\n              <span>Action 记录</span>\r\n              <span className=\"badge badge-success\">已启用</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"mt-md\">\r\n            <h3 className=\"font-semibold mb-sm\">测试Actions</h3>\r\n            <div className=\"flex gap-sm flex-wrap\">\r\n              <button onClick={increment} className=\"btn btn-primary btn-sm\">\r\n                增加计数\r\n              </button>\r\n              <button onClick={decrement} className=\"btn btn-secondary btn-sm\">\r\n                减少计数\r\n              </button>\r\n              <button onClick={() => setName('DevTools Test')} className=\"btn btn-info btn-sm\">\r\n                设置名称\r\n              </button>\r\n              <button onClick={() => addItem('DevTools项目')} className=\"btn btn-success btn-sm\">\r\n                添加项目\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"mt-md p-sm bg-surface rounded\">\r\n            <div className=\"text-xs text-secondary\">\r\n              💡 打开浏览器开发者工具 → Redux DevTools 查看状态变化\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* SubscribeWithSelector 中间件 */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">👂 SubscribeWithSelector</h2>\r\n          <p className=\"text-sm text-secondary mb-md\">\r\n            精确订阅特定状态变化，避免不必要的重新渲染\r\n          </p>\r\n\r\n          <div className=\"mb-md\">\r\n            <h3 className=\"font-semibold mb-sm\">订阅日志 ({subscriptions.length})</h3>\r\n            <div className=\"max-h-32 overflow-y-auto space-y-1\">\r\n              {subscriptions.slice(-5).map((sub) => (\r\n                <div key={sub.id} className=\"text-xs p-xs bg-surface rounded\">\r\n                  <span className={`badge badge-sm ${\r\n                    sub.type === 'count' ? 'badge-primary' : \r\n                    sub.type === 'user' ? 'badge-success' : 'badge-info'\r\n                  }`}>\r\n                    {sub.type}\r\n                  </span>\r\n                  <span className=\"ml-xs\">{sub.message}</span>\r\n                  <span className=\"text-muted ml-xs\">{sub.timestamp}</span>\r\n                </div>\r\n              ))}\r\n              {subscriptions.length === 0 && (\r\n                <div className=\"text-center text-muted py-sm\">暂无订阅日志</div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex gap-sm\">\r\n            <button onClick={clearSubscriptions} className=\"btn btn-warning btn-sm\">\r\n              清除日志\r\n            </button>\r\n            <button \r\n              onClick={() => incrementByAmount(Math.floor(Math.random() * 10))} \r\n              className=\"btn btn-primary btn-sm\"\r\n            >\r\n              随机增加\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Immer 中间件 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">🔄 Immer 中间件</h2>\r\n        <p className=\"text-sm text-secondary mb-md\">\r\n          使用Immer进行不可变状态更新，可以直接\"修改\"状态对象\r\n        </p>\r\n\r\n        <div className=\"grid grid-2\">\r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">当前状态</h3>\r\n            <pre className=\"bg-surface p-md rounded text-sm overflow-auto\">\r\n{JSON.stringify({\r\n  count,\r\n  name,\r\n  itemsCount: items.length,\r\n  user: { name: user.name, age: user.age }\r\n}, null, 2)}\r\n            </pre>\r\n          </div>\r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">Immer操作</h3>\r\n            <div className=\"space-y-2\">\r\n              <button\r\n                onClick={simulateBatchOperations}\r\n                className=\"btn btn-primary w-full\"\r\n              >\r\n                🔄 批量更新 (Immer)\r\n              </button>\r\n              <button\r\n                onClick={() => useBasicStore.getState().updateUser({\r\n                  age: user.age + 1,\r\n                  profile: { ...user.profile, lastLogin: new Date().toISOString() }\r\n                })}\r\n                className=\"btn btn-success w-full\"\r\n              >\r\n                👤 更新用户信息\r\n              </button>\r\n              <button\r\n                onClick={testAsyncMiddleware}\r\n                disabled={asyncLoading}\r\n                className=\"btn btn-info w-full\"\r\n              >\r\n                {asyncLoading ? '⏳ 处理中...' : '🚀 异步操作测试'}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Persist 中间件 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">💾 Persist 中间件</h2>\r\n        <p className=\"text-sm text-secondary mb-md\">\r\n          自动将状态持久化到本地存储，页面刷新后自动恢复\r\n        </p>\r\n\r\n        <div className=\"grid grid-3\">\r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">主题持久化</h3>\r\n            <div className=\"space-y-2\">\r\n              <p className=\"text-sm\">当前主题: <span className=\"badge badge-info\">{theme}</span></p>\r\n              <div className=\"flex gap-xs\">\r\n                <button onClick={() => setTheme('light')} className=\"btn btn-sm btn-secondary\">\r\n                  ☀️\r\n                </button>\r\n                <button onClick={() => setTheme('dark')} className=\"btn btn-sm btn-secondary\">\r\n                  🌙\r\n                </button>\r\n                <button onClick={() => setTheme('auto')} className=\"btn btn-sm btn-secondary\">\r\n                  🔄\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">统计信息</h3>\r\n            <div className=\"text-sm space-y-1\">\r\n              <div>历史记录: {history.length} 条</div>\r\n              <div>操作总数: {stats.totalOperations}</div>\r\n              <div>错误次数: {stats.errorCount}</div>\r\n              <div>成功率: {stats.successRate}%</div>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">存储管理</h3>\r\n            <div className=\"space-y-2\">\r\n              <button\r\n                onClick={() => localStorage.clear()}\r\n                className=\"btn btn-warning btn-sm w-full\"\r\n              >\r\n                🗑️ 清除所有存储\r\n              </button>\r\n              <button\r\n                onClick={() => window.location.reload()}\r\n                className=\"btn btn-info btn-sm w-full\"\r\n              >\r\n                🔄 重新加载页面\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 错误处理中间件 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">⚠️ 错误处理</h2>\r\n        <div className=\"grid grid-2\">\r\n          <div>\r\n            {error && (\r\n              <div className=\"alert alert-danger\">\r\n                <strong>错误:</strong> {error}\r\n                <button onClick={clearError} className=\"btn btn-sm btn-danger ml-sm\">\r\n                  清除错误\r\n                </button>\r\n              </div>\r\n            )}\r\n            {!error && (\r\n              <div className=\"alert alert-success\">\r\n                当前没有错误\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div>\r\n            <button\r\n              onClick={() => {\r\n                try {\r\n                  throw new Error('测试错误处理')\r\n                } catch (err) {\r\n                  console.error('捕获错误:', err.message)\r\n                }\r\n              }}\r\n              className=\"btn btn-danger\"\r\n            >\r\n              🚨 模拟错误\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* API请求日志 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">🌐 API请求中间件</h2>\r\n        <p className=\"text-sm text-secondary mb-md\">\r\n          API请求的拦截器和日志记录中间件\r\n        </p>\r\n\r\n        <div className=\"grid grid-2\">\r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">最近请求日志</h3>\r\n            <div className=\"max-h-32 overflow-y-auto space-y-1\">\r\n              {requestLogs.slice(-3).map((log) => (\r\n                <div key={log.id} className=\"text-xs p-xs bg-surface rounded\">\r\n                  <span className={`badge badge-sm ${\r\n                    log.status === 'cached' ? 'badge-info' : \r\n                    typeof log.status === 'number' && log.status < 400 ? 'badge-success' : 'badge-danger'\r\n                  }`}>\r\n                    {log.method}\r\n                  </span>\r\n                  <span className=\"ml-xs\">{log.url}</span>\r\n                </div>\r\n              ))}\r\n              {requestLogs.length === 0 && (\r\n                <div className=\"text-center text-muted py-sm\">暂无请求日志</div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <div className=\"space-y-2\">\r\n              <button onClick={fetchUsers} className=\"btn btn-primary btn-sm w-full\">\r\n                📡 发起API请求\r\n              </button>\r\n              <button onClick={clearRequestLogs} className=\"btn btn-warning btn-sm w-full\">\r\n                🗑️ 清除请求日志\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 中间件说明 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">📖 中间件说明</h2>\r\n        <div className=\"grid grid-2\">\r\n          <div>\r\n            <h3 className=\"font-semibold mb-md\">🔧 核心中间件</h3>\r\n            <ul className=\"text-sm text-secondary space-y-1\">\r\n              <li><strong>devtools:</strong> Redux DevTools集成</li>\r\n              <li><strong>subscribeWithSelector:</strong> 选择性订阅</li>\r\n              <li><strong>immer:</strong> 不可变状态更新</li>\r\n              <li><strong>persist:</strong> 状态持久化</li>\r\n            </ul>\r\n          </div>\r\n          <div>\r\n            <h3 className=\"font-semibold mb-md\">⚡ 自定义中间件</h3>\r\n            <ul className=\"text-sm text-secondary space-y-1\">\r\n              <li><strong>错误处理:</strong> 统一错误捕获和处理</li>\r\n              <li><strong>日志记录:</strong> 操作历史跟踪</li>\r\n              <li><strong>API拦截:</strong> 请求响应拦截</li>\r\n              <li><strong>性能监控:</strong> 状态变更性能统计</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 重置区域 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">🔄 重置操作</h2>\r\n        <div className=\"flex gap-sm\">\r\n          <button\r\n            onClick={resetBasic}\r\n            className=\"btn btn-warning\"\r\n          >\r\n            🔄 重置基础Store\r\n          </button>\r\n          <button\r\n            onClick={() => {\r\n              resetBasic()\r\n              clearSubscriptions()\r\n              clearRequestLogs()\r\n            }}\r\n            className=\"btn btn-danger\"\r\n          >\r\n            🗑️ 重置所有\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default MiddlewareDemo "], "names": ["MiddlewareDemo", "count", "useBasicStore", "selectCount", "name", "selectName", "items", "selectItems", "user", "selectUser", "history", "selectHistory", "error", "selectError", "asyncLoading", "selectIsLoading", "stats", "selectStats", "theme", "useSettingsStore", "selectTheme", "requestLogs", "useApiStore", "selectRequestLogs", "increment", "decrement", "incrementByAmount", "setName", "addItem", "simulateAsyncOperation", "clearError", "reset", "resetBasic", "setTheme", "fetchUsers", "clearRequestLogs", "subscriptions", "setSubscriptions", "useState", "middlewareStats", "setMiddlewareStats", "devtoolsEnabled", "persistEnabled", "subscribersCount", "immerEnabled", "useEffect", "unsubscribeCount", "subscribe", "prev", "id", "Date", "now", "type", "message", "timestamp", "toLocaleTimeString", "unsubscribeUser", "unsubscribeTheme", "clearSubscriptions", "jsxs", "className", "children", "jsx", "onClick", "length", "slice", "map", "sub", "Math", "floor", "random", "stringify", "itemsCount", "age", "getState", "batchUpdate", "completed", "updateUser", "profile", "lastLogin", "toISOString", "async", "disabled", "totalOperations", "errorCount", "successRate", "localStorage", "clear", "window", "location", "reload", "Error", "err", "log", "status", "method", "url"], "mappings": "sRAKA,SAASA,IAEP,MAAMC,EAAQC,EAAcC,GACtBC,EAAOF,EAAcG,GACrBC,EAAQJ,EAAcK,GACtBC,EAAON,EAAcO,GACrBC,EAAUR,EAAcS,GACxBC,EAAQV,EAAcW,GACtBC,EAAeZ,EAAca,GAC7BC,EAAQd,EAAce,GAEtBC,EAAQC,EAAiBC,GACzBC,EAAcC,EAAYC,IAG1BC,UACJA,EAAAC,UACAA,EAAAC,kBACAA,EAAAC,QACAA,EAAAC,QACAA,EAAAC,uBACAA,EAAAC,WACAA,EACAC,MAAOC,GACL9B,KAEE+B,SAAEA,GAAad,KACfe,WAAEA,EAAAC,iBAAYA,GAAqBb,KAGlCc,EAAeC,GAAoBC,EAAAA,SAAS,KAC5CC,EAAiBC,GAAsBF,WAAS,CACrDG,iBAAiB,EACjBC,gBAAgB,EAChBC,iBAAkB,EAClBC,cAAc,IAIhBC,EAAAA,UAAU,KAER,MAAMC,EAAmB5C,EAAc6C,UACrC5C,EACCF,IACCoC,EAAiBW,GAAQ,IAAIA,EAAM,CACjCC,GAAIC,KAAKC,MACTC,KAAM,QACNC,QAAS,WAAWpD,IACpBqD,WAAA,IAAeJ,MAAOK,0BAMtBC,EAAkBtD,EAAc6C,UACpCtC,EACCD,IACC6B,EAAiBW,GAAQ,IAAIA,EAAM,CACjCC,GAAIC,KAAKC,MACTC,KAAM,OACNC,QAAS,WAAW7C,EAAKJ,OACzBkD,WAAA,IAAeJ,MAAOK,0BAMtBE,EAAmBtC,EAAiB4B,UACxC3B,EACCF,IACCmB,EAAiBW,GAAQ,IAAIA,EAAM,CACjCC,GAAIC,KAAKC,MACTC,KAAM,QACNC,QAAS,UAAUnC,IACnBoC,WAAA,IAAeJ,MAAOK,0BAa5B,OAPAf,EAAmB,CACjBC,iBAAiB,EACjBC,gBAAgB,EAChBC,iBAAkB,EAClBC,cAAc,IAGT,KACLE,IACAU,IACAC,MAED,IAGH,MAAMC,EAAqB,KACzBrB,EAAiB,KAuBnB,SACEsB,KAAC,MAAA,CAAIC,UAAU,uBACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,aAC3BC,EAAAA,IAAC,IAAA,CAAEF,UAAU,mBAAmBC,SAAA,2EAMlCF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,eAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,GAAAC,IAAC,MAAA,CAAIF,UAAW,uBAAsBrB,EAAgBE,gBAAkB,eAAiB,eACtFoB,SAAAtB,EAAgBE,gBAAkB,IAAM,MAE3CqB,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,kBAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,GAAAC,IAAC,MAAA,CAAIF,UAAW,uBAAsBrB,EAAgBG,eAAiB,eAAiB,eACrFmB,SAAAtB,EAAgBG,eAAiB,IAAM,MAE1CoB,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,iBAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,+BAAgCC,SAAAtB,EAAgBI,mBAC/DmB,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,aAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,GAAAC,IAAC,MAAA,CAAIF,UAAW,uBAAsBrB,EAAgBK,aAAe,eAAiB,eACnFiB,SAAAtB,EAAgBK,aAAe,IAAM,MAExCkB,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,qBAK9CF,KAAC,MAAA,CAAIC,UAAU,cAEbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,qBAC3BC,EAAAA,IAAC,IAAA,CAAEF,UAAU,+BAA+BC,SAAA,oCAI5CF,KAAC,MAAA,CAAIC,UAAU,YACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,oCACbC,SAAA,GAAAC,IAAC,QAAKD,SAAA,SACNC,EAAAA,IAAC,OAAA,CAAKF,UAAU,sBAAsBC,SAAA,aAExCF,KAAC,MAAA,CAAIC,UAAU,oCACbC,SAAA,GAAAC,IAAC,QAAKD,SAAA,SACNC,EAAAA,IAAC,OAAA,CAAKF,UAAU,sBAAsBC,SAAA,YAExCF,KAAC,MAAA,CAAIC,UAAU,oCACbC,SAAA,GAAAC,IAAC,QAAKD,SAAA,cACNC,EAAAA,IAAC,OAAA,CAAKF,UAAU,sBAAsBC,SAAA,gBAI1CF,KAAC,MAAA,CAAIC,UAAU,QACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,gBACpCF,KAAC,MAAA,CAAIC,UAAU,wBACbC,SAAA,CAAAC,MAAC,SAAA,CAAOC,QAASvC,EAAWoC,UAAU,yBAAyBC,SAAA,eAG9D,SAAA,CAAOE,QAAStC,EAAWmC,UAAU,2BAA2BC,SAAA,SAGjEC,EAAAA,IAAC,UAAOC,QAAS,IAAMpC,EAAQ,iBAAkBiC,UAAU,sBAAsBC,SAAA,SAGjFC,EAAAA,IAAC,UAAOC,QAAS,IAAMnC,EAAQ,cAAegC,UAAU,yBAAyBC,SAAA,iBAMrFC,IAAC,OAAIF,UAAU,gCACbC,eAAC,MAAA,CAAID,UAAU,yBAAyBC,SAAA,iDAO5CF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,6BAC3BC,EAAAA,IAAC,IAAA,CAAEF,UAAU,+BAA+BC,SAAA,4BAI5CF,KAAC,MAAA,CAAIC,UAAU,QACbC,SAAA,GAAAF,KAAC,KAAA,CAAGC,UAAU,sBAAsBC,SAAA,CAAA,SAAOzB,EAAc4B,OAAO,SAChEL,KAAC,MAAA,CAAIC,UAAU,qCACZC,SAAA,CAAAzB,EAAc6B,OAAM,GAAIC,IAAKC,GAC5BR,EAAAA,KAAC,MAAA,CAAiBC,UAAU,kCAC1BC,SAAA,OAAC,OAAA,CAAKD,UAAW,mBACF,UAAbO,EAAIf,KAAmB,gBACV,SAAbe,EAAIf,KAAkB,gBAAkB,cAEvCS,WAAIT,OAEPU,EAAAA,IAAC,OAAA,CAAKF,UAAU,QAASC,WAAIR,UAC7BS,EAAAA,IAAC,OAAA,CAAKF,UAAU,mBAAoBC,WAAIP,cARhCa,EAAIlB,KAWU,IAAzBb,EAAc4B,cACZ,MAAA,CAAIJ,UAAU,+BAA+BC,SAAA,mBAKpDF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,MAAC,SAAA,CAAOC,QAASL,EAAoBE,UAAU,yBAAyBC,SAAA,SAGxEC,EAAAA,IAAC,SAAA,CACCC,QAAS,IAAMrC,EAAkB0C,KAAKC,MAAsB,GAAhBD,KAAKE,WACjDV,UAAU,yBACXC,SAAA,oBAQPF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,iBAC3BC,EAAAA,IAAC,IAAA,CAAEF,UAAU,+BAA+BC,SAAA,oCAI5CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,OAAC,MAAA,CACCE,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,SACpCC,EAAAA,IAAC,MAAA,CAAIF,UAAU,gDAC1BC,cAAKU,UAAU,CACdtE,QACAG,OACAoE,WAAYlE,EAAM0D,OAClBxD,KAAM,CAAEJ,KAAMI,EAAKJ,KAAMqE,IAAKjE,EAAKiE,MAClC,KAAM,eAGE,MAAA,CACCZ,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,cACpCF,KAAC,MAAA,CAAIC,UAAU,YACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CACCC,QA1KkB,KAE9B7D,EAAcwE,WAAWC,YAAY,CACnC1E,MAAOA,EAAQ,GACfG,KAAM,gBACNE,MAAO,IAAIA,EAAO,CAAE2C,GAAIC,KAAKC,MAAO/C,KAAM,SAAUwE,WAAW,OAsKrDhB,UAAU,yBACXC,SAAA,oBAGDC,EAAAA,IAAC,SAAA,CACCC,QAAS,IAAM7D,EAAcwE,WAAWG,WAAW,CACjDJ,IAAKjE,EAAKiE,IAAM,EAChBK,QAAS,IAAKtE,EAAKsE,QAASC,eAAe7B,MAAO8B,iBAEpDpB,UAAU,yBACXC,SAAA,cAGDC,EAAAA,IAAC,SAAA,CACCC,QA/KckB,UAC1B,UACQpD,UACAK,GACR,OAAStB,GAET,GA0KYsE,SAAUpE,EACV8C,UAAU,sBAETC,WAAe,WAAa,4BAQvCF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,mBAC3BC,EAAAA,IAAC,IAAA,CAAEF,UAAU,+BAA+BC,SAAA,8BAI5CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,OAAC,MAAA,CACCE,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,YACpCF,KAAC,MAAA,CAAIC,UAAU,YACbC,SAAA,GAAAF,KAAC,IAAA,CAAEC,UAAU,UAAUC,SAAA,CAAA,SAAMC,EAAAA,IAAC,OAAA,CAAKF,UAAU,mBAAoBC,SAAA3C,SACjEyC,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CAAOC,QAAS,IAAM9B,EAAS,SAAU2B,UAAU,2BAA2BC,SAAA,OAG/EC,EAAAA,IAAC,UAAOC,QAAS,IAAM9B,EAAS,QAAS2B,UAAU,2BAA2BC,SAAA,OAG9EC,EAAAA,IAAC,UAAOC,QAAS,IAAM9B,EAAS,QAAS2B,UAAU,2BAA2BC,SAAA,uBAMnF,MAAA,CACCA,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,WACpCF,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAF,OAAC,MAAA,CAAIE,SAAA,CAAA,SAAOnD,EAAQsD,OAAO,eAC1B,MAAA,CAAIH,SAAA,CAAA,SAAO7C,EAAMmE,0BACjB,MAAA,CAAItB,SAAA,CAAA,SAAO7C,EAAMoE,qBACjB,MAAA,CAAIvB,SAAA,CAAA,QAAM7C,EAAMqE,YAAY,oBAGhC,MAAA,CACCxB,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,WACpCF,KAAC,MAAA,CAAIC,UAAU,YACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CACCC,QAAS,IAAMuB,aAAaC,QAC5B3B,UAAU,gCACXC,SAAA,eAGDC,EAAAA,IAAC,SAAA,CACCC,QAAS,IAAMyB,OAAOC,SAASC,SAC/B9B,UAAU,6BACXC,SAAA,4BASTF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,OAAC,MAAA,CACEE,SAAA,CAAAjD,GACC+C,EAAAA,KAAC,MAAA,CAAIC,UAAU,qBACbC,SAAA,GAAAC,IAAC,UAAOD,SAAA,QAAY,IAAEjD,QACrB,SAAA,CAAOmD,QAASjC,EAAY8B,UAAU,8BAA8BC,SAAA,aAKvEjD,GACAkD,EAAAA,IAAC,MAAA,CAAIF,UAAU,sBAAsBC,SAAA,oBAKxC,MAAA,CACCA,SAAAC,EAAAA,IAAC,SAAA,CACCC,QAAS,KACP,IACE,MAAM,IAAI4B,MAAM,SAClB,OAASC,GAET,GAEFhC,UAAU,iBACXC,SAAA,sBAQPF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,gBAC3BC,EAAAA,IAAC,IAAA,CAAEF,UAAU,+BAA+BC,SAAA,wBAI5CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,OAAC,MAAA,CACCE,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,aACpCF,KAAC,MAAA,CAAIC,UAAU,qCACZC,SAAA,CAAAxC,EAAY4C,OAAM,GAAIC,IAAK2B,GAC1BlC,EAAAA,KAAC,MAAA,CAAiBC,UAAU,kCAC1BC,SAAA,CAAAC,MAAC,QAAKF,UAAW,mBACA,WAAfiC,EAAIC,OAAsB,aACJ,iBAAfD,EAAIC,QAAuBD,EAAIC,OAAS,IAAM,gBAAkB,gBAEtEjC,WAAIkC,SAEPjC,EAAAA,IAAC,OAAA,CAAKF,UAAU,QAASC,WAAImC,QAPrBH,EAAI5C,KAUQ,IAAvB5B,EAAY2C,cACV,MAAA,CAAIJ,UAAU,+BAA+BC,SAAA,iBAIpDC,MAAC,MAAA,CACCD,SAAAF,EAAAA,KAAC,MAAA,CAAIC,UAAU,YACbC,SAAA,CAAAC,MAAC,SAAA,CAAOC,QAAS7B,EAAY0B,UAAU,gCAAgCC,SAAA,qBAGtE,SAAA,CAAOE,QAAS5B,EAAkByB,UAAU,gCAAgCC,SAAA,4BASrFF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,eAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,OAAC,MAAA,CACCE,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,eACpCF,KAAC,KAAA,CAAGC,UAAU,mCACZC,SAAA,CAAAF,OAAC,KAAA,CAAGE,SAAA,GAAAC,IAAC,UAAOD,SAAA,cAAkB,8BAC7B,KAAA,CAAGA,SAAA,GAAAC,IAAC,UAAOD,SAAA,2BAA+B,mBAC1C,KAAA,CAAGA,SAAA,GAAAC,IAAC,UAAOD,SAAA,WAAe,qBAC1B,KAAA,CAAGA,SAAA,GAAAC,IAAC,UAAOD,SAAA,aAAiB,yBAGhC,MAAA,CACCA,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,eACpCF,KAAC,KAAA,CAAGC,UAAU,mCACZC,SAAA,CAAAF,OAAC,KAAA,CAAGE,SAAA,GAAAC,IAAC,UAAOD,SAAA,UAAc,uBACzB,KAAA,CAAGA,SAAA,GAAAC,IAAC,UAAOD,SAAA,UAAc,oBACzB,KAAA,CAAGA,SAAA,GAAAC,IAAC,UAAOD,SAAA,WAAe,oBAC1B,KAAA,CAAGA,SAAA,GAAAC,IAAC,UAAOD,SAAA,UAAc,6BAOlCF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CACCC,QAAS/B,EACT4B,UAAU,kBACXC,SAAA,iBAGDC,EAAAA,IAAC,SAAA,CACCC,QAAS,KACP/B,IACA0B,IACAvB,KAEFyB,UAAU,iBACXC,SAAA,qBAOX"}