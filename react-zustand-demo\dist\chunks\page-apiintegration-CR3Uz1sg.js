import{r as s,j as e}from"./react-vendor-CyNirxNk.js";import{u as a,s as l,b as t,c,d as n,e as r,f as i,a as d}from"./store-apistore-B0ecmVeH.js";function m(){const m=a(l),x=a(t),h=a(c),o=a(n),j=a(r),u=a(i),b=a(d),{fetchUsers:N,fetchUserById:g,createUser:v,updateUser:p,deleteUser:f,fetchPosts:y,setCurrentUser:C,clearError:k,clearRequestLogs:w,clearCache:U,reset:P}=a(),[T,E]=s.useState(""),[I,A]=s.useState({name:"",username:"",email:"",phone:""}),[S,O]=s.useState(null),[D,G]=s.useState(!1);s.useEffect(()=>{0===m.length&&N()},[N,m.length]);return e.jsxs("div",{className:"api-integration-page",children:[e.jsxs("div",{className:"card-header",children:[e.jsx("h1",{className:"card-title",children:"🌐 API集成"}),e.jsx("p",{className:"card-description",children:"展示与外部API的集成，包括HTTP请求、缓存、错误处理、状态管理等功能"})]}),e.jsxs("div",{className:"card mb-lg",children:[e.jsx("h2",{className:"card-title",children:"📊 API统计"}),e.jsxs("div",{className:"grid grid-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary",children:b.totalUsers}),e.jsx("div",{className:"text-sm text-secondary",children:"用户总数"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-success",children:b.totalRequests}),e.jsx("div",{className:"text-sm text-secondary",children:"请求次数"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-warning",children:b.cacheSize}),e.jsx("div",{className:"text-sm text-secondary",children:"缓存条目"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-danger",children:Object.keys(j).length}),e.jsx("div",{className:"text-sm text-secondary",children:"错误数量"})]})]})]}),Object.keys(j).length>0&&e.jsxs("div",{className:"alert alert-danger mb-lg",children:[e.jsx("strong",{children:"API错误："}),e.jsx("ul",{className:"mt-sm",children:Object.entries(j).map(([s,a])=>e.jsxs("li",{children:[e.jsxs("strong",{children:[s,":"]})," ",a]},s))}),e.jsx("button",{onClick:()=>k(),className:"btn btn-sm btn-danger mt-sm",children:"清除所有错误"})]}),e.jsxs("div",{className:"grid grid-2",children:[e.jsxs("div",{className:"card",children:[e.jsxs("div",{className:"flex justify-between items-center mb-md",children:[e.jsx("h2",{className:"card-title",children:"👥 用户管理"}),e.jsxs("div",{className:"flex gap-sm",children:[e.jsx("button",{onClick:()=>G(!D),className:"btn btn-success btn-sm",children:D?"取消":"添加用户"}),e.jsx("button",{onClick:N,disabled:o.fetchUsers,className:"btn btn-primary btn-sm",children:o.fetchUsers?"加载中...":"刷新列表"})]})]}),D&&e.jsxs("div",{className:"mb-md p-md bg-surface rounded",children:[e.jsx("h3",{className:"font-semibold mb-md",children:"创建新用户"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("input",{type:"text",value:I.name,onChange:s=>A({...I,name:s.target.value}),className:"form-control",placeholder:"用户名 *"}),e.jsx("input",{type:"text",value:I.username,onChange:s=>A({...I,username:s.target.value}),className:"form-control",placeholder:"用户名简称"}),e.jsx("input",{type:"email",value:I.email,onChange:s=>A({...I,email:s.target.value}),className:"form-control",placeholder:"邮箱 *"}),e.jsx("input",{type:"tel",value:I.phone,onChange:s=>A({...I,phone:s.target.value}),className:"form-control",placeholder:"电话"})]}),e.jsxs("div",{className:"flex gap-sm mt-md",children:[e.jsx("button",{onClick:async()=>{if(I.name.trim()&&I.email.trim())try{await v(I),A({name:"",username:"",email:"",phone:""}),G(!1),alert("用户创建成功！")}catch(s){}else alert("请填写用户名和邮箱")},disabled:o.createUser,className:"btn btn-success",children:o.createUser?"创建中...":"创建用户"}),e.jsx("button",{onClick:()=>G(!1),className:"btn btn-secondary",children:"取消"})]})]}),e.jsxs("div",{className:"space-y-1",children:[m.map(s=>e.jsx("div",{className:"p-sm border rounded cursor-pointer transition-all "+(T==s.id?"bg-primary text-white":"hover:bg-surface"),onClick:()=>(async s=>{if(E(s),s)try{await g(parseInt(s))}catch(e){}else C(null)})(s.id),children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-semibold",children:s.name}),e.jsx("div",{className:"text-sm opacity-75",children:s.email})]}),e.jsxs("div",{className:"flex gap-xs",children:[e.jsx("button",{onClick:e=>{e.stopPropagation(),O({...s})},className:"btn btn-sm btn-primary",children:"✏️"}),e.jsx("button",{onClick:e=>{e.stopPropagation(),(async s=>{if(confirm("确定要删除这个用户吗？"))try{await f(s),x&&x.id===s&&(C(null),E("")),alert("用户删除成功！")}catch(e){}})(s.id)},className:"btn btn-sm btn-danger",children:"🗑️"})]})]})},s.id)),0===m.length&&!o.fetchUsers&&e.jsx("div",{className:"text-center text-muted py-lg",children:"暂无用户数据，点击刷新列表获取数据"}),o.fetchUsers&&e.jsxs("div",{className:"text-center py-lg",children:[e.jsx("div",{className:"spinner"}),e.jsx("div",{className:"text-sm text-muted mt-sm",children:"加载用户列表中..."})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("h2",{className:"card-title",children:"👤 用户详情"}),x?e.jsxs("div",{children:[e.jsxs("div",{className:"mb-md",children:[e.jsx("h3",{className:"font-semibold text-lg",children:x.name}),e.jsxs("div",{className:"text-sm text-secondary space-y-1",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"用户名:"})," ",x.username]}),e.jsxs("div",{children:[e.jsx("strong",{children:"邮箱:"})," ",x.email]}),e.jsxs("div",{children:[e.jsx("strong",{children:"电话:"})," ",x.phone]}),x.website&&e.jsxs("div",{children:[e.jsx("strong",{children:"网站:"})," ",x.website]}),x.company&&e.jsxs("div",{children:[e.jsx("strong",{children:"公司:"})," ",x.company.name]}),x.address&&e.jsxs("div",{children:[e.jsx("strong",{children:"地址:"})," ",x.address.city,", ",x.address.street]})]})]}),e.jsxs("div",{className:"flex gap-sm",children:[e.jsx("button",{onClick:()=>(async s=>{try{await y(s)}catch(e){}})(x.id),disabled:o.fetchPosts,className:"btn btn-info btn-sm",children:o.fetchPosts?"加载中...":"获取文章"}),e.jsx("button",{onClick:()=>O({...x}),className:"btn btn-warning btn-sm",children:"编辑用户"})]})]}):e.jsx("div",{className:"text-center text-muted py-lg",children:"请从左侧选择一个用户查看详情"})]})]}),S&&e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"✏️ 编辑用户"}),e.jsxs("div",{className:"grid grid-2",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"用户名"}),e.jsx("input",{type:"text",value:S.name,onChange:s=>O({...S,name:s.target.value}),className:"form-control"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"用户名简称"}),e.jsx("input",{type:"text",value:S.username,onChange:s=>O({...S,username:s.target.value}),className:"form-control"})]})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"邮箱"}),e.jsx("input",{type:"email",value:S.email,onChange:s=>O({...S,email:s.target.value}),className:"form-control"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"电话"}),e.jsx("input",{type:"tel",value:S.phone,onChange:s=>O({...S,phone:s.target.value}),className:"form-control"})]})]})]}),e.jsxs("div",{className:"flex gap-sm",children:[e.jsx("button",{onClick:async()=>{if(S)try{await p(S.id,S),O(null),alert("用户更新成功！")}catch(s){}},disabled:o.updateUser,className:"btn btn-success",children:o.updateUser?"更新中...":"保存更改"}),e.jsx("button",{onClick:()=>O(null),className:"btn btn-secondary",children:"取消"})]})]}),h.length>0&&e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"📝 用户文章"}),e.jsxs("div",{className:"space-y-2",children:[h.slice(0,5).map(s=>e.jsxs("div",{className:"p-md bg-surface rounded",children:[e.jsx("h3",{className:"font-semibold mb-sm",children:s.title}),e.jsx("p",{className:"text-sm text-secondary",children:s.body})]},s.id)),h.length>5&&e.jsxs("div",{className:"text-center text-muted",children:["还有 ",h.length-5," 篇文章..."]})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsxs("div",{className:"flex justify-between items-center mb-md",children:[e.jsx("h2",{className:"card-title",children:"📋 请求日志"}),e.jsxs("div",{className:"flex gap-sm",children:[e.jsx("button",{onClick:w,className:"btn btn-secondary btn-sm",children:"清空日志"}),e.jsx("button",{onClick:U,className:"btn btn-warning btn-sm",children:"清除缓存"}),e.jsx("button",{onClick:P,className:"btn btn-danger btn-sm",children:"重置所有"})]})]}),e.jsxs("div",{className:"space-y-1 max-h-64 overflow-y-auto",children:[u.slice(0,10).map(s=>e.jsxs("div",{className:"flex justify-between items-center p-sm bg-surface rounded text-sm",children:[e.jsxs("div",{className:"flex items-center gap-sm",children:[e.jsx("span",{className:"badge "+("cached"===s.status?"badge-info":"number"==typeof s.status&&s.status<400?"badge-success":"badge-danger"),children:s.method}),e.jsx("span",{children:s.url}),e.jsxs("span",{className:"text-muted",children:["- ",s.message]})]}),e.jsx("span",{className:"text-muted",children:new Date(s.timestamp).toLocaleTimeString()})]},s.id)),0===u.length&&e.jsx("div",{className:"text-center text-muted py-lg",children:"暂无请求日志"})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"📖 API功能说明"}),e.jsxs("div",{className:"grid grid-2",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-md",children:"🔧 功能特性"}),e.jsxs("ul",{className:"text-sm text-secondary space-y-1",children:[e.jsxs("li",{children:["• ",e.jsx("strong",{children:"自动缓存"}),"：5分钟有效期，减少重复请求"]}),e.jsxs("li",{children:["• ",e.jsx("strong",{children:"加载状态"}),"：细粒度的加载状态管理"]}),e.jsxs("li",{children:["• ",e.jsx("strong",{children:"错误处理"}),"：统一的错误处理和展示"]}),e.jsxs("li",{children:["• ",e.jsx("strong",{children:"请求日志"}),"：详细的请求历史记录"]}),e.jsxs("li",{children:["• ",e.jsx("strong",{children:"CRUD操作"}),"：完整的增删改查功能"]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-md",children:"🌐 API端点"}),e.jsxs("div",{className:"text-sm text-secondary space-y-1",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"GET"})," /users - 获取用户列表"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"GET"})," /users/:id - 获取用户详情"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"POST"})," /users - 创建新用户"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"PUT"})," /users/:id - 更新用户"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"DELETE"})," /users/:id - 删除用户"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"GET"})," /users/:id/posts - 获取用户文章"]})]})]})]})]})]})}export{m as A};
//# sourceMappingURL=page-apiintegration-CR3Uz1sg.js.map
