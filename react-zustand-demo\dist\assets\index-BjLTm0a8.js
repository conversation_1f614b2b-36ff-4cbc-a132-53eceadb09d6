import{u as e,r as s,j as a,L as t,R as n,e as r,f as i,B as o}from"../chunks/react-vendor-CyNirxNk.js";import{u as c,a as l}from"../chunks/store-settingsstore-CkjrIQl7.js";import{H as d}from"../chunks/page-home-5NslTj78.js";import{B as m}from"../chunks/page-basicstore-ECzgDzuM.js";import{T as p}from"../chunks/page-todosmanagement-DW3Rbh2M.js";import{A as h}from"../chunks/page-apiintegration-CR3Uz1sg.js";import{S as u}from"../chunks/page-settingsdemo-B25qVxJ9.js";import{M as j}from"../chunks/page-middlewaredemo-CV2OZ4zC.js";import{A as f}from"../chunks/page-advancedfeatures-BaD2xKca.js";import"../chunks/store-basicstore-DgVIqOAD.js";import"../chunks/store-todosstore-DE5vS_Wk.js";import"../chunks/store-apistore-B0ecmVeH.js";function x(){const i=e(),o=c(l);s.useEffect(()=>{document.body.className=`theme-${o}`},[o]);return a.jsxs("div",{className:"app",children:[a.jsx("header",{className:"header",role:"banner",children:a.jsxs("div",{className:"container",children:[a.jsxs("h1",{className:"title",children:[a.jsx("span",{className:"icon",children:"🐻"}),"React + Zustand 全方向API演示"]}),a.jsx("nav",{className:"nav",role:"navigation","aria-label":"主导航",children:[{path:"/",label:"首页",icon:"🏠",description:"项目概览和导航"},{path:"/basic-store",label:"基础Store",icon:"📦",description:"Zustand基础概念演示"},{path:"/todos-management",label:"Todos管理",icon:"📝",description:"复杂列表状态管理"},{path:"/api-integration",label:"API集成",icon:"🌐",description:"异步操作和HTTP请求"},{path:"/settings-demo",label:"设置演示",icon:"⚙️",description:"状态持久化和本地存储"},{path:"/middleware-demo",label:"中间件演示",icon:"🔧",description:"自定义中间件功能"},{path:"/advanced-features",label:"高级特性",icon:"🚀",description:"Zustand高级功能演示"}].map(e=>a.jsxs(t,{to:e.path,className:"nav-link "+(i.pathname===e.path?"active":""),title:e.description,children:[a.jsx("span",{className:"nav-icon","aria-hidden":"true",children:e.icon}),a.jsx("span",{className:"nav-label",children:e.label})]},e.path))})]})}),a.jsx("main",{id:"main-content",className:"main",role:"main",children:a.jsx("div",{className:"container",children:a.jsxs(n,{children:[a.jsx(r,{path:"/",element:a.jsx(d,{})}),a.jsx(r,{path:"/basic-store",element:a.jsx(m,{})}),a.jsx(r,{path:"/todos-management",element:a.jsx(p,{})}),a.jsx(r,{path:"/api-integration",element:a.jsx(h,{})}),a.jsx(r,{path:"/settings-demo",element:a.jsx(u,{})}),a.jsx(r,{path:"/middleware-demo",element:a.jsx(j,{})}),a.jsx(r,{path:"/advanced-features",element:a.jsx(f,{})})]})})}),a.jsx("footer",{className:"footer",role:"contentinfo",children:a.jsxs("div",{className:"container",children:[a.jsx("p",{children:"🐻 Zustand State Management | 📊 Real-time State | 🚀 Performance First"}),a.jsx("p",{children:"Built with React + Zustand + Vite + JavaScript"})]})})]})}!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))s(e);new MutationObserver(e=>{for(const a of e)if("childList"===a.type)for(const e of a.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&s(e)}).observe(document,{childList:!0,subtree:!0})}function s(e){if(e.ep)return;e.ep=!0;const s=function(e){const s={};return e.integrity&&(s.integrity=e.integrity),e.referrerPolicy&&(s.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?s.credentials="include":"anonymous"===e.crossOrigin?s.credentials="omit":s.credentials="same-origin",s}(e);fetch(e.href,s)}}(),"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(e=>{}).catch(e=>{})}),i.createRoot(document.getElementById("root")).render(a.jsx(o,{children:a.jsx(x,{})}));
//# sourceMappingURL=index-BjLTm0a8.js.map
