{"name": "react-zustand-demo", "private": true, "version": "1.0.0", "type": "module", "description": "🐻 React + Zustand 全方向API演示项目 - 展示Zustand状态管理的各种功能和最佳实践", "keywords": ["react", "zustand", "state-management", "demo", "typescript", "vite"], "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"axios": "^1.10.0", "immer": "^10.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.7.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.31.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.31.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "terser": "^5.43.1", "vite": "^7.0.5", "vitest": "^3.2.4"}}