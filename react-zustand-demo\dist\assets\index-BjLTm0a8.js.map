{"version": 3, "file": "index-BjLTm0a8.js", "sources": ["../../src/App.jsx", "../../src/main.jsx"], "sourcesContent": ["import React, { useEffect } from 'react'\nimport { Routes, Route, Link, useLocation } from 'react-router-dom'\nimport { useSettingsStore, selectTheme } from '@/stores/settingsStore'\n\n// 直接导入页面组件（不使用懒加载）\nimport Home from '@/pages/Home'\nimport BasicStore from '@/pages/BasicStore'\nimport TodosManagement from '@/pages/TodosManagement'\nimport ApiIntegration from '@/pages/ApiIntegration'\nimport SettingsDemo from '@/pages/SettingsDemo'\nimport MiddlewareDemo from '@/pages/MiddlewareDemo'\nimport AdvancedFeatures from '@/pages/AdvancedFeatures'\n\nfunction App() {\n  const location = useLocation()\n  \n  // 获取主题设置\n  const theme = useSettingsStore(selectTheme)\n\n  // 应用主题类名到document.body\n  useEffect(() => {\n    document.body.className = `theme-${theme}`\n  }, [theme])\n\n  const navigationItems = [\n    { path: '/', label: '首页', icon: '🏠', description: '项目概览和导航' },\n    { path: '/basic-store', label: '基础Store', icon: '📦', description: 'Zustand基础概念演示' },\n    { path: '/todos-management', label: 'Todos管理', icon: '📝', description: '复杂列表状态管理' },\n    { path: '/api-integration', label: 'API集成', icon: '🌐', description: '异步操作和HTTP请求' },\n    { path: '/settings-demo', label: '设置演示', icon: '⚙️', description: '状态持久化和本地存储' },\n    { path: '/middleware-demo', label: '中间件演示', icon: '🔧', description: '自定义中间件功能' },\n    { path: '/advanced-features', label: '高级特性', icon: '🚀', description: 'Zustand高级功能演示' },\n  ]\n\n  return (\n    <div className=\"app\">\n      <header className=\"header\" role=\"banner\">\n        <div className=\"container\">\n          <h1 className=\"title\">\n            <span className=\"icon\">🐻</span>\n            React + Zustand 全方向API演示\n          </h1>\n          \n          <nav className=\"nav\" role=\"navigation\" aria-label=\"主导航\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.path}\n                to={item.path}\n                className={`nav-link ${location.pathname === item.path ? 'active' : ''}`}\n                title={item.description}\n              >\n                <span className=\"nav-icon\" aria-hidden=\"true\">{item.icon}</span>\n                <span className=\"nav-label\">{item.label}</span>\n              </Link>\n            ))}\n          </nav>\n        </div>\n      </header>\n\n      <main id=\"main-content\" className=\"main\" role=\"main\">\n        <div className=\"container\">\n          <Routes>\n            <Route path=\"/\" element={<Home />} />\n            <Route path=\"/basic-store\" element={<BasicStore />} />\n            <Route path=\"/todos-management\" element={<TodosManagement />} />\n            <Route path=\"/api-integration\" element={<ApiIntegration />} />\n            <Route path=\"/settings-demo\" element={<SettingsDemo />} />\n            <Route path=\"/middleware-demo\" element={<MiddlewareDemo />} />\n            <Route path=\"/advanced-features\" element={<AdvancedFeatures />} />\n          </Routes>\n        </div>\n      </main>\n\n      <footer className=\"footer\" role=\"contentinfo\">\n        <div className=\"container\">\n          <p>\n            🐻 Zustand State Management | 📊 Real-time State | 🚀 Performance First\n          </p>\n          <p>\n            Built with React + Zustand + Vite + JavaScript\n          </p>\n        </div>\n      </footer>\n    </div>\n  )\n}\n\nexport default App\n", "import React from 'react'\nimport ReactDOM from 'react-dom/client'\nimport { <PERSON>rowserRouter } from 'react-router-dom'\nimport App from '@/App.jsx'\nimport '@/styles/index.css'\n\n// 注册 Service Worker\nif ('serviceWorker' in navigator) {\n  window.addEventListener('load', () => {\n    navigator.serviceWorker.register('/sw.js')\n      .then((registration) => {\n        console.log('SW registered: ', registration)\n      })\n      .catch((registrationError) => {\n        console.log('SW registration failed: ', registrationError)\n      })\n  })\n}\n\nReactDOM.createRoot(document.getElementById('root')).render(\n  <BrowserRouter>\n    <App />\n  </BrowserRouter>\n)\n"], "names": ["App", "location", "useLocation", "theme", "useSettingsStore", "selectTheme", "useEffect", "document", "body", "className", "jsxs", "children", "jsx", "role", "path", "label", "icon", "description", "map", "item", "Link", "to", "pathname", "title", "id", "Routes", "Route", "element", "Home", "BasicStore", "TodosManagement", "ApiIntegration", "SettingsDemo", "MiddlewareDemo", "AdvancedFeatures", "navigator", "window", "addEventListener", "serviceWorker", "register", "then", "registration", "catch", "registrationError", "ReactDOM", "createRoot", "getElementById", "render", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "6tBAaA,SAASA,IACP,MAAMC,EAAWC,IAGXC,EAAQC,EAAiBC,GAG/BC,EAAAA,UAAU,KACRC,SAASC,KAAKC,UAAY,SAASN,KAClC,CAACA,IAYJ,SACEO,KAAC,MAAA,CAAID,UAAU,MACbE,SAAA,CAAAC,EAAAA,IAAC,SAAA,CAAOH,UAAU,SAASI,KAAK,SAC9BF,WAAAD,KAAC,MAAA,CAAID,UAAU,YACbE,SAAA,GAAAD,KAAC,KAAA,CAAGD,UAAU,QACZE,SAAA,CAAAC,EAAAA,IAAC,OAAA,CAAKH,UAAU,OAAOE,SAAA,OAAS,8BAIlCC,EAAAA,IAAC,MAAA,CAAIH,UAAU,MAAMI,KAAK,aAAa,aAAW,MAC/CF,SApBa,CACtB,CAAEG,KAAM,IAAKC,MAAO,KAAMC,KAAM,KAAMC,YAAa,WACnD,CAAEH,KAAM,eAAgBC,MAAO,UAAWC,KAAM,KAAMC,YAAa,iBACnE,CAAEH,KAAM,oBAAqBC,MAAO,UAAWC,KAAM,KAAMC,YAAa,YACxE,CAAEH,KAAM,mBAAoBC,MAAO,QAASC,KAAM,KAAMC,YAAa,eACrE,CAAEH,KAAM,iBAAkBC,MAAO,OAAQC,KAAM,KAAMC,YAAa,cAClE,CAAEH,KAAM,mBAAoBC,MAAO,QAASC,KAAM,KAAMC,YAAa,YACrE,CAAEH,KAAM,qBAAsBC,MAAO,OAAQC,KAAM,KAAMC,YAAa,kBAa7CC,IAAKC,GACpBT,EAAAA,KAACU,EAAA,CAECC,GAAIF,EAAKL,KACTL,UAAW,aAAYR,EAASqB,WAAaH,EAAKL,KAAO,SAAW,IACpES,MAAOJ,EAAKF,YAEZN,SAAA,CAAAC,MAAC,QAAKH,UAAU,WAAW,cAAY,OAAQE,WAAKK,OACpDJ,EAAAA,IAAC,OAAA,CAAKH,UAAU,YAAaE,WAAKI,UAN7BI,EAAKL,cAapBF,MAAC,OAAA,CAAKY,GAAG,eAAef,UAAU,OAAOI,KAAK,OAC5CF,WAAAC,IAAC,MAAA,CAAIH,UAAU,YACbE,gBAACc,EAAA,CACCd,SAAA,CAAAC,MAACc,GAAMZ,KAAK,IAAIa,QAASf,MAACgB,cACzBF,EAAA,CAAMZ,KAAK,eAAea,QAASf,MAACiB,cACpCH,EAAA,CAAMZ,KAAK,oBAAoBa,QAASf,MAACkB,cACzCJ,EAAA,CAAMZ,KAAK,mBAAmBa,QAASf,MAACmB,cACxCL,EAAA,CAAMZ,KAAK,iBAAiBa,QAASf,MAACoB,cACtCN,EAAA,CAAMZ,KAAK,mBAAmBa,QAASf,MAACqB,cACxCP,EAAA,CAAMZ,KAAK,qBAAqBa,QAASf,EAAAA,IAACsB,eAKjDtB,EAAAA,IAAC,UAAOH,UAAU,SAASI,KAAK,cAC9BF,WAAAD,KAAC,MAAA,CAAID,UAAU,YACbE,SAAA,GAAAC,IAAC,KAAED,SAAA,8EAGHC,IAAC,KAAED,SAAA,0DAOb,gsBC9EI,kBAAmBwB,WACrBC,OAAOC,iBAAiB,OAAQ,KAC9BF,UAAUG,cAAcC,SAAS,UAC9BC,KAAMC,OAGNC,MAAOC,SAMdC,EAASC,WAAWtC,SAASuC,eAAe,SAASC,OACnDnC,EAAAA,IAACoC,EAAA,CACCrC,SAAAC,EAAAA,IAACZ,EAAA,CAAA"}