import React, { useEffect } from 'react'
import { Routes, Route, Link, useLocation } from 'react-router-dom'
import { useSettingsStore, selectTheme } from '@/stores/settingsStore'

// 直接导入页面组件（不使用懒加载）
import Home from '@/pages/Home'
import BasicStore from '@/pages/BasicStore'
import TodosManagement from '@/pages/TodosManagement'
import ApiIntegration from '@/pages/ApiIntegration'
import SettingsDemo from '@/pages/SettingsDemo'
import MiddlewareDemo from '@/pages/MiddlewareDemo'
import AdvancedFeatures from '@/pages/AdvancedFeatures'

function App() {
  const location = useLocation()
  
  // 获取主题设置
  const theme = useSettingsStore(selectTheme)

  // 应用主题类名到document.body
  useEffect(() => {
    document.body.className = `theme-${theme}`
  }, [theme])

  const navigationItems = [
    { path: '/', label: '首页', icon: '🏠', description: '项目概览和导航' },
    { path: '/basic-store', label: '基础Store', icon: '📦', description: 'Zustand基础概念演示' },
    { path: '/todos-management', label: 'Todos管理', icon: '📝', description: '复杂列表状态管理' },
    { path: '/api-integration', label: 'API集成', icon: '🌐', description: '异步操作和HTTP请求' },
    { path: '/settings-demo', label: '设置演示', icon: '⚙️', description: '状态持久化和本地存储' },
    { path: '/middleware-demo', label: '中间件演示', icon: '🔧', description: '自定义中间件功能' },
    { path: '/advanced-features', label: '高级特性', icon: '🚀', description: 'Zustand高级功能演示' },
  ]

  return (
    <div className="app">
      <header className="header" role="banner">
        <div className="container">
          <h1 className="title">
            <span className="icon">🐻</span>
            React + Zustand 全方向API演示
          </h1>
          
          <nav className="nav" role="navigation" aria-label="主导航">
            {navigationItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`nav-link ${location.pathname === item.path ? 'active' : ''}`}
                title={item.description}
              >
                <span className="nav-icon" aria-hidden="true">{item.icon}</span>
                <span className="nav-label">{item.label}</span>
              </Link>
            ))}
          </nav>
        </div>
      </header>

      <main id="main-content" className="main" role="main">
        <div className="container">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/basic-store" element={<BasicStore />} />
            <Route path="/todos-management" element={<TodosManagement />} />
            <Route path="/api-integration" element={<ApiIntegration />} />
            <Route path="/settings-demo" element={<SettingsDemo />} />
            <Route path="/middleware-demo" element={<MiddlewareDemo />} />
            <Route path="/advanced-features" element={<AdvancedFeatures />} />
          </Routes>
        </div>
      </main>

      <footer className="footer" role="contentinfo">
        <div className="container">
          <p>
            🐻 Zustand State Management | 📊 Real-time State | 🚀 Performance First
          </p>
          <p>
            Built with React + Zustand + Vite + JavaScript
          </p>
        </div>
      </footer>
    </div>
  )
}

export default App
