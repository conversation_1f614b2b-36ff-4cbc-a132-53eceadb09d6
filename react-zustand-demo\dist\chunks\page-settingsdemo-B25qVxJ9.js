import{j as e}from"./react-vendor-CyNirxNk.js";import{u as s,a,s as l,c as t,d as c,e as n,f as r,g as i,h as d,i as m,j as o,k as x,l as h,m as j,n as b,b as N}from"./store-settingsstore-CkjrIQl7.js";function g(){const g=s(a),p=s(l),u=s(t),f=s(c),v=s(n),y=s(r),k=s(i),S=s(d),C=s(m),w=s(o),O=s(x),z=s(h),L=s(j),R=s(b),J=s(N),{setTheme:U,setLanguage:A,toggleNotifications:P,toggleAutoSave:T,setFontSize:E,setLayout:F,updatePreference:K,togglePreference:Z,toggleAdvancedSetting:$,updateShortcut:q,updateColor:B,batchUpdate:D,applyPreset:I,exportSettings:M,importSettings:V,saveSettings:W,resetToDefaults:G,reset:H}=s();return e.jsxs("div",{className:"settings-demo-page",children:[e.jsxs("div",{className:"card-header",children:[e.jsx("h1",{className:"card-title",children:"⚙️ 设置演示"}),e.jsx("p",{className:"card-description",children:"展示Zustand的persist中间件、设置管理、主题切换等功能"})]}),e.jsxs("div",{className:"card mb-lg",children:[e.jsx("h2",{className:"card-title",children:"📊 设置概览"}),e.jsxs("div",{className:"grid grid-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary",children:J.totalSettings}),e.jsx("div",{className:"text-sm text-secondary",children:"总设置数"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-success",children:J.modifiedSettings}),e.jsx("div",{className:"text-sm text-secondary",children:"已修改"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-warning",children:J.enabledFeatures}),e.jsx("div",{className:"text-sm text-secondary",children:"启用功能"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-info",children:Object.keys(C).length}),e.jsx("div",{className:"text-sm text-secondary",children:"快捷键"})]})]})]}),e.jsxs("div",{className:"grid grid-2",children:[e.jsxs("div",{className:"card",children:[e.jsx("h2",{className:"card-title",children:"🎨 基础设置"}),e.jsxs("div",{className:"form-group",children:[e.jsxs("label",{className:"form-label",children:["主题 (",O,")"]}),e.jsx("div",{className:"flex gap-sm",children:["light","dark","auto"].map(s=>e.jsx("button",{onClick:()=>U(s),className:"btn btn-sm "+(g===s?"btn-primary":"btn-secondary"),children:"light"===s?"🌞 浅色":"dark"===s?"🌙 深色":"🔄 自动"},s))})]}),e.jsxs("div",{className:"form-group",children:[e.jsxs("label",{className:"form-label",children:["语言 (",z,")"]}),e.jsxs("select",{value:p,onChange:e=>A(e.target.value),className:"form-control",children:[e.jsx("option",{value:"zh-CN",children:"中文简体"}),e.jsx("option",{value:"zh-TW",children:"中文繁體"}),e.jsx("option",{value:"en-US",children:"English"}),e.jsx("option",{value:"ja-JP",children:"日本語"}),e.jsx("option",{value:"ko-KR",children:"한국어"})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsxs("label",{className:"form-label",children:["字体大小: ",v,"px"]}),e.jsx("input",{type:"range",min:"12",max:"24",value:v,onChange:e=>E(parseInt(e.target.value)),className:"form-control"})]}),e.jsxs("div",{className:"form-group",children:[e.jsxs("label",{className:"form-label",children:["布局 (",L,")"]}),e.jsx("div",{className:"flex gap-sm",children:["sidebar","topbar","compact"].map(s=>e.jsx("button",{onClick:()=>F(s),className:"btn btn-sm "+(y===s?"btn-primary":"btn-secondary"),children:"sidebar"===s?"📋 侧边栏":"topbar"===s?"📄 顶部栏":"📱 紧凑"},s))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{children:"通知"}),e.jsx("input",{type:"checkbox",checked:u,onChange:P,className:"form-control w-auto"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{children:"自动保存"}),e.jsx("input",{type:"checkbox",checked:f,onChange:T,className:"form-control w-auto"})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("h2",{className:"card-title",children:"👤 个人偏好"}),e.jsx("div",{className:"space-y-2",children:Object.entries(k).map(([s,a])=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"capitalize",children:s.replace(/([A-Z])/g," $1").toLowerCase()}),e.jsx("input",{type:"checkbox",checked:a,onChange:()=>Z(s),className:"form-control w-auto"})]},s))}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"自定义偏好"}),e.jsxs("div",{className:"flex gap-sm",children:[e.jsx("input",{type:"text",placeholder:"偏好名称",onKeyPress:e=>{"Enter"===e.key&&e.target.value.trim()&&(K(e.target.value,!0),e.target.value="")},className:"form-control"}),e.jsx("button",{onClick:()=>{const e=document.querySelector('input[placeholder="偏好名称"]');e.value.trim()&&(K(e.value,!0),e.value="")},className:"btn btn-primary",children:"添加"})]})]})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"⚡ 高级设置"}),e.jsx("div",{className:"grid grid-3",children:Object.entries(S).map(([s,a])=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm",children:s.replace(/([A-Z])/g," $1").toLowerCase()}),e.jsx("input",{type:"checkbox",checked:a,onChange:()=>$(s),className:"form-control w-auto"})]},s))})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"🎨 颜色配置"}),e.jsx("div",{className:"grid grid-3",children:Object.entries(w).map(([s,a])=>e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:s}),e.jsxs("div",{className:"flex gap-sm items-center",children:[e.jsx("input",{type:"color",value:a,onChange:e=>B(s,e.target.value),className:"w-10 h-8 rounded border"}),e.jsx("input",{type:"text",value:a,onChange:e=>B(s,e.target.value),className:"form-control text-sm"})]})]},s))})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"⌨️ 快捷键"}),e.jsx("div",{className:"grid grid-2",children:Object.entries(C).map(([s,a])=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm",children:s}),e.jsx("input",{type:"text",value:a,onChange:e=>q(s,e.target.value),className:"form-control w-32 text-sm",placeholder:"按键组合"})]},s))})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"🔧 预设和操作"}),e.jsxs("div",{className:"mb-md",children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"主题预设"}),e.jsx("div",{className:"flex gap-sm",children:Object.entries({dark:{theme:"dark",fontSize:16,colorScheme:{primary:"#3b82f6",secondary:"#6b7280",background:"#1f2937"}},light:{theme:"light",fontSize:14,colorScheme:{primary:"#2563eb",secondary:"#374151",background:"#ffffff"}},accessibility:{fontSize:18,preferences:{highContrast:!0,reduceMotion:!0,screenReader:!0},advancedSettings:{enableKeyboardNavigation:!0,enableVoiceCommand:!0}}}).map(([s,a])=>e.jsx("button",{onClick:()=>I(s,a),className:"btn btn-info btn-sm",children:"dark"===s?"🌙 深色主题":"light"===s?"🌞 浅色主题":"♿ 无障碍"},s))})]}),e.jsxs("div",{className:"mb-md",children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"批量操作"}),e.jsxs("div",{className:"flex gap-sm",children:[e.jsx("button",{onClick:()=>D({fontSize:16,notifications:!0,autoSave:!0,theme:"auto"}),className:"btn btn-success btn-sm",children:"🔄 推荐设置"}),e.jsx("button",{onClick:()=>D({"preferences.animations":!1,"preferences.sounds":!1,"advancedSettings.enableCache":!1}),className:"btn btn-warning btn-sm",children:"⚡ 性能模式"})]})]}),e.jsxs("div",{className:"mb-md",children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"导入导出"}),e.jsxs("div",{className:"flex gap-sm",children:[e.jsx("button",{onClick:()=>{const e=M(),s=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),a=URL.createObjectURL(s),l=document.createElement("a");l.href=a,l.download="settings.json",l.click(),URL.revokeObjectURL(a)},className:"btn btn-primary btn-sm",children:"📤 导出设置"}),e.jsxs("label",{className:"btn btn-secondary btn-sm cursor-pointer",children:["📥 导入设置",e.jsx("input",{type:"file",accept:".json",onChange:e=>{const s=e.target.files[0];if(s){const e=new FileReader;e.onload=e=>{try{const s=JSON.parse(e.target.result);V(s),alert("设置导入成功！")}catch{alert("导入失败：无效的JSON格式")}},e.readAsText(s)}},className:"hidden"})]}),e.jsx("button",{onClick:W,className:"btn btn-success btn-sm",children:"💾 保存设置"})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"重置操作"}),e.jsxs("div",{className:"flex gap-sm",children:[e.jsx("button",{onClick:()=>{confirm("确定要重置为默认设置吗？")&&G()},className:"btn btn-warning btn-sm",children:"🔄 重置默认"}),e.jsx("button",{onClick:()=>{confirm("确定要清除所有设置吗？")&&H()},className:"btn btn-danger btn-sm",children:"🗑️ 清除所有"})]})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"👁️ 当前设置预览"}),e.jsxs("div",{className:"grid grid-2",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"基础设置"}),e.jsx("pre",{className:"bg-surface p-md rounded text-sm overflow-auto",children:JSON.stringify({theme:g,language:p,fontSize:v,layout:y,notifications:u,autoSave:f},null,2)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"完整配置"}),e.jsx("pre",{className:"bg-surface p-md rounded text-sm overflow-auto max-h-64",children:JSON.stringify(R,null,2)})]})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"💾 持久化功能"}),e.jsxs("div",{className:"text-sm text-secondary space-y-2",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"🔒 本地存储:"})," 设置自动保存到浏览器本地存储"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"🔄 自动同步:"})," 页面刷新后设置会自动恢复"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"📦 版本管理:"})," 支持设置版本迁移和兼容性"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"🎯 选择性持久化:"})," 只有重要设置会被持久化"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"⚡ 性能优化:"})," 使用防抖策略减少存储操作"]})]})]})]})}export{g as S};
//# sourceMappingURL=page-settingsdemo-B25qVxJ9.js.map
