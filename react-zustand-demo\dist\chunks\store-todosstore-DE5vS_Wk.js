import{c as e,d as t,s as o,i as d}from"./react-vendor-CyNirxNk.js";const s=e(t(o(d((e,t)=>({todos:[],filter:"all",editingId:null,bulkSelectMode:!1,selectedIds:[],nextId:1,addTodo:t=>e(e=>{const o={id:e.nextId++,text:"string"==typeof t?t:t.text,completed:!1,createdAt:(new Date).toISOString(),priority:t?.priority||"normal",category:t?.category||"general",tags:t?.tags||[]};e.todos.push(o)}),toggleTodo:t=>e(e=>{const o=e.todos.find(e=>e.id===t);o&&(o.completed=!o.completed,o.completedAt=o.completed?(new Date).toISOString():null)}),removeTodo:t=>e(e=>{e.todos=e.todos.filter(e=>e.id!==t),e.selectedIds=e.selectedIds.filter(e=>e!==t)}),updateTodoText:(t,o)=>e(e=>{const d=e.todos.find(e=>e.id===t);d&&o.trim()&&(d.text=o.trim(),d.updatedAt=(new Date).toISOString())}),setTodoPriority:(t,o)=>e(e=>{const d=e.todos.find(e=>e.id===t);d&&(d.priority=o,d.updatedAt=(new Date).toISOString())}),setTodoCategory:(t,o)=>e(e=>{const d=e.todos.find(e=>e.id===t);d&&(d.category=o,d.updatedAt=(new Date).toISOString())}),toggleTodoTag:(t,o)=>e(e=>{const d=e.todos.find(e=>e.id===t);if(d){const e=d.tags.indexOf(o);e>-1?d.tags.splice(e,1):d.tags.push(o),d.updatedAt=(new Date).toISOString()}}),setFilter:t=>e(e=>{e.filter=t}),toggleAll:()=>e(e=>{const t=e.todos.length>0&&e.todos.every(e=>e.completed);e.todos.forEach(e=>{e.completed=!t,e.completedAt=t?null:(new Date).toISOString()})}),clearCompleted:()=>e(e=>{e.todos=e.todos.filter(e=>!e.completed);const t=e.todos.map(e=>e.id);e.selectedIds=e.selectedIds.filter(e=>t.includes(e))}),startEditing:t=>e(e=>{e.editingId=t}),stopEditing:()=>e(e=>{e.editingId=null}),toggleBulkSelectMode:()=>e(e=>{e.bulkSelectMode=!e.bulkSelectMode,e.bulkSelectMode||(e.selectedIds=[])}),toggleTodoSelection:t=>e(e=>{const o=e.selectedIds.indexOf(t);o>-1?e.selectedIds.splice(o,1):e.selectedIds.push(t)}),toggleSelectAll:()=>e(e=>{const o=t().getFilteredTodos();if(o.length>0&&o.every(t=>e.selectedIds.includes(t.id))){const t=o.map(e=>e.id);e.selectedIds=e.selectedIds.filter(e=>!t.includes(e))}else{o.map(e=>e.id).forEach(t=>{e.selectedIds.includes(t)||e.selectedIds.push(t)})}}),deleteSelected:()=>e(e=>{e.todos=e.todos.filter(t=>!e.selectedIds.includes(t.id)),e.selectedIds=[]}),markSelectedAsCompleted:(t=!0)=>e(e=>{e.todos.forEach(o=>{e.selectedIds.includes(o.id)&&(o.completed=t,o.completedAt=t?(new Date).toISOString():null)})}),sortByPriority:()=>e(e=>{const t={high:3,normal:2,low:1};e.todos.sort((e,o)=>t[o.priority]-t[e.priority])}),sortByCreatedTime:(t=!0)=>e(e=>{e.todos.sort((e,o)=>{const d=new Date(e.createdAt).getTime(),s=new Date(o.createdAt).getTime();return t?d-s:s-d})}),addBatchTodos:t=>e(e=>{t.split("\n").map(e=>e.trim()).filter(e=>e.length>0).forEach(t=>{e.todos.push({id:e.nextId++,text:t,completed:!1,createdAt:(new Date).toISOString(),priority:"normal",category:"batch",tags:[]})})}),reset:()=>e(e=>{e.todos=[],e.filter="all",e.editingId=null,e.bulkSelectMode=!1,e.selectedIds=[],e.nextId=1}),getFilteredTodos:()=>{const{todos:e,filter:o}=t();switch(o){case"active":return e.filter(e=>!e.completed);case"completed":return e.filter(e=>e.completed);default:return e}},getTodosByCategory:e=>t().todos.filter(t=>t.category===e),getTodosByPriority:e=>t().todos.filter(t=>t.priority===e),getTodosByTag:e=>t().todos.filter(t=>t.tags.includes(e)),getStats:()=>{const{todos:e}=t(),o=e.filter(e=>e.completed).length,d=e.length;return{total:d,completed:o,active:d-o,completionRate:d>0?Math.round(o/d*100):0,categories:[...new Set(e.map(e=>e.category))],priorities:{high:e.filter(e=>"high"===e.priority).length,normal:e.filter(e=>"normal"===e.priority).length,low:e.filter(e=>"low"===e.priority).length},allTags:[...new Set(e.flatMap(e=>e.tags))]}},getSelectedStats:()=>{const{selectedIds:e,todos:o}=t(),d=o.filter(t=>e.includes(t.id)),s=d.filter(e=>e.completed).length;return{total:d.length,completed:s,active:d.length-s}}}))))),l=e=>e.todos,i=e=>e.filter,r=e=>e.getFilteredTodos(),c=e=>e.editingId,n=e=>e.bulkSelectMode,a=e=>e.selectedIds,g=e=>e.getStats();export{r as a,g as b,l as c,c as d,n as e,a as f,i as s,s as u};
//# sourceMappingURL=store-todosstore-DE5vS_Wk.js.map
