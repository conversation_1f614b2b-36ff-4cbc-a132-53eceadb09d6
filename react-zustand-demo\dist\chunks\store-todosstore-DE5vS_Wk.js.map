{"version": 3, "file": "store-todosstore-DE5vS_Wk.js", "sources": ["../../src/stores/todosStore.js"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools, subscribeWithSelector } from 'zustand/middleware'\r\nimport { immer } from 'zustand/middleware/immer'\r\n\r\n// Todos store - 展示复杂列表状态管理\r\nexport const useTodosStore = create(\r\n  devtools(\r\n    subscribeWithSelector(\r\n      immer((set, get) => ({\r\n        // 状态\r\n        todos: [],\r\n        filter: 'all', // all, active, completed\r\n        editingId: null,\r\n        bulkSelectMode: false,\r\n        selectedIds: [],\r\n        nextId: 1,\r\n\r\n        // 添加新todo\r\n        addTodo: (todoData) => set((state) => {\r\n          const todo = {\r\n            id: state.nextId++,\r\n            text: typeof todoData === 'string' ? todoData : todoData.text,\r\n            completed: false,\r\n            createdAt: new Date().toISOString(),\r\n            priority: todoData?.priority || 'normal', // low, normal, high\r\n            category: todoData?.category || 'general',\r\n            tags: todoData?.tags || [],\r\n          }\r\n          state.todos.push(todo)\r\n        }),\r\n\r\n        // 切换todo完成状态\r\n        toggleTodo: (id) => set((state) => {\r\n          const todo = state.todos.find(t => t.id === id)\r\n          if (todo) {\r\n            todo.completed = !todo.completed\r\n            todo.completedAt = todo.completed ? new Date().toISOString() : null\r\n          }\r\n        }),\r\n\r\n        // 删除todo\r\n        removeTodo: (id) => set((state) => {\r\n          state.todos = state.todos.filter(todo => todo.id !== id)\r\n          // 同时从选中列表中移除\r\n          state.selectedIds = state.selectedIds.filter(selectedId => selectedId !== id)\r\n        }),\r\n\r\n        // 更新todo文本\r\n        updateTodoText: (id, newText) => set((state) => {\r\n          const todo = state.todos.find(t => t.id === id)\r\n          if (todo && newText.trim()) {\r\n            todo.text = newText.trim()\r\n            todo.updatedAt = new Date().toISOString()\r\n          }\r\n        }),\r\n\r\n        // 设置todo优先级\r\n        setTodoPriority: (id, priority) => set((state) => {\r\n          const todo = state.todos.find(t => t.id === id)\r\n          if (todo) {\r\n            todo.priority = priority\r\n            todo.updatedAt = new Date().toISOString()\r\n          }\r\n        }),\r\n\r\n        // 设置todo分类\r\n        setTodoCategory: (id, category) => set((state) => {\r\n          const todo = state.todos.find(t => t.id === id)\r\n          if (todo) {\r\n            todo.category = category\r\n            todo.updatedAt = new Date().toISOString()\r\n          }\r\n        }),\r\n\r\n        // 添加/移除标签\r\n        toggleTodoTag: (id, tag) => set((state) => {\r\n          const todo = state.todos.find(t => t.id === id)\r\n          if (todo) {\r\n            const tagIndex = todo.tags.indexOf(tag)\r\n            if (tagIndex > -1) {\r\n              todo.tags.splice(tagIndex, 1)\r\n            } else {\r\n              todo.tags.push(tag)\r\n            }\r\n            todo.updatedAt = new Date().toISOString()\r\n          }\r\n        }),\r\n\r\n        // 设置过滤器\r\n        setFilter: (filter) => set((state) => {\r\n          state.filter = filter\r\n        }),\r\n\r\n        // 全选/取消全选\r\n        toggleAll: () => set((state) => {\r\n          const allCompleted = state.todos.length > 0 && \r\n            state.todos.every(todo => todo.completed)\r\n          \r\n          state.todos.forEach(todo => {\r\n            todo.completed = !allCompleted\r\n            todo.completedAt = !allCompleted ? new Date().toISOString() : null\r\n          })\r\n        }),\r\n\r\n        // 清除已完成的todos\r\n        clearCompleted: () => set((state) => {\r\n          state.todos = state.todos.filter(todo => !todo.completed)\r\n          // 清除选中状态中已删除的项目\r\n          const remainingIds = state.todos.map(todo => todo.id)\r\n          state.selectedIds = state.selectedIds.filter(id => remainingIds.includes(id))\r\n        }),\r\n\r\n        // 编辑状态管理\r\n        startEditing: (id) => set((state) => {\r\n          state.editingId = id\r\n        }),\r\n\r\n        stopEditing: () => set((state) => {\r\n          state.editingId = null\r\n        }),\r\n\r\n        // 批量选择模式\r\n        toggleBulkSelectMode: () => set((state) => {\r\n          state.bulkSelectMode = !state.bulkSelectMode\r\n          if (!state.bulkSelectMode) {\r\n            state.selectedIds = []\r\n          }\r\n        }),\r\n\r\n        // 切换todo选中状态\r\n        toggleTodoSelection: (id) => set((state) => {\r\n          const index = state.selectedIds.indexOf(id)\r\n          if (index > -1) {\r\n            state.selectedIds.splice(index, 1)\r\n          } else {\r\n            state.selectedIds.push(id)\r\n          }\r\n        }),\r\n\r\n        // 全选/取消全选\r\n        toggleSelectAll: () => set((state) => {\r\n          const filteredTodos = get().getFilteredTodos()\r\n          const allSelected = filteredTodos.length > 0 && \r\n            filteredTodos.every(todo => state.selectedIds.includes(todo.id))\r\n          \r\n          if (allSelected) {\r\n            // 取消全选 - 从selectedIds中移除当前筛选的todos\r\n            const filteredIds = filteredTodos.map(todo => todo.id)\r\n            state.selectedIds = state.selectedIds.filter(id => !filteredIds.includes(id))\r\n          } else {\r\n            // 全选 - 添加当前筛选的todos到selectedIds\r\n            const filteredIds = filteredTodos.map(todo => todo.id)\r\n            filteredIds.forEach(id => {\r\n              if (!state.selectedIds.includes(id)) {\r\n                state.selectedIds.push(id)\r\n              }\r\n            })\r\n          }\r\n        }),\r\n\r\n        // 批量删除选中的todos\r\n        deleteSelected: () => set((state) => {\r\n          state.todos = state.todos.filter(todo => !state.selectedIds.includes(todo.id))\r\n          state.selectedIds = []\r\n        }),\r\n\r\n        // 批量标记选中的todos为完成/未完成\r\n        markSelectedAsCompleted: (completed = true) => set((state) => {\r\n          state.todos.forEach(todo => {\r\n            if (state.selectedIds.includes(todo.id)) {\r\n              todo.completed = completed\r\n              todo.completedAt = completed ? new Date().toISOString() : null\r\n            }\r\n          })\r\n        }),\r\n\r\n        // 按优先级排序\r\n        sortByPriority: () => set((state) => {\r\n          const priorityOrder = { high: 3, normal: 2, low: 1 }\r\n          state.todos.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority])\r\n        }),\r\n\r\n        // 按创建时间排序\r\n        sortByCreatedTime: (ascending = true) => set((state) => {\r\n          state.todos.sort((a, b) => {\r\n            const timeA = new Date(a.createdAt).getTime()\r\n            const timeB = new Date(b.createdAt).getTime()\r\n            return ascending ? timeA - timeB : timeB - timeA\r\n          })\r\n        }),\r\n\r\n        // 批量添加todos\r\n        addBatchTodos: (todosText) => set((state) => {\r\n          const lines = todosText.split('\\n')\r\n            .map(line => line.trim())\r\n            .filter(line => line.length > 0)\r\n          \r\n          lines.forEach(text => {\r\n            state.todos.push({\r\n              id: state.nextId++,\r\n              text,\r\n              completed: false,\r\n              createdAt: new Date().toISOString(),\r\n              priority: 'normal',\r\n              category: 'batch',\r\n              tags: []\r\n            })\r\n          })\r\n        }),\r\n\r\n        // 重置store\r\n        reset: () => set((state) => {\r\n          state.todos = []\r\n          state.filter = 'all'\r\n          state.editingId = null\r\n          state.bulkSelectMode = false\r\n          state.selectedIds = []\r\n          state.nextId = 1\r\n        }),\r\n\r\n        // 计算属性\r\n        getFilteredTodos: () => {\r\n          const { todos, filter } = get()\r\n          switch (filter) {\r\n            case 'active':\r\n              return todos.filter(todo => !todo.completed)\r\n            case 'completed':\r\n              return todos.filter(todo => todo.completed)\r\n            default:\r\n              return todos\r\n          }\r\n        },\r\n\r\n        getTodosByCategory: (category) => {\r\n          return get().todos.filter(todo => todo.category === category)\r\n        },\r\n\r\n        getTodosByPriority: (priority) => {\r\n          return get().todos.filter(todo => todo.priority === priority)\r\n        },\r\n\r\n        getTodosByTag: (tag) => {\r\n          return get().todos.filter(todo => todo.tags.includes(tag))\r\n        },\r\n\r\n        getStats: () => {\r\n          const { todos } = get()\r\n          const completed = todos.filter(todo => todo.completed).length\r\n          const total = todos.length\r\n          \r\n          return {\r\n            total,\r\n            completed,\r\n            active: total - completed,\r\n            completionRate: total > 0 ? Math.round((completed / total) * 100) : 0,\r\n            categories: [...new Set(todos.map(todo => todo.category))],\r\n            priorities: {\r\n              high: todos.filter(todo => todo.priority === 'high').length,\r\n              normal: todos.filter(todo => todo.priority === 'normal').length,\r\n              low: todos.filter(todo => todo.priority === 'low').length,\r\n            },\r\n            allTags: [...new Set(todos.flatMap(todo => todo.tags))]\r\n          }\r\n        },\r\n\r\n        getSelectedStats: () => {\r\n          const { selectedIds, todos } = get()\r\n          const selectedTodos = todos.filter(todo => selectedIds.includes(todo.id))\r\n          const completed = selectedTodos.filter(todo => todo.completed).length\r\n          \r\n          return {\r\n            total: selectedTodos.length,\r\n            completed,\r\n            active: selectedTodos.length - completed,\r\n          }\r\n        }\r\n      })),\r\n      { name: 'todos-store' }\r\n    )\r\n  )\r\n)\r\n\r\n// Selectors\r\nexport const selectTodos = (state) => state.todos\r\nexport const selectFilter = (state) => state.filter\r\nexport const selectFilteredTodos = (state) => state.getFilteredTodos()\r\nexport const selectEditingId = (state) => state.editingId\r\nexport const selectBulkSelectMode = (state) => state.bulkSelectMode\r\nexport const selectSelectedIds = (state) => state.selectedIds\r\nexport const selectTodoStats = (state) => state.getStats()\r\nexport const selectSelectedStats = (state) => state.getSelectedStats()\r\n\r\n// 按分类的选择器\r\nexport const selectTodosByCategory = (category) => (state) => state.getTodosByCategory(category)\r\nexport const selectTodosByPriority = (priority) => (state) => state.getTodosByPriority(priority)\r\nexport const selectTodosByTag = (tag) => (state) => state.getTodosByTag(tag)\r\n\r\n// 复合选择器\r\nexport const selectActiveTodos = (state) => state.todos.filter(todo => !todo.completed)\r\nexport const selectCompletedTodos = (state) => state.todos.filter(todo => todo.completed)\r\nexport const selectHighPriorityTodos = (state) => state.todos.filter(todo => todo.priority === 'high')\r\n\r\n// 订阅示例\r\nexport const subscribeToTodosChange = (callback) => {\r\n  return useTodosStore.subscribe(\r\n    (state) => state.todos,\r\n    callback\r\n  )\r\n}\r\n\r\nexport const subscribeToFilterChange = (callback) => {\r\n  return useTodosStore.subscribe(\r\n    (state) => state.filter,\r\n    callback\r\n  )\r\n} "], "names": ["useTodosStore", "create", "devtools", "subscribeWithSelector", "immer", "set", "get", "todos", "filter", "editingId", "bulkSelectMode", "selectedIds", "nextId", "addTodo", "todoData", "state", "todo", "id", "text", "completed", "createdAt", "Date", "toISOString", "priority", "category", "tags", "push", "toggleTodo", "find", "t", "completedAt", "removeTodo", "selectedId", "updateTodoText", "newText", "trim", "updatedAt", "setTodoPriority", "setTodoCategory", "toggleTodoTag", "tag", "tagIndex", "indexOf", "splice", "setFilter", "toggleAll", "allCompleted", "length", "every", "for<PERSON>ach", "clearCompleted", "remainingIds", "map", "includes", "startEditing", "stopEditing", "toggleBulkSelectMode", "toggleTodoSelection", "index", "toggleSelectAll", "filteredTodos", "getFilteredTodos", "filteredIds", "deleteSelected", "markSelectedAsCompleted", "sortByPriority", "priorityOrder", "high", "normal", "low", "sort", "a", "b", "sortByCreatedTime", "ascending", "timeA", "getTime", "timeB", "addBatchTodos", "todosText", "split", "line", "reset", "getTodosByCategory", "getTodosByPriority", "getTodosByTag", "getStats", "total", "active", "completionRate", "Math", "round", "categories", "Set", "priorities", "allTags", "flatMap", "getSelectedStats", "<PERSON><PERSON><PERSON><PERSON>", "selectTodos", "selectFilter", "selectFilteredTodos", "selectEditingId", "selectBulkSelectMode", "selectSelectedIds", "selectTodoStats"], "mappings": "oEAKY,MAACA,EAAgBC,EAC3BC,EACEC,EACEC,EAAM,CAACC,EAAKC,KAAA,CAEVC,MAAO,GACPC,OAAQ,MACRC,UAAW,KACXC,gBAAgB,EAChBC,YAAa,GACbC,OAAQ,EAGRC,QAAUC,GAAaT,EAAKU,IAC1B,MAAMC,EAAO,CACXC,GAAIF,EAAMH,SACVM,KAA0B,iBAAbJ,EAAwBA,EAAWA,EAASI,KACzDC,WAAW,EACXC,WAAA,IAAeC,MAAOC,cACtBC,SAAUT,GAAUS,UAAY,SAChCC,SAAUV,GAAUU,UAAY,UAChCC,KAAMX,GAAUW,MAAQ,IAE1BV,EAAMR,MAAMmB,KAAKV,KAInBW,WAAaV,GAAOZ,EAAKU,IACvB,MAAMC,EAAOD,EAAMR,MAAMqB,KAAKC,GAAKA,EAAEZ,KAAOA,GACxCD,IACFA,EAAKG,WAAaH,EAAKG,UACvBH,EAAKc,YAAcd,EAAKG,WAAA,IAAgBE,MAAOC,cAAgB,QAKnES,WAAad,GAAOZ,EAAKU,IACvBA,EAAMR,MAAQQ,EAAMR,MAAMC,OAAOQ,GAAQA,EAAKC,KAAOA,GAErDF,EAAMJ,YAAcI,EAAMJ,YAAYH,OAAOwB,GAAcA,IAAef,KAI5EgB,eAAgB,CAAChB,EAAIiB,IAAY7B,EAAKU,IACpC,MAAMC,EAAOD,EAAMR,MAAMqB,KAAKC,GAAKA,EAAEZ,KAAOA,GACxCD,GAAQkB,EAAQC,SAClBnB,EAAKE,KAAOgB,EAAQC,OACpBnB,EAAKoB,WAAA,IAAgBf,MAAOC,iBAKhCe,gBAAiB,CAACpB,EAAIM,IAAalB,EAAKU,IACtC,MAAMC,EAAOD,EAAMR,MAAMqB,KAAKC,GAAKA,EAAEZ,KAAOA,GACxCD,IACFA,EAAKO,SAAWA,EAChBP,EAAKoB,WAAA,IAAgBf,MAAOC,iBAKhCgB,gBAAiB,CAACrB,EAAIO,IAAanB,EAAKU,IACtC,MAAMC,EAAOD,EAAMR,MAAMqB,KAAKC,GAAKA,EAAEZ,KAAOA,GACxCD,IACFA,EAAKQ,SAAWA,EAChBR,EAAKoB,WAAA,IAAgBf,MAAOC,iBAKhCiB,cAAe,CAACtB,EAAIuB,IAAQnC,EAAKU,IAC/B,MAAMC,EAAOD,EAAMR,MAAMqB,KAAKC,GAAKA,EAAEZ,KAAOA,GAC5C,GAAID,EAAM,CACR,MAAMyB,EAAWzB,EAAKS,KAAKiB,QAAQF,GAC/BC,GAAW,EACbzB,EAAKS,KAAKkB,OAAOF,EAAU,GAE3BzB,EAAKS,KAAKC,KAAKc,GAEjBxB,EAAKoB,WAAA,IAAgBf,MAAOC,aAC9B,IAIFsB,UAAYpC,GAAWH,EAAKU,IAC1BA,EAAMP,OAASA,IAIjBqC,UAAW,IAAMxC,EAAKU,IACpB,MAAM+B,EAAe/B,EAAMR,MAAMwC,OAAS,GACxChC,EAAMR,MAAMyC,MAAMhC,GAAQA,EAAKG,WAEjCJ,EAAMR,MAAM0C,QAAQjC,IAClBA,EAAKG,WAAa2B,EAClB9B,EAAKc,YAAegB,EAA0C,MAA1C,IAAmBzB,MAAOC,kBAKlD4B,eAAgB,IAAM7C,EAAKU,IACzBA,EAAMR,MAAQQ,EAAMR,MAAMC,OAAOQ,IAASA,EAAKG,WAE/C,MAAMgC,EAAepC,EAAMR,MAAM6C,IAAIpC,GAAQA,EAAKC,IAClDF,EAAMJ,YAAcI,EAAMJ,YAAYH,UAAa2C,EAAaE,SAASpC,MAI3EqC,aAAerC,GAAOZ,EAAKU,IACzBA,EAAMN,UAAYQ,IAGpBsC,YAAa,IAAMlD,EAAKU,IACtBA,EAAMN,UAAY,OAIpB+C,qBAAsB,IAAMnD,EAAKU,IAC/BA,EAAML,gBAAkBK,EAAML,eACzBK,EAAML,iBACTK,EAAMJ,YAAc,MAKxB8C,oBAAsBxC,GAAOZ,EAAKU,IAChC,MAAM2C,EAAQ3C,EAAMJ,YAAY+B,QAAQzB,GACpCyC,GAAQ,EACV3C,EAAMJ,YAAYgC,OAAOe,EAAO,GAEhC3C,EAAMJ,YAAYe,KAAKT,KAK3B0C,gBAAiB,IAAMtD,EAAKU,IAC1B,MAAM6C,EAAgBtD,IAAMuD,mBAI5B,GAHoBD,EAAcb,OAAS,GACzCa,EAAcZ,MAAMhC,GAAQD,EAAMJ,YAAY0C,SAASrC,EAAKC,KAE7C,CAEf,MAAM6C,EAAcF,EAAcR,IAAIpC,GAAQA,EAAKC,IACnDF,EAAMJ,YAAcI,EAAMJ,YAAYH,WAAcsD,EAAYT,SAASpC,GAC3E,KAAO,CAEe2C,EAAcR,IAAIpC,GAAQA,EAAKC,IACvCgC,QAAQhC,IACbF,EAAMJ,YAAY0C,SAASpC,IAC9BF,EAAMJ,YAAYe,KAAKT,IAG7B,IAIF8C,eAAgB,IAAM1D,EAAKU,IACzBA,EAAMR,MAAQQ,EAAMR,MAAMC,OAAOQ,IAASD,EAAMJ,YAAY0C,SAASrC,EAAKC,KAC1EF,EAAMJ,YAAc,KAItBqD,wBAAyB,CAAC7C,GAAY,IAASd,EAAKU,IAClDA,EAAMR,MAAM0C,QAAQjC,IACdD,EAAMJ,YAAY0C,SAASrC,EAAKC,MAClCD,EAAKG,UAAYA,EACjBH,EAAKc,YAAcX,GAAA,IAAgBE,MAAOC,cAAgB,UAMhE2C,eAAgB,IAAM5D,EAAKU,IACzB,MAAMmD,EAAgB,CAAEC,KAAM,EAAGC,OAAQ,EAAGC,IAAK,GACjDtD,EAAMR,MAAM+D,KAAK,CAACC,EAAGC,IAAMN,EAAcM,EAAEjD,UAAY2C,EAAcK,EAAEhD,aAIzEkD,kBAAmB,CAACC,GAAY,IAASrE,EAAKU,IAC5CA,EAAMR,MAAM+D,KAAK,CAACC,EAAGC,KACnB,MAAMG,EAAQ,IAAItD,KAAKkD,EAAEnD,WAAWwD,UAC9BC,EAAQ,IAAIxD,KAAKmD,EAAEpD,WAAWwD,UACpC,OAAOF,EAAYC,EAAQE,EAAQA,EAAQF,MAK/CG,cAAgBC,GAAc1E,EAAKU,IACnBgE,EAAUC,MAAM,MAC3B5B,IAAI6B,GAAQA,EAAK9C,QACjB3B,OAAOyE,GAAQA,EAAKlC,OAAS,GAE1BE,QAAQ/B,IACZH,EAAMR,MAAMmB,KAAK,CACfT,GAAIF,EAAMH,SACVM,OACAC,WAAW,EACXC,WAAA,IAAeC,MAAOC,cACtBC,SAAU,SACVC,SAAU,QACVC,KAAM,SAMZyD,MAAO,IAAM7E,EAAKU,IAChBA,EAAMR,MAAQ,GACdQ,EAAMP,OAAS,MACfO,EAAMN,UAAY,KAClBM,EAAML,gBAAiB,EACvBK,EAAMJ,YAAc,GACpBI,EAAMH,OAAS,IAIjBiD,iBAAkB,KAChB,MAAMtD,MAAEA,EAAAC,OAAOA,GAAWF,IAC1B,OAAQE,GACN,IAAK,SACH,OAAOD,EAAMC,OAAOQ,IAASA,EAAKG,WACpC,IAAK,YACH,OAAOZ,EAAMC,OAAOQ,GAAQA,EAAKG,WACnC,QACE,OAAOZ,IAIb4E,mBAAqB3D,GACZlB,IAAMC,MAAMC,OAAOQ,GAAQA,EAAKQ,WAAaA,GAGtD4D,mBAAqB7D,GACZjB,IAAMC,MAAMC,OAAOQ,GAAQA,EAAKO,WAAaA,GAGtD8D,cAAgB7C,GACPlC,IAAMC,MAAMC,UAAeQ,EAAKS,KAAK4B,SAASb,IAGvD8C,SAAU,KACR,MAAM/E,MAAEA,GAAUD,IACZa,EAAYZ,EAAMC,OAAOQ,GAAQA,EAAKG,WAAW4B,OACjDwC,EAAQhF,EAAMwC,OAEpB,MAAO,CACLwC,QACApE,YACAqE,OAAQD,EAAQpE,EAChBsE,eAAgBF,EAAQ,EAAIG,KAAKC,MAAOxE,EAAYoE,EAAS,KAAO,EACpEK,WAAY,IAAI,IAAIC,IAAItF,EAAM6C,IAAIpC,GAAQA,EAAKQ,YAC/CsE,WAAY,CACV3B,KAAM5D,EAAMC,UAAiC,SAAlBQ,EAAKO,UAAqBwB,OACrDqB,OAAQ7D,EAAMC,UAAiC,WAAlBQ,EAAKO,UAAuBwB,OACzDsB,IAAK9D,EAAMC,UAAiC,QAAlBQ,EAAKO,UAAoBwB,QAErDgD,QAAS,IAAI,IAAIF,IAAItF,EAAMyF,QAAQhF,GAAQA,EAAKS,UAIpDwE,iBAAkB,KAChB,MAAMtF,YAAEA,EAAAJ,MAAaA,GAAUD,IACzB4F,EAAgB3F,EAAMC,OAAOQ,GAAQL,EAAY0C,SAASrC,EAAKC,KAC/DE,EAAY+E,EAAc1F,OAAOQ,GAAQA,EAAKG,WAAW4B,OAE/D,MAAO,CACLwC,MAAOW,EAAcnD,OACrB5B,YACAqE,OAAQU,EAAcnD,OAAS5B,UAU9BgF,EAAepF,GAAUA,EAAMR,MAC/B6F,EAAgBrF,GAAUA,EAAMP,OAChC6F,EAAuBtF,GAAUA,EAAM8C,mBACvCyC,EAAmBvF,GAAUA,EAAMN,UACnC8F,EAAwBxF,GAAUA,EAAML,eACxC8F,EAAqBzF,GAAUA,EAAMJ,YACrC8F,EAAmB1F,GAAUA,EAAMuE"}