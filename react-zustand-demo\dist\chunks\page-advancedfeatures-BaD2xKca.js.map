{"version": 3, "file": "page-advancedfeatures-BaD2xKca.js", "sources": ["../../src/pages/AdvancedFeatures.jsx"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react'\r\nimport { useBasicStore, selectCount, selectName, selectItems, selectStats, selectDoubleCount, selectTripleCount } from '@/stores/basicStore'\r\nimport { useTodosStore, selectTodos, selectFilter, selectFilteredTodos, selectTodoStats } from '@/stores/todosStore'\r\nimport { useSettingsStore, selectTheme, selectLanguage } from '@/stores/settingsStore'\r\nimport { useApiStore, selectUsers, selectStats as selectApiStats } from '@/stores/apiStore'\r\n\r\n// 自定义Hook示例\r\nfunction useMultiStoreData() {\r\n  return {\r\n    basicStats: useBasicStore(selectStats),\r\n    todosStats: useTodosStore(selectTodoStats),\r\n    apiStats: useApiStore(selectApiStats),\r\n    theme: useSettingsStore(selectTheme),\r\n    language: useSettingsStore(selectLanguage)\r\n  }\r\n}\r\n\r\n// 性能优化Hook\r\nfunction useOptimizedSelector() {\r\n  const count = useBasicStore(selectCount)\r\n  const doubleCount = useBasicStore(selectDoubleCount)\r\n  const tripleCount = useBasicStore(selectTripleCount)\r\n  \r\n  // 使用useMemo优化计算\r\n  const expensiveCalculation = useMemo(() => {\r\n    console.log('执行昂贵计算...')\r\n    return count * doubleCount * tripleCount + Math.random() * 1000\r\n  }, [count, doubleCount, tripleCount])\r\n  \r\n  return { count, doubleCount, tripleCount, expensiveCalculation }\r\n}\r\n\r\n// 状态同步Hook\r\nfunction useStateSynchronization() {\r\n  const [syncEnabled, setSyncEnabled] = useState(false)\r\n  const count = useBasicStore(selectCount)\r\n  const todosCount = useTodosStore(selectTodos).length\r\n  \r\n  useEffect(() => {\r\n    if (syncEnabled) {\r\n      // 同步计数器和todos数量\r\n      const difference = Math.abs(count - todosCount)\r\n      if (difference > 0) {\r\n        console.log(`状态同步: 计数器=${count}, Todos=${todosCount}, 差异=${difference}`)\r\n      }\r\n    }\r\n  }, [syncEnabled, count, todosCount])\r\n  \r\n  return { syncEnabled, setSyncEnabled, count, todosCount }\r\n}\r\n\r\nfunction AdvancedFeatures() {\r\n  // 使用自定义hooks\r\n  const multiStoreData = useMultiStoreData()\r\n  const optimizedData = useOptimizedSelector()\r\n  const syncData = useStateSynchronization()\r\n  \r\n  // 基础状态\r\n  const name = useBasicStore(selectName)\r\n  const items = useBasicStore(selectItems)\r\n  const filter = useTodosStore(selectFilter)\r\n  const filteredTodos = useTodosStore(selectFilteredTodos)\r\n  const users = useApiStore(selectUsers)\r\n  \r\n  // 获取actions\r\n  const { increment, decrement, setName, addItem, batchUpdate } = useBasicStore()\r\n  const { addTodo, setFilter } = useTodosStore()\r\n  const { setTheme } = useSettingsStore()\r\n  \r\n  // 本地状态\r\n  const [performanceLog, setPerformanceLog] = useState([])\r\n  const [renderCount, setRenderCount] = useState(0)\r\n  const [computedValues, setComputedValues] = useState({})\r\n  \r\n  // 性能监控\r\n  useEffect(() => {\r\n    setRenderCount(prev => {\r\n      const newCount = prev + 1\r\n      const renderTime = performance.now()\r\n      setPerformanceLog(prevLog => [...prevLog.slice(-9), {\r\n        id: Date.now(),\r\n        renderCount: newCount,\r\n        timestamp: new Date().toLocaleTimeString(),\r\n        renderTime: renderTime.toFixed(2)\r\n      }])\r\n      return newCount\r\n    })\r\n  })\r\n  \r\n  // 状态计算示例\r\n  const complexCalculations = useMemo(() => {\r\n    const startTime = performance.now()\r\n    \r\n    const calculations = {\r\n      // 多Store聚合计算\r\n      totalItems: items.length + filteredTodos.length + users.length,\r\n      averageLength: ((items.length + filteredTodos.length + users.length) / 3).toFixed(1),\r\n      \r\n      // 复杂业务逻辑\r\n      businessScore: (multiStoreData.basicStats.totalOperations * 0.3 + \r\n                     multiStoreData.todosStats.completedCount * 0.5 + \r\n                     multiStoreData.apiStats.totalRequests * 0.2).toFixed(1),\r\n      \r\n      // 状态健康度\r\n      healthScore: Math.min(100, (\r\n        (multiStoreData.basicStats.successRate + \r\n         (multiStoreData.todosStats.completedCount / Math.max(1, multiStoreData.todosStats.totalCount) * 100) +\r\n         (multiStoreData.apiStats.totalUsers * 10)) / 3\r\n      )).toFixed(1)\r\n    }\r\n    \r\n    const endTime = performance.now()\r\n    calculations.computeTime = (endTime - startTime).toFixed(2)\r\n    \r\n    return calculations\r\n  }, [items.length, filteredTodos.length, users.length, multiStoreData])\r\n  \r\n  // 批量操作示例\r\n  const handleBatchOperations = useCallback(() => {\r\n    const startTime = performance.now()\r\n    \r\n    // 批量更新多个store\r\n    batchUpdate({\r\n      count: optimizedData.count + 5,\r\n      name: `批量更新-${Date.now()}`,\r\n      items: [...items, { id: Date.now(), name: '批量项目', completed: false }]\r\n    })\r\n    \r\n    addTodo(`批量Todo-${Date.now()}`)\r\n    \r\n    const endTime = performance.now()\r\n    console.log(`批量操作耗时: ${(endTime - startTime).toFixed(2)}ms`)\r\n  }, [batchUpdate, optimizedData.count, items, addTodo])\r\n  \r\n  // 跨Store通信示例\r\n  const handleCrossStoreAction = () => {\r\n    // 基于一个store的状态更新另一个store\r\n    if (optimizedData.count > 10) {\r\n      setTheme('dark')\r\n      addTodo(`高级Todo-${optimizedData.count}`)\r\n    } else {\r\n      setTheme('light')\r\n      setName(`Count: ${optimizedData.count}`)\r\n    }\r\n  }\r\n  \r\n  // 状态快照功能\r\n  const createStateSnapshot = () => {\r\n    const snapshot = {\r\n      timestamp: new Date().toISOString(),\r\n      basicStore: {\r\n        count: optimizedData.count,\r\n        name,\r\n        itemsCount: items.length\r\n      },\r\n      todosStore: {\r\n        filter,\r\n        todosCount: filteredTodos.length\r\n      },\r\n      settingsStore: {\r\n        theme: multiStoreData.theme,\r\n        language: multiStoreData.language\r\n      }\r\n    }\r\n    \r\n    setComputedValues(prev => ({\r\n      ...prev,\r\n      lastSnapshot: snapshot\r\n    }))\r\n  }\r\n  \r\n  return (\r\n    <div className=\"advanced-features-page\">\r\n      <div className=\"card-header\">\r\n        <h1 className=\"card-title\">🚀 高级特性</h1>\r\n        <p className=\"card-description\">\r\n          展示Zustand的高级功能，包括性能优化、状态计算、跨组件通信等\r\n        </p>\r\n      </div>\r\n\r\n      {/* 性能监控 */}\r\n      <div className=\"card mb-lg\">\r\n        <h2 className=\"card-title\">📊 性能监控</h2>\r\n        <div className=\"grid grid-4\">\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-primary\">{renderCount}</div>\r\n            <div className=\"text-sm text-secondary\">组件渲染次数</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-success\">{complexCalculations.computeTime}ms</div>\r\n            <div className=\"text-sm text-secondary\">计算耗时</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-warning\">{optimizedData.expensiveCalculation.toFixed(0)}</div>\r\n            <div className=\"text-sm text-secondary\">复杂计算值</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-info\">{performanceLog.length}</div>\r\n            <div className=\"text-sm text-secondary\">性能日志</div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"mt-md\">\r\n          <h3 className=\"font-semibold mb-sm\">最近渲染日志</h3>\r\n          <div className=\"max-h-32 overflow-y-auto space-y-1\">\r\n            {performanceLog.slice(-5).map((log) => (\r\n              <div key={log.id} className=\"text-xs p-xs bg-surface rounded flex justify-between\">\r\n                <span>渲染 #{log.renderCount}</span>\r\n                <span>{log.timestamp}</span>\r\n                <span>{log.renderTime}ms</span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-2\">\r\n        {/* 自定义Hooks */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">🎣 自定义Hooks</h2>\r\n          <p className=\"text-sm text-secondary mb-md\">\r\n            封装复杂逻辑，提高代码复用性和可维护性\r\n          </p>\r\n          \r\n          <div className=\"space-y-2\">\r\n            <div>\r\n              <h3 className=\"font-semibold mb-sm\">useMultiStoreData</h3>\r\n              <pre className=\"bg-surface p-sm rounded text-xs overflow-auto\">\r\n{JSON.stringify(multiStoreData, null, 2)}\r\n              </pre>\r\n            </div>\r\n            \r\n            <div>\r\n              <h3 className=\"font-semibold mb-sm\">useOptimizedSelector</h3>\r\n              <div className=\"text-sm space-y-1\">\r\n                <div>基础计数: {optimizedData.count}</div>\r\n                <div>双倍计数: {optimizedData.doubleCount}</div>\r\n                <div>三倍计数: {optimizedData.tripleCount}</div>\r\n                <div>优化计算: {optimizedData.expensiveCalculation.toFixed(2)}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 状态计算 */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">🧮 状态计算</h2>\r\n          <p className=\"text-sm text-secondary mb-md\">\r\n            基于多个store的状态进行复杂计算和业务逻辑处理\r\n          </p>\r\n          \r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex justify-between\">\r\n              <span>总项目数:</span>\r\n              <span className=\"badge badge-primary\">{complexCalculations.totalItems}</span>\r\n            </div>\r\n            <div className=\"flex justify-between\">\r\n              <span>平均项目数:</span>\r\n              <span className=\"badge badge-info\">{complexCalculations.averageLength}</span>\r\n            </div>\r\n            <div className=\"flex justify-between\">\r\n              <span>业务评分:</span>\r\n              <span className=\"badge badge-success\">{complexCalculations.businessScore}</span>\r\n            </div>\r\n            <div className=\"flex justify-between\">\r\n              <span>健康度:</span>\r\n              <span className=\"badge badge-warning\">{complexCalculations.healthScore}%</span>\r\n            </div>\r\n          </div>\r\n          \r\n          <button\r\n            onClick={createStateSnapshot}\r\n            className=\"btn btn-primary btn-sm w-full mt-md\"\r\n          >\r\n            📸 创建状态快照\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 跨Store通信 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">🔗 跨Store通信</h2>\r\n        <p className=\"text-sm text-secondary mb-md\">\r\n          演示不同store之间的状态同步和相互影响\r\n        </p>\r\n        \r\n        <div className=\"grid grid-3\">\r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">状态同步</h3>\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <span>同步开关:</span>\r\n                <input\r\n                  type=\"checkbox\"\r\n                  checked={syncData.syncEnabled}\r\n                  onChange={(e) => syncData.setSyncEnabled(e.target.checked)}\r\n                  className=\"form-control w-auto\"\r\n                />\r\n              </div>\r\n              <div className=\"text-sm\">\r\n                <div>计数器: {syncData.count}</div>\r\n                <div>Todos: {syncData.todosCount}</div>\r\n                <div>差异: {Math.abs(syncData.count - syncData.todosCount)}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">批量操作</h3>\r\n            <div className=\"space-y-2\">\r\n              <button\r\n                onClick={handleBatchOperations}\r\n                className=\"btn btn-success btn-sm w-full\"\r\n              >\r\n                🔄 批量更新多Store\r\n              </button>\r\n              <button\r\n                onClick={handleCrossStoreAction}\r\n                className=\"btn btn-warning btn-sm w-full\"\r\n              >\r\n                🔗 跨Store联动\r\n              </button>\r\n            </div>\r\n          </div>\r\n          \r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">状态快照</h3>\r\n            {computedValues.lastSnapshot && (\r\n              <div className=\"text-xs\">\r\n                <div>时间: {new Date(computedValues.lastSnapshot.timestamp).toLocaleTimeString()}</div>\r\n                <div>计数: {computedValues.lastSnapshot.basicStore.count}</div>\r\n                <div>主题: {computedValues.lastSnapshot.settingsStore.theme}</div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 选择器优化 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">⚡ 选择器优化</h2>\r\n        <div className=\"grid grid-2\">\r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">派生状态示例</h3>\r\n            <div className=\"text-sm space-y-1\">\r\n              <div>基础计数: {optimizedData.count}</div>\r\n              <div>派生双倍: {optimizedData.doubleCount}</div>\r\n              <div>派生三倍: {optimizedData.tripleCount}</div>\r\n              <div>复合计算: {optimizedData.expensiveCalculation.toFixed(2)}</div>\r\n            </div>\r\n            \r\n            <div className=\"mt-md space-y-1\">\r\n              <button onClick={increment} className=\"btn btn-primary btn-sm mr-xs\">+1</button>\r\n              <button onClick={decrement} className=\"btn btn-secondary btn-sm mr-xs\">-1</button>\r\n              <button onClick={() => addItem(`Item-${Date.now()}`)} className=\"btn btn-info btn-sm\">\r\n                添加项目\r\n              </button>\r\n            </div>\r\n          </div>\r\n          \r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">过滤器状态</h3>\r\n            <div className=\"text-sm space-y-1\">\r\n              <div>当前过滤: {filter}</div>\r\n              <div>过滤结果: {filteredTodos.length} 项</div>\r\n            </div>\r\n            \r\n            <div className=\"mt-md space-y-1\">\r\n              {['all', 'active', 'completed'].map((filterType) => (\r\n                <button\r\n                  key={filterType}\r\n                  onClick={() => setFilter(filterType)}\r\n                  className={`btn btn-sm mr-xs ${filter === filterType ? 'btn-primary' : 'btn-secondary'}`}\r\n                >\r\n                  {filterType}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 高级模式 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">🔬 高级模式</h2>\r\n        <div className=\"grid grid-3\">\r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">内存使用</h3>\r\n            <div className=\"text-sm space-y-1\">\r\n              <div>Store实例: 4个</div>\r\n              <div>选择器缓存: 活跃</div>\r\n              <div>订阅数量: {multiStoreData.basicStats.totalOperations || 0}</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">热重载支持</h3>\r\n            <div className=\"text-sm space-y-1\">\r\n              <div>状态保持: ✅</div>\r\n              <div>开发模式: ✅</div>\r\n              <div>HMR兼容: ✅</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">类型安全</h3>\r\n            <div className=\"text-sm space-y-1\">\r\n              <div>TypeScript: ⚠️ JS模式</div>\r\n              <div>类型推断: 部分支持</div>\r\n              <div>智能提示: 基础支持</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 最佳实践 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">💡 最佳实践</h2>\r\n        <div className=\"grid grid-2\">\r\n          <div>\r\n            <h3 className=\"font-semibold mb-md\">性能优化</h3>\r\n            <ul className=\"text-sm text-secondary space-y-1\">\r\n              <li>• 使用选择器避免不必要的重渲染</li>\r\n              <li>• 合理拆分store避免单一职责过重</li>\r\n              <li>• 使用useMemo缓存昂贵计算</li>\r\n              <li>• 批量更新减少状态变更频率</li>\r\n              <li>• 使用subscribe精确监听状态变化</li>\r\n            </ul>\r\n          </div>\r\n          <div>\r\n            <h3 className=\"font-semibold mb-md\">架构设计</h3>\r\n            <ul className=\"text-sm text-secondary space-y-1\">\r\n              <li>• 按功能模块划分store</li>\r\n              <li>• 定义清晰的状态接口</li>\r\n              <li>• 使用中间件增强功能</li>\r\n              <li>• 封装自定义hooks复用逻辑</li>\r\n              <li>• 实现良好的错误边界处理</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 调试工具 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">🔍 调试工具</h2>\r\n        <div className=\"flex gap-sm flex-wrap\">\r\n          <button\r\n            onClick={() => console.log('BasicStore状态:', useBasicStore.getState())}\r\n            className=\"btn btn-info btn-sm\"\r\n          >\r\n            🐛 打印Basic状态\r\n          </button>\r\n          <button\r\n            onClick={() => console.log('TodosStore状态:', useTodosStore.getState())}\r\n            className=\"btn btn-info btn-sm\"\r\n          >\r\n            🐛 打印Todos状态\r\n          </button>\r\n          <button\r\n            onClick={() => console.log('SettingsStore状态:', useSettingsStore.getState())}\r\n            className=\"btn btn-info btn-sm\"\r\n          >\r\n            🐛 打印Settings状态\r\n          </button>\r\n          <button\r\n            onClick={() => console.log('ApiStore状态:', useApiStore.getState())}\r\n            className=\"btn btn-info btn-sm\"\r\n          >\r\n            🐛 打印API状态\r\n          </button>\r\n          <button\r\n            onClick={() => {\r\n              setPerformanceLog([])\r\n              setComputedValues({})\r\n              setRenderCount(0)\r\n            }}\r\n            className=\"btn btn-warning btn-sm\"\r\n          >\r\n            🧹 清理调试数据\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default AdvancedFeatures "], "names": ["AdvancedFeatures", "multiStoreData", "basicStats", "useBasicStore", "selectStats", "todosStats", "useTodosStore", "selectTodoStats", "apiStats", "useApiStore", "selectApiStats", "theme", "useSettingsStore", "selectTheme", "language", "selectLanguage", "optimizedData", "count", "selectCount", "doubleCount", "selectDoubleCount", "tripleCount", "selectTripleCount", "expensiveCalculation", "useMemo", "Math", "random", "useOptimizedSelector", "syncData", "syncEnabled", "setSyncEnabled", "useState", "todosCount", "selectTodos", "length", "useEffect", "abs", "useStateSynchronization", "name", "selectName", "items", "selectItems", "filter", "selectFilter", "filteredTodos", "selectFilteredTodos", "users", "selectUsers", "increment", "decrement", "setName", "addItem", "batchUpdate", "addTodo", "setFilter", "setTheme", "performanceLog", "setPerformanceLog", "renderCount", "setRenderCount", "computedValues", "setComputedValues", "prev", "newCount", "renderTime", "performance", "now", "prevLog", "slice", "id", "Date", "timestamp", "toLocaleTimeString", "toFixed", "complexCalculations", "startTime", "calculations", "totalItems", "averageLength", "businessScore", "totalOperations", "completedCount", "totalRequests", "healthScore", "min", "successRate", "max", "totalCount", "totalUsers", "endTime", "computeTime", "handleBatchOperations", "useCallback", "completed", "jsxs", "className", "children", "jsx", "map", "log", "stringify", "onClick", "snapshot", "toISOString", "basicStore", "itemsCount", "todosStore", "settingsStore", "lastSnapshot", "type", "checked", "onChange", "e", "target", "filterType"], "mappings": "qWAmDA,SAASA,IAEP,MAAMC,EA7CC,CACLC,WAAYC,EAAcC,GAC1BC,WAAYC,EAAcC,GAC1BC,SAAUC,EAAYC,GACtBC,MAAOC,EAAiBC,GACxBC,SAAUF,EAAiBG,IAyCvBC,EApCR,WACE,MAAMC,EAAQd,EAAce,GACtBC,EAAchB,EAAciB,GAC5BC,EAAclB,EAAcmB,GAG5BC,EAAuBC,EAAAA,QAAQ,IAE5BP,EAAQE,EAAcE,EAA8B,IAAhBI,KAAKC,SAC/C,CAACT,EAAOE,EAAaE,IAExB,MAAO,CAAEJ,QAAOE,cAAaE,cAAaE,uBAC5C,CAwBwBI,GAChBC,EAtBR,WACE,MAAOC,EAAaC,GAAkBC,EAAAA,UAAS,GACzCd,EAAQd,EAAce,GACtBc,EAAa1B,EAAc2B,GAAaC,OAY9C,OAVAC,EAAAA,UAAU,KACJN,GAEiBJ,KAAKW,IAAInB,EAAQe,IAKrC,CAACH,EAAaZ,EAAOe,IAEjB,CAAEH,cAAaC,iBAAgBb,QAAOe,aAC/C,CAMmBK,GAGXC,EAAOnC,EAAcoC,GACrBC,EAAQrC,EAAcsC,GACtBC,EAASpC,EAAcqC,GACvBC,EAAgBtC,EAAcuC,GAC9BC,EAAQrC,EAAYsC,IAGpBC,UAAEA,EAAAC,UAAWA,EAAAC,QAAWA,UAASC,EAAAC,YAASA,GAAgBjD,KAC1DkD,QAAEA,EAAAC,UAASA,GAAchD,KACzBiD,SAAEA,GAAa3C,KAGd4C,EAAgBC,GAAqB1B,EAAAA,SAAS,KAC9C2B,EAAaC,GAAkB5B,EAAAA,SAAS,IACxC6B,EAAgBC,GAAqB9B,EAAAA,SAAS,CAAA,GAGrDI,EAAAA,UAAU,KACRwB,EAAeG,IACb,MAAMC,EAAWD,EAAO,EAClBE,EAAaC,YAAYC,MAO/B,OANAT,KAA6B,IAAIU,EAAQC,UAAW,CAClDC,GAAIC,KAAKJ,MACTR,YAAaK,EACbQ,WAAA,IAAeD,MAAOE,qBACtBR,WAAYA,EAAWS,QAAQ,MAE1BV,MAKX,MAAMW,EAAsBlD,EAAAA,QAAQ,KAClC,MAAMmD,EAAYV,YAAYC,MAExBU,EAAe,CAEnBC,WAAYrC,EAAMN,OAASU,EAAcV,OAASY,EAAMZ,OACxD4C,gBAAiBtC,EAAMN,OAASU,EAAcV,OAASY,EAAMZ,QAAU,GAAGuC,QAAQ,GAGlFM,eAA4D,GAA5C9E,EAAeC,WAAW8E,gBACgB,GAA3C/E,EAAeI,WAAW4E,eACc,GAAxChF,EAAeO,SAAS0E,eAAqBT,QAAQ,GAGpEU,YAAa1D,KAAK2D,IAAI,KACnBnF,EAAeC,WAAWmF,YACzBpF,EAAeI,WAAW4E,eAAiBxD,KAAK6D,IAAI,EAAGrF,EAAeI,WAAWkF,YAAc,IAC1D,GAArCtF,EAAeO,SAASgF,YAAoB,GAC7Cf,QAAQ,IAGPgB,EAAUxB,YAAYC,MAG5B,OAFAU,EAAac,aAAeD,EAAUd,GAAWF,QAAQ,GAElDG,GACN,CAACpC,EAAMN,OAAQU,EAAcV,OAAQY,EAAMZ,OAAQjC,IAGhD0F,EAAwBC,EAAAA,YAAY,KACtB3B,YAAYC,MAG9Bd,EAAY,CACVnC,MAAOD,EAAcC,MAAQ,EAC7BqB,KAAM,QAAQgC,KAAKJ,QACnB1B,MAAO,IAAIA,EAAO,CAAE6B,GAAIC,KAAKJ,MAAO5B,KAAM,OAAQuD,WAAW,MAG/DxC,EAAQ,UAAUiB,KAAKJ,SAEPD,YAAYC,OAE3B,CAACd,EAAapC,EAAcC,MAAOuB,EAAOa,IAuC7C,SACEyC,KAAC,MAAA,CAAIC,UAAU,yBACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,YAC3BC,EAAAA,IAAC,IAAA,CAAEF,UAAU,mBAAmBC,SAAA,2CAMlCF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,kCAAmCC,SAAAtC,IAClDuC,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,gBAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,kCAAmCC,SAAA,CAAAtB,EAAoBgB,YAAY,QAClFO,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,cAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,OAAIF,UAAU,kCAAmCC,WAAczE,qBAAqBkD,QAAQ,KAC7FwB,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,eAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,+BAAgCC,SAAAxC,EAAetB,SAC9D+D,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,iBAI5CF,KAAC,MAAA,CAAIC,UAAU,QACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,WACpCC,EAAAA,IAAC,MAAA,CAAIF,UAAU,qCACZC,WAAe5B,OAAM,GAAI8B,IAAKC,GAC7BL,EAAAA,KAAC,MAAA,CAAiBC,UAAU,uDAC1BC,SAAA,CAAAF,OAAC,OAAA,CAAKE,SAAA,CAAA,OAAKG,EAAIzC,iBACfuC,IAAC,OAAA,CAAMD,SAAAG,EAAI5B,mBACV,OAAA,CAAMyB,SAAA,CAAAG,EAAInC,WAAW,UAHdmC,EAAI9B,eAUtByB,KAAC,MAAA,CAAIC,UAAU,cAEbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,gBAC3BC,EAAAA,IAAC,IAAA,CAAEF,UAAU,+BAA+BC,SAAA,0BAI5CF,KAAC,MAAA,CAAIC,UAAU,YACbC,SAAA,CAAAF,OAAC,MAAA,CACCE,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,sBACpCC,EAAAA,IAAC,OAAIF,UAAU,gDAC5BC,cAAKI,UAAUnG,EAAgB,KAAM,eAIzB,MAAA,CACC+F,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,2BACpCF,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAF,OAAC,MAAA,CAAIE,SAAA,CAAA,SAAOhF,EAAcC,gBACzB,MAAA,CAAI+E,SAAA,CAAA,SAAOhF,EAAcG,sBACzB,MAAA,CAAI6E,SAAA,CAAA,SAAOhF,EAAcK,sBACzB,MAAA,CAAI2E,SAAA,CAAA,SAAOhF,EAAcO,qBAAqBkD,QAAQ,oBAO/DqB,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,YAC3BC,EAAAA,IAAC,IAAA,CAAEF,UAAU,+BAA+BC,SAAA,gCAI5CF,KAAC,MAAA,CAAIC,UAAU,YACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,uBACbC,SAAA,GAAAC,IAAC,QAAKD,SAAA,UACNC,EAAAA,IAAC,OAAA,CAAKF,UAAU,sBAAuBC,WAAoBnB,kBAE7DiB,KAAC,MAAA,CAAIC,UAAU,uBACbC,SAAA,GAAAC,IAAC,QAAKD,SAAA,WACNC,EAAAA,IAAC,OAAA,CAAKF,UAAU,mBAAoBC,WAAoBlB,qBAE1DgB,KAAC,MAAA,CAAIC,UAAU,uBACbC,SAAA,GAAAC,IAAC,QAAKD,SAAA,UACNC,EAAAA,IAAC,OAAA,CAAKF,UAAU,sBAAuBC,WAAoBjB,qBAE7De,KAAC,MAAA,CAAIC,UAAU,uBACbC,SAAA,GAAAC,IAAC,QAAKD,SAAA,WACNF,KAAC,OAAA,CAAKC,UAAU,sBAAuBC,SAAA,CAAAtB,EAAoBS,YAAY,aAI3Ec,EAAAA,IAAC,SAAA,CACCI,QA5HkB,KAC1B,MAAMC,EAAW,CACf/B,WAAA,IAAeD,MAAOiC,cACtBC,WAAY,CACVvF,MAAOD,EAAcC,MACrBqB,OACAmE,WAAYjE,EAAMN,QAEpBwE,WAAY,CACVhE,SACAV,WAAYY,EAAcV,QAE5ByE,cAAe,CACbhG,MAAOV,EAAeU,MACtBG,SAAUb,EAAea,WAI7B+C,EAAkBC,IAAA,IACbA,EACH8C,aAAcN,MAyGRP,UAAU,sCACXC,SAAA,sBAOLF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,gBAC3BC,EAAAA,IAAC,IAAA,CAAEF,UAAU,+BAA+BC,SAAA,4BAI5CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,OAAC,MAAA,CACCE,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,WACpCF,KAAC,MAAA,CAAIC,UAAU,YACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,oCACbC,SAAA,GAAAC,IAAC,QAAKD,SAAA,UACNC,EAAAA,IAAC,QAAA,CACCY,KAAK,WACLC,QAASlF,EAASC,YAClBkF,SAAWC,GAAMpF,EAASE,eAAekF,EAAEC,OAAOH,SAClDf,UAAU,6BAGdD,KAAC,MAAA,CAAIC,UAAU,UACbC,SAAA,CAAAF,OAAC,MAAA,CAAIE,SAAA,CAAA,QAAMpE,EAASX,gBACnB,MAAA,CAAI+E,SAAA,CAAA,UAAQpE,EAASI,qBACrB,MAAA,CAAIgE,SAAA,CAAA,OAAKvE,KAAKW,IAAIR,EAASX,MAAQW,EAASI,+BAKlD,MAAA,CACCgE,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,WACpCF,KAAC,MAAA,CAAIC,UAAU,YACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CACCI,QAASV,EACTI,UAAU,gCACXC,SAAA,kBAGDC,EAAAA,IAAC,SAAA,CACCI,QAtLiB,KAEzBrF,EAAcC,MAAQ,IACxBsC,EAAS,QACTF,EAAQ,UAAUrC,EAAcC,WAEhCsC,EAAS,SACTL,EAAQ,UAAUlC,EAAcC,WAgLtB8E,UAAU,gCACXC,SAAA,6BAMJ,MAAA,CACCA,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,SACnCpC,EAAegD,cACdd,OAAC,MAAA,CAAIC,UAAU,UACbC,SAAA,CAAAF,OAAC,MAAA,CAAIE,SAAA,CAAA,OAAK,IAAI1B,KAAKV,EAAegD,aAAarC,WAAWC,+BACzD,MAAA,CAAIwB,SAAA,CAAA,OAAKpC,EAAegD,aAAaJ,WAAWvF,gBAChD,MAAA,CAAI+E,SAAA,CAAA,OAAKpC,EAAegD,aAAaD,cAAchG,uBAQ9DmF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,OAAC,MAAA,CACCE,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,aACpCF,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAF,OAAC,MAAA,CAAIE,SAAA,CAAA,SAAOhF,EAAcC,gBACzB,MAAA,CAAI+E,SAAA,CAAA,SAAOhF,EAAcG,sBACzB,MAAA,CAAI6E,SAAA,CAAA,SAAOhF,EAAcK,sBACzB,MAAA,CAAI2E,SAAA,CAAA,SAAOhF,EAAcO,qBAAqBkD,QAAQ,WAGzDqB,KAAC,MAAA,CAAIC,UAAU,kBACbC,SAAA,CAAAC,MAAC,SAAA,CAAOI,QAASrD,EAAW+C,UAAU,+BAA+BC,SAAA,aACpE,SAAA,CAAOK,QAASpD,EAAW8C,UAAU,iCAAiCC,SAAA,SACvEC,IAAC,SAAA,CAAOI,QAAS,IAAMlD,EAAQ,QAAQmB,KAAKJ,SAAU6B,UAAU,sBAAsBC,SAAA,sBAMzF,MAAA,CACCA,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,YACpCF,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAF,OAAC,MAAA,CAAIE,SAAA,CAAA,SAAOtD,YACX,MAAA,CAAIsD,SAAA,CAAA,SAAOpD,EAAcV,OAAO,WAGnC+D,EAAAA,IAAC,MAAA,CAAIF,UAAU,kBACZC,SAAA,CAAC,MAAO,SAAU,aAAaE,IAAKgB,GACnCjB,EAAAA,IAAC,SAAA,CAECI,QAAS,IAAM/C,EAAU4D,GACzBnB,UAAW,qBAAoBrD,IAAWwE,EAAa,cAAgB,iBAEtElB,SAAAkB,GAJIA,iBAajBpB,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,OAAC,MAAA,CACCE,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,WACpCF,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,GAAAC,IAAC,OAAID,SAAA,kBACLC,IAAC,OAAID,SAAA,qBACJ,MAAA,CAAIA,SAAA,CAAA,SAAO/F,EAAeC,WAAW8E,iBAAmB,kBAI5D,MAAA,CACCgB,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,YACpCF,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,GAAAC,IAAC,OAAID,SAAA,cACLC,IAAC,OAAID,SAAA,cACLC,IAAC,OAAID,SAAA,0BAIR,MAAA,CACCA,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,WACpCF,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,GAAAC,IAAC,OAAID,SAAA,0BACLC,IAAC,OAAID,SAAA,iBACLC,IAAC,OAAID,SAAA,6BAObF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,OAAC,MAAA,CACCE,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,WACpCF,KAAC,KAAA,CAAGC,UAAU,mCACZC,SAAA,GAAAC,IAAC,MAAGD,SAAA,uBACJC,IAAC,MAAGD,SAAA,0BACJC,IAAC,MAAGD,SAAA,wBACJC,IAAC,MAAGD,SAAA,qBACJC,IAAC,MAAGD,SAAA,uCAGP,MAAA,CACCA,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,WACpCF,KAAC,KAAA,CAAGC,UAAU,mCACZC,SAAA,GAAAC,IAAC,MAAGD,SAAA,qBACJC,IAAC,MAAGD,SAAA,kBACJC,IAAC,MAAGD,SAAA,kBACJC,IAAC,MAAGD,SAAA,uBACJC,IAAC,MAAGD,SAAA,gCAOZF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAC3BF,KAAC,MAAA,CAAIC,UAAU,wBACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CACCI,QAAS,OACTN,UAAU,sBACXC,SAAA,iBAGDC,EAAAA,IAAC,SAAA,CACCI,QAAS,OACTN,UAAU,sBACXC,SAAA,iBAGDC,EAAAA,IAAC,SAAA,CACCI,QAAS,OACTN,UAAU,sBACXC,SAAA,oBAGDC,EAAAA,IAAC,SAAA,CACCI,QAAS,OACTN,UAAU,sBACXC,SAAA,eAGDC,EAAAA,IAAC,SAAA,CACCI,QAAS,KACP5C,EAAkB,IAClBI,EAAkB,CAAA,GAClBF,EAAe,IAEjBoC,UAAU,yBACXC,SAAA,sBAOX"}