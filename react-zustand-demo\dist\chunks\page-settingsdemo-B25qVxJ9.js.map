{"version": 3, "file": "page-settingsdemo-B25qVxJ9.js", "sources": ["../../src/pages/SettingsDemo.jsx"], "sourcesContent": ["import React from 'react'\r\nimport { useSettingsStore, selectTheme, selectLanguage, selectNotifications, selectAutoSave, selectFontSize, selectLayout, selectPreferences, selectAdvancedSettings, selectShortcuts, selectColorScheme, selectThemeLabel, selectLanguageLabel, selectLayoutLabel, selectAllSettings, selectSettingsSummary } from '@/stores/settingsStore'\r\n\r\nfunction SettingsDemo() {\r\n  // 使用Zustand获取状态\r\n  const theme = useSettingsStore(selectTheme)\r\n  const language = useSettingsStore(selectLanguage)\r\n  const notifications = useSettingsStore(selectNotifications)\r\n  const autoSave = useSettingsStore(selectAutoSave)\r\n  const fontSize = useSettingsStore(selectFontSize)\r\n  const layout = useSettingsStore(selectLayout)\r\n  const preferences = useSettingsStore(selectPreferences)\r\n  const advancedSettings = useSettingsStore(selectAdvancedSettings)\r\n  const shortcuts = useSettingsStore(selectShortcuts)\r\n  const colorScheme = useSettingsStore(selectColorScheme)\r\n  \r\n  // 获取派生状态\r\n  const themeLabel = useSettingsStore(selectThemeLabel)\r\n  const languageLabel = useSettingsStore(selectLanguageLabel)\r\n  const layoutLabel = useSettingsStore(selectLayoutLabel)\r\n  const allSettings = useSettingsStore(selectAllSettings)\r\n  const settingsSummary = useSettingsStore(selectSettingsSummary)\r\n\r\n  // 获取actions\r\n  const {\r\n    setTheme,\r\n    setLanguage,\r\n    toggleNotifications,\r\n    toggleAutoSave,\r\n    setFontSize,\r\n    setLayout,\r\n    updatePreference,\r\n    togglePreference,\r\n    toggleAdvancedSetting,\r\n    updateShortcut,\r\n    updateColor,\r\n    batchUpdate,\r\n    applyPreset,\r\n    exportSettings,\r\n    importSettings,\r\n    saveSettings,\r\n    resetToDefaults,\r\n    reset\r\n  } = useSettingsStore()\r\n\r\n  // 预设配置\r\n  const presets = {\r\n    dark: {\r\n      theme: 'dark',\r\n      fontSize: 16,\r\n      colorScheme: {\r\n        primary: '#3b82f6',\r\n        secondary: '#6b7280',\r\n        background: '#1f2937'\r\n      }\r\n    },\r\n    light: {\r\n      theme: 'light',\r\n      fontSize: 14,\r\n      colorScheme: {\r\n        primary: '#2563eb',\r\n        secondary: '#374151',\r\n        background: '#ffffff'\r\n      }\r\n    },\r\n    accessibility: {\r\n      fontSize: 18,\r\n      preferences: {\r\n        highContrast: true,\r\n        reduceMotion: true,\r\n        screenReader: true\r\n      },\r\n      advancedSettings: {\r\n        enableKeyboardNavigation: true,\r\n        enableVoiceCommand: true\r\n      }\r\n    }\r\n  }\r\n\r\n  // 处理文件导入\r\n  const handleImportSettings = (event) => {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      const reader = new FileReader()\r\n      reader.onload = (e) => {\r\n        try {\r\n          const settings = JSON.parse(e.target.result)\r\n          importSettings(settings)\r\n          alert('设置导入成功！')\r\n                 } catch {\r\n           alert('导入失败：无效的JSON格式')\r\n         }\r\n      }\r\n      reader.readAsText(file)\r\n    }\r\n  }\r\n\r\n  // 导出设置\r\n  const handleExportSettings = () => {\r\n    const settings = exportSettings()\r\n    const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' })\r\n    const url = URL.createObjectURL(blob)\r\n    const a = document.createElement('a')\r\n    a.href = url\r\n    a.download = 'settings.json'\r\n    a.click()\r\n    URL.revokeObjectURL(url)\r\n  }\r\n\r\n  return (\r\n    <div className=\"settings-demo-page\">\r\n      <div className=\"card-header\">\r\n        <h1 className=\"card-title\">⚙️ 设置演示</h1>\r\n        <p className=\"card-description\">\r\n          展示Zustand的persist中间件、设置管理、主题切换等功能\r\n        </p>\r\n      </div>\r\n\r\n      {/* 设置概览 */}\r\n      <div className=\"card mb-lg\">\r\n        <h2 className=\"card-title\">📊 设置概览</h2>\r\n        <div className=\"grid grid-4\">\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-primary\">{settingsSummary.totalSettings}</div>\r\n            <div className=\"text-sm text-secondary\">总设置数</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-success\">{settingsSummary.modifiedSettings}</div>\r\n            <div className=\"text-sm text-secondary\">已修改</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-warning\">{settingsSummary.enabledFeatures}</div>\r\n            <div className=\"text-sm text-secondary\">启用功能</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-info\">{Object.keys(shortcuts).length}</div>\r\n            <div className=\"text-sm text-secondary\">快捷键</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-2\">\r\n        {/* 基础设置 */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">🎨 基础设置</h2>\r\n          \r\n          {/* 主题设置 */}\r\n          <div className=\"form-group\">\r\n            <label className=\"form-label\">主题 ({themeLabel})</label>\r\n            <div className=\"flex gap-sm\">\r\n              {['light', 'dark', 'auto'].map((themeOption) => (\r\n                <button\r\n                  key={themeOption}\r\n                  onClick={() => setTheme(themeOption)}\r\n                  className={`btn btn-sm ${theme === themeOption ? 'btn-primary' : 'btn-secondary'}`}\r\n                >\r\n                  {themeOption === 'light' ? '🌞 浅色' : \r\n                   themeOption === 'dark' ? '🌙 深色' : '🔄 自动'}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* 语言设置 */}\r\n          <div className=\"form-group\">\r\n            <label className=\"form-label\">语言 ({languageLabel})</label>\r\n            <select\r\n              value={language}\r\n              onChange={(e) => setLanguage(e.target.value)}\r\n              className=\"form-control\"\r\n            >\r\n              <option value=\"zh-CN\">中文简体</option>\r\n              <option value=\"zh-TW\">中文繁體</option>\r\n              <option value=\"en-US\">English</option>\r\n              <option value=\"ja-JP\">日本語</option>\r\n              <option value=\"ko-KR\">한국어</option>\r\n            </select>\r\n          </div>\r\n\r\n          {/* 字体大小 */}\r\n          <div className=\"form-group\">\r\n            <label className=\"form-label\">字体大小: {fontSize}px</label>\r\n            <input\r\n              type=\"range\"\r\n              min=\"12\"\r\n              max=\"24\"\r\n              value={fontSize}\r\n              onChange={(e) => setFontSize(parseInt(e.target.value))}\r\n              className=\"form-control\"\r\n            />\r\n          </div>\r\n\r\n          {/* 布局设置 */}\r\n          <div className=\"form-group\">\r\n            <label className=\"form-label\">布局 ({layoutLabel})</label>\r\n            <div className=\"flex gap-sm\">\r\n              {['sidebar', 'topbar', 'compact'].map((layoutOption) => (\r\n                <button\r\n                  key={layoutOption}\r\n                  onClick={() => setLayout(layoutOption)}\r\n                  className={`btn btn-sm ${layout === layoutOption ? 'btn-primary' : 'btn-secondary'}`}\r\n                >\r\n                  {layoutOption === 'sidebar' ? '📋 侧边栏' : \r\n                   layoutOption === 'topbar' ? '📄 顶部栏' : '📱 紧凑'}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* 开关设置 */}\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex justify-between items-center\">\r\n              <span>通知</span>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={notifications}\r\n                onChange={toggleNotifications}\r\n                className=\"form-control w-auto\"\r\n              />\r\n            </div>\r\n            <div className=\"flex justify-between items-center\">\r\n              <span>自动保存</span>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={autoSave}\r\n                onChange={toggleAutoSave}\r\n                className=\"form-control w-auto\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 个人偏好 */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">👤 个人偏好</h2>\r\n          \r\n          <div className=\"space-y-2\">\r\n            {Object.entries(preferences).map(([key, value]) => (\r\n              <div key={key} className=\"flex justify-between items-center\">\r\n                <span className=\"capitalize\">\r\n                  {key.replace(/([A-Z])/g, ' $1').toLowerCase()}\r\n                </span>\r\n                <input\r\n                  type=\"checkbox\"\r\n                  checked={value}\r\n                  onChange={() => togglePreference(key)}\r\n                  className=\"form-control w-auto\"\r\n                />\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          <div className=\"form-group\">\r\n            <label className=\"form-label\">自定义偏好</label>\r\n            <div className=\"flex gap-sm\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"偏好名称\"\r\n                onKeyPress={(e) => {\r\n                  if (e.key === 'Enter' && e.target.value.trim()) {\r\n                    updatePreference(e.target.value, true)\r\n                    e.target.value = ''\r\n                  }\r\n                }}\r\n                className=\"form-control\"\r\n              />\r\n              <button\r\n                onClick={() => {\r\n                  const input = document.querySelector('input[placeholder=\"偏好名称\"]')\r\n                  if (input.value.trim()) {\r\n                    updatePreference(input.value, true)\r\n                    input.value = ''\r\n                  }\r\n                }}\r\n                className=\"btn btn-primary\"\r\n              >\r\n                添加\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 高级设置 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">⚡ 高级设置</h2>\r\n        <div className=\"grid grid-3\">\r\n          {Object.entries(advancedSettings).map(([key, value]) => (\r\n            <div key={key} className=\"flex justify-between items-center\">\r\n              <span className=\"text-sm\">\r\n                {key.replace(/([A-Z])/g, ' $1').toLowerCase()}\r\n              </span>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={value}\r\n                onChange={() => toggleAdvancedSetting(key)}\r\n                className=\"form-control w-auto\"\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 颜色配置 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">🎨 颜色配置</h2>\r\n        <div className=\"grid grid-3\">\r\n          {Object.entries(colorScheme).map(([key, value]) => (\r\n            <div key={key} className=\"form-group\">\r\n              <label className=\"form-label\">{key}</label>\r\n              <div className=\"flex gap-sm items-center\">\r\n                <input\r\n                  type=\"color\"\r\n                  value={value}\r\n                  onChange={(e) => updateColor(key, e.target.value)}\r\n                  className=\"w-10 h-8 rounded border\"\r\n                />\r\n                <input\r\n                  type=\"text\"\r\n                  value={value}\r\n                  onChange={(e) => updateColor(key, e.target.value)}\r\n                  className=\"form-control text-sm\"\r\n                />\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 快捷键设置 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">⌨️ 快捷键</h2>\r\n        <div className=\"grid grid-2\">\r\n          {Object.entries(shortcuts).map(([key, value]) => (\r\n            <div key={key} className=\"flex justify-between items-center\">\r\n              <span className=\"text-sm\">{key}</span>\r\n              <input\r\n                type=\"text\"\r\n                value={value}\r\n                onChange={(e) => updateShortcut(key, e.target.value)}\r\n                className=\"form-control w-32 text-sm\"\r\n                placeholder=\"按键组合\"\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 预设和操作 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">🔧 预设和操作</h2>\r\n        \r\n        {/* 预设主题 */}\r\n        <div className=\"mb-md\">\r\n          <h3 className=\"font-semibold mb-sm\">主题预设</h3>\r\n          <div className=\"flex gap-sm\">\r\n            {Object.entries(presets).map(([presetName, preset]) => (\r\n              <button\r\n                key={presetName}\r\n                onClick={() => applyPreset(presetName, preset)}\r\n                className=\"btn btn-info btn-sm\"\r\n              >\r\n                {presetName === 'dark' ? '🌙 深色主题' : \r\n                 presetName === 'light' ? '🌞 浅色主题' : '♿ 无障碍'}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* 批量更新示例 */}\r\n        <div className=\"mb-md\">\r\n          <h3 className=\"font-semibold mb-sm\">批量操作</h3>\r\n          <div className=\"flex gap-sm\">\r\n            <button\r\n              onClick={() => batchUpdate({\r\n                fontSize: 16,\r\n                notifications: true,\r\n                autoSave: true,\r\n                theme: 'auto'\r\n              })}\r\n              className=\"btn btn-success btn-sm\"\r\n            >\r\n              🔄 推荐设置\r\n            </button>\r\n            <button\r\n              onClick={() => batchUpdate({\r\n                'preferences.animations': false,\r\n                'preferences.sounds': false,\r\n                'advancedSettings.enableCache': false\r\n              })}\r\n              className=\"btn btn-warning btn-sm\"\r\n            >\r\n              ⚡ 性能模式\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 导入导出 */}\r\n        <div className=\"mb-md\">\r\n          <h3 className=\"font-semibold mb-sm\">导入导出</h3>\r\n          <div className=\"flex gap-sm\">\r\n            <button\r\n              onClick={handleExportSettings}\r\n              className=\"btn btn-primary btn-sm\"\r\n            >\r\n              📤 导出设置\r\n            </button>\r\n            <label className=\"btn btn-secondary btn-sm cursor-pointer\">\r\n              📥 导入设置\r\n              <input\r\n                type=\"file\"\r\n                accept=\".json\"\r\n                onChange={handleImportSettings}\r\n                className=\"hidden\"\r\n              />\r\n            </label>\r\n            <button\r\n              onClick={saveSettings}\r\n              className=\"btn btn-success btn-sm\"\r\n            >\r\n              💾 保存设置\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 重置操作 */}\r\n        <div>\r\n          <h3 className=\"font-semibold mb-sm\">重置操作</h3>\r\n          <div className=\"flex gap-sm\">\r\n            <button\r\n              onClick={() => {\r\n                if (confirm('确定要重置为默认设置吗？')) {\r\n                  resetToDefaults()\r\n                }\r\n              }}\r\n              className=\"btn btn-warning btn-sm\"\r\n            >\r\n              🔄 重置默认\r\n            </button>\r\n            <button\r\n              onClick={() => {\r\n                if (confirm('确定要清除所有设置吗？')) {\r\n                  reset()\r\n                }\r\n              }}\r\n              className=\"btn btn-danger btn-sm\"\r\n            >\r\n              🗑️ 清除所有\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 设置预览 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">👁️ 当前设置预览</h2>\r\n        <div className=\"grid grid-2\">\r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">基础设置</h3>\r\n            <pre className=\"bg-surface p-md rounded text-sm overflow-auto\">\r\n{JSON.stringify({\r\n  theme,\r\n  language,\r\n  fontSize,\r\n  layout,\r\n  notifications,\r\n  autoSave\r\n}, null, 2)}\r\n            </pre>\r\n          </div>\r\n          <div>\r\n            <h3 className=\"font-semibold mb-sm\">完整配置</h3>\r\n            <pre className=\"bg-surface p-md rounded text-sm overflow-auto max-h-64\">\r\n{JSON.stringify(allSettings, null, 2)}\r\n            </pre>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 持久化说明 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">💾 持久化功能</h2>\r\n        <div className=\"text-sm text-secondary space-y-2\">\r\n          <p><strong>🔒 本地存储:</strong> 设置自动保存到浏览器本地存储</p>\r\n          <p><strong>🔄 自动同步:</strong> 页面刷新后设置会自动恢复</p>\r\n          <p><strong>📦 版本管理:</strong> 支持设置版本迁移和兼容性</p>\r\n          <p><strong>🎯 选择性持久化:</strong> 只有重要设置会被持久化</p>\r\n          <p><strong>⚡ 性能优化:</strong> 使用防抖策略减少存储操作</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default SettingsDemo "], "names": ["SettingsDemo", "theme", "useSettingsStore", "selectTheme", "language", "selectLanguage", "notifications", "selectNotifications", "autoSave", "selectAutoSave", "fontSize", "selectFontSize", "layout", "selectLayout", "preferences", "selectPreferences", "advancedSettings", "selectAdvancedSettings", "shortcuts", "selectShortcuts", "colorScheme", "selectColorScheme", "themeLabel", "selectThemeLabel", "languageLabel", "selectLanguageLabel", "layoutLabel", "selectLayoutLabel", "allSettings", "selectAllSettings", "settingsSummary", "selectSettingsSummary", "setTheme", "setLanguage", "toggleNotifications", "toggleAutoSave", "setFontSize", "setLayout", "updatePreference", "togglePreference", "toggleAdvancedSetting", "updateShortcut", "updateColor", "batchUpdate", "applyPreset", "exportSettings", "importSettings", "saveSettings", "resetToDefaults", "reset", "jsxs", "className", "children", "jsx", "totalSettings", "modifiedSettings", "enabledFeatures", "keys", "length", "map", "themeOption", "onClick", "value", "onChange", "e", "target", "type", "min", "max", "parseInt", "layoutOption", "checked", "Object", "entries", "key", "replace", "toLowerCase", "placeholder", "onKeyPress", "trim", "input", "document", "querySelector", "dark", "primary", "secondary", "background", "light", "accessibility", "highContrast", "reduceMotion", "screenReader", "enableKeyboardNavigation", "enableVoiceCommand", "presetName", "preset", "settings", "blob", "Blob", "JSON", "stringify", "url", "URL", "createObjectURL", "a", "createElement", "href", "download", "click", "revokeObjectURL", "accept", "event", "file", "files", "reader", "FileReader", "onload", "parse", "result", "alert", "readAsText", "confirm"], "mappings": "yMAGA,SAASA,IAEP,MAAMC,EAAQC,EAAiBC,GACzBC,EAAWF,EAAiBG,GAC5BC,EAAgBJ,EAAiBK,GACjCC,EAAWN,EAAiBO,GAC5BC,EAAWR,EAAiBS,GAC5BC,EAASV,EAAiBW,GAC1BC,EAAcZ,EAAiBa,GAC/BC,EAAmBd,EAAiBe,GACpCC,EAAYhB,EAAiBiB,GAC7BC,EAAclB,EAAiBmB,GAG/BC,EAAapB,EAAiBqB,GAC9BC,EAAgBtB,EAAiBuB,GACjCC,EAAcxB,EAAiByB,GAC/BC,EAAc1B,EAAiB2B,GAC/BC,EAAkB5B,EAAiB6B,IAGnCC,SACJA,EAAAC,YACAA,EAAAC,oBACAA,EAAAC,eACAA,EAAAC,YACAA,EAAAC,UACAA,EAAAC,iBACAA,EAAAC,iBACAA,EAAAC,sBACAA,EAAAC,eACAA,EAAAC,YACAA,EAAAC,YACAA,EAAAC,YACAA,EAAAC,eACAA,EAAAC,eACAA,EAAAC,aACAA,EAAAC,gBACAA,EAAAC,MACAA,GACE/C,IAkEJ,SACEgD,KAAC,MAAA,CAAIC,UAAU,qBACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,YAC3BC,EAAAA,IAAC,IAAA,CAAEF,UAAU,mBAAmBC,SAAA,2CAMlCF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,kCAAmCC,SAAAtB,EAAgBwB,gBAClED,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,cAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,kCAAmCC,SAAAtB,EAAgByB,mBAClEF,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,aAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,kCAAmCC,SAAAtB,EAAgB0B,kBAClEH,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,cAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,OAAIF,UAAU,+BAAgCC,gBAAOK,KAAKvC,GAAWwC,SACtEL,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,mBAK9CF,KAAC,MAAA,CAAIC,UAAU,cAEbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAG3BF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,GAAAF,KAAC,QAAA,CAAMC,UAAU,aAAaC,SAAA,CAAA,OAAK9B,EAAW,OAC9C+B,EAAAA,IAAC,MAAA,CAAIF,UAAU,cACZC,SAAA,CAAC,QAAS,OAAQ,QAAQO,IAAKC,GAC9BP,EAAAA,IAAC,SAAA,CAECQ,QAAS,IAAM7B,EAAS4B,GACxBT,UAAW,eAAclD,IAAU2D,EAAc,cAAgB,iBAEhER,SAAgB,UAAhBQ,EAA0B,QACV,SAAhBA,EAAyB,QAAU,SAL/BA,WAYbV,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,GAAAF,KAAC,QAAA,CAAMC,UAAU,aAAaC,SAAA,CAAA,OAAK5B,EAAc,OACjD0B,EAAAA,KAAC,SAAA,CACCY,MAAO1D,EACP2D,SAAWC,GAAM/B,EAAY+B,EAAEC,OAAOH,OACtCX,UAAU,eAEVC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CAAOS,MAAM,QAAQV,SAAA,SACtBC,EAAAA,IAAC,SAAA,CAAOS,MAAM,QAAQV,SAAA,SACtBC,EAAAA,IAAC,SAAA,CAAOS,MAAM,QAAQV,SAAA,YACtBC,EAAAA,IAAC,SAAA,CAAOS,MAAM,QAAQV,SAAA,QACtBC,EAAAA,IAAC,SAAA,CAAOS,MAAM,QAAQV,SAAA,gBAK1BF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,GAAAF,KAAC,QAAA,CAAMC,UAAU,aAAaC,SAAA,CAAA,SAAO1C,EAAS,QAC9C2C,EAAAA,IAAC,QAAA,CACCa,KAAK,QACLC,IAAI,KACJC,IAAI,KACJN,MAAOpD,EACPqD,SAAWC,GAAM5B,EAAYiC,SAASL,EAAEC,OAAOH,QAC/CX,UAAU,sBAKdD,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,GAAAF,KAAC,QAAA,CAAMC,UAAU,aAAaC,SAAA,CAAA,OAAK1B,EAAY,OAC/C2B,EAAAA,IAAC,MAAA,CAAIF,UAAU,cACZC,SAAA,CAAC,UAAW,SAAU,WAAWO,IAAKW,GACrCjB,EAAAA,IAAC,SAAA,CAECQ,QAAS,IAAMxB,EAAUiC,GACzBnB,UAAW,eAAcvC,IAAW0D,EAAe,cAAgB,iBAElElB,SAAiB,YAAjBkB,EAA6B,SACZ,WAAjBA,EAA4B,SAAW,SALnCA,WAYbpB,KAAC,MAAA,CAAIC,UAAU,YACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,oCACbC,SAAA,GAAAC,IAAC,QAAKD,SAAA,OACNC,EAAAA,IAAC,QAAA,CACCa,KAAK,WACLK,QAASjE,EACTyD,SAAU7B,EACViB,UAAU,6BAGdD,KAAC,MAAA,CAAIC,UAAU,oCACbC,SAAA,GAAAC,IAAC,QAAKD,SAAA,SACNC,EAAAA,IAAC,QAAA,CACCa,KAAK,WACLK,QAAS/D,EACTuD,SAAU5B,EACVgB,UAAU,mCAOlBD,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,kBAE1B,MAAA,CAAID,UAAU,YACZC,SAAAoB,OAAOC,QAAQ3D,GAAa6C,IAAI,EAAEe,EAAKZ,KACtCZ,EAAAA,KAAC,MAAA,CAAcC,UAAU,oCACvBC,SAAA,CAAAC,EAAAA,IAAC,OAAA,CAAKF,UAAU,aACbC,SAAAsB,EAAIC,QAAQ,WAAY,OAAOC,gBAElCvB,EAAAA,IAAC,QAAA,CACCa,KAAK,WACLK,QAAST,EACTC,SAAU,IAAMxB,EAAiBmC,GACjCvB,UAAU,0BARJuB,QAcdxB,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,QAAA,CAAMF,UAAU,aAAaC,SAAA,YAC9BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,QAAA,CACCa,KAAK,OACLW,YAAY,OACZC,WAAad,IACG,UAAVA,EAAEU,KAAmBV,EAAEC,OAAOH,MAAMiB,SACtCzC,EAAiB0B,EAAEC,OAAOH,OAAO,GACjCE,EAAEC,OAAOH,MAAQ,KAGrBX,UAAU,iBAEZE,EAAAA,IAAC,SAAA,CACCQ,QAAS,KACP,MAAMmB,EAAQC,SAASC,cAAc,6BACjCF,EAAMlB,MAAMiB,SACdzC,EAAiB0C,EAAMlB,OAAO,GAC9BkB,EAAMlB,MAAQ,KAGlBX,UAAU,kBACXC,SAAA,qBASTF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,iBAC1B,MAAA,CAAID,UAAU,cACZC,SAAAoB,OAAOC,QAAQzD,GAAkB2C,IAAI,EAAEe,EAAKZ,KAC3CZ,EAAAA,KAAC,MAAA,CAAcC,UAAU,oCACvBC,SAAA,CAAAC,EAAAA,IAAC,OAAA,CAAKF,UAAU,UACbC,SAAAsB,EAAIC,QAAQ,WAAY,OAAOC,gBAElCvB,EAAAA,IAAC,QAAA,CACCa,KAAK,WACLK,QAAST,EACTC,SAAU,IAAMvB,EAAsBkC,GACtCvB,UAAU,0BARJuB,WAgBhBxB,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,kBAC1B,MAAA,CAAID,UAAU,cACZC,SAAAoB,OAAOC,QAAQrD,GAAauC,IAAI,EAAEe,EAAKZ,KACtCZ,EAAAA,KAAC,MAAA,CAAcC,UAAU,aACvBC,SAAA,CAAAC,EAAAA,IAAC,QAAA,CAAMF,UAAU,aAAcC,SAAAsB,MAC/BxB,KAAC,MAAA,CAAIC,UAAU,2BACbC,SAAA,CAAAC,EAAAA,IAAC,QAAA,CACCa,KAAK,QACLJ,QACAC,SAAWC,GAAMtB,EAAYgC,EAAKV,EAAEC,OAAOH,OAC3CX,UAAU,4BAEZE,EAAAA,IAAC,QAAA,CACCa,KAAK,OACLJ,QACAC,SAAWC,GAAMtB,EAAYgC,EAAKV,EAAEC,OAAOH,OAC3CX,UAAU,8BAbNuB,WAsBhBxB,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,iBAC1B,MAAA,CAAID,UAAU,cACZC,SAAAoB,OAAOC,QAAQvD,GAAWyC,IAAI,EAAEe,EAAKZ,KACpCZ,EAAAA,KAAC,MAAA,CAAcC,UAAU,oCACvBC,SAAA,CAAAC,EAAAA,IAAC,OAAA,CAAKF,UAAU,UAAWC,SAAAsB,IAC3BrB,EAAAA,IAAC,QAAA,CACCa,KAAK,OACLJ,QACAC,SAAWC,GAAMvB,EAAeiC,EAAKV,EAAEC,OAAOH,OAC9CX,UAAU,4BACV0B,YAAY,WAPNH,WAehBxB,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,eAG3BF,KAAC,MAAA,CAAIC,UAAU,QACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,WACpCC,IAAC,MAAA,CAAIF,UAAU,cACZC,SAAAoB,OAAOC,QAtTF,CACdU,KAAM,CACJlF,MAAO,OACPS,SAAU,GACVU,YAAa,CACXgE,QAAS,UACTC,UAAW,UACXC,WAAY,YAGhBC,MAAO,CACLtF,MAAO,QACPS,SAAU,GACVU,YAAa,CACXgE,QAAS,UACTC,UAAW,UACXC,WAAY,YAGhBE,cAAe,CACb9E,SAAU,GACVI,YAAa,CACX2E,cAAc,EACdC,cAAc,EACdC,cAAc,GAEhB3E,iBAAkB,CAChB4E,0BAA0B,EAC1BC,oBAAoB,MA0RSlC,IAAI,EAAEmC,EAAYC,KACzC1C,EAAAA,IAAC,SAAA,CAECQ,QAAS,IAAMjB,EAAYkD,EAAYC,GACvC5C,UAAU,sBAETC,SAAe,SAAf0C,EAAwB,UACT,UAAfA,EAAyB,UAAY,SALjCA,WAYb5C,KAAC,MAAA,CAAIC,UAAU,QACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,WACpCF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CACCQ,QAAS,IAAMlB,EAAY,CACzBjC,SAAU,GACVJ,eAAe,EACfE,UAAU,EACVP,MAAO,SAETkD,UAAU,yBACXC,SAAA,YAGDC,EAAAA,IAAC,SAAA,CACCQ,QAAS,IAAMlB,EAAY,CACzB,0BAA0B,EAC1B,sBAAsB,EACtB,gCAAgC,IAElCQ,UAAU,yBACXC,SAAA,mBAOLF,KAAC,MAAA,CAAIC,UAAU,QACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,WACpCF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CACCQ,QAhTiB,KAC3B,MAAMmC,EAAWnD,IACXoD,EAAO,IAAIC,KAAK,CAACC,KAAKC,UAAUJ,EAAU,KAAM,IAAK,CAAE9B,KAAM,qBAC7DmC,EAAMC,IAAIC,gBAAgBN,GAC1BO,EAAIvB,SAASwB,cAAc,KACjCD,EAAEE,KAAOL,EACTG,EAAEG,SAAW,gBACbH,EAAEI,QACFN,IAAIO,gBAAgBR,IAySVlD,UAAU,yBACXC,SAAA,cAGDF,KAAC,QAAA,CAAMC,UAAU,0CAA0CC,SAAA,CAAA,UAEzDC,EAAAA,IAAC,QAAA,CACCa,KAAK,OACL4C,OAAO,QACP/C,SA5UgBgD,IAC5B,MAAMC,EAAOD,EAAM9C,OAAOgD,MAAM,GAChC,GAAID,EAAM,CACR,MAAME,EAAS,IAAIC,WACnBD,EAAOE,OAAUpD,IACf,IACE,MAAMgC,EAAWG,KAAKkB,MAAMrD,EAAEC,OAAOqD,QACrCxE,EAAekD,GACfuB,MAAM,UACC,CAAA,MACNA,MAAM,iBACR,GAEHL,EAAOM,WAAWR,EACpB,GA+TY7D,UAAU,cAGdE,EAAAA,IAAC,SAAA,CACCQ,QAASd,EACTI,UAAU,yBACXC,SAAA,yBAOJ,MAAA,CACCA,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,WACpCF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CACCQ,QAAS,KACH4D,QAAQ,iBACVzE,KAGJG,UAAU,yBACXC,SAAA,YAGDC,EAAAA,IAAC,SAAA,CACCQ,QAAS,KACH4D,QAAQ,gBACVxE,KAGJE,UAAU,wBACXC,SAAA,wBAQPF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,iBAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,OAAC,MAAA,CACCE,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,SACpCC,EAAAA,IAAC,MAAA,CAAIF,UAAU,gDAC1BC,cAAKgD,UAAU,CACdnG,QACAG,WACAM,WACAE,SACAN,gBACAE,YACC,KAAM,eAGE,MAAA,CACC4C,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,SACpCC,EAAAA,IAAC,OAAIF,UAAU,yDAC1BC,cAAKgD,UAAUxE,EAAa,KAAM,gBAO7BsB,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,eAC3BF,KAAC,MAAA,CAAIC,UAAU,mCACbC,SAAA,CAAAF,OAAC,IAAA,CAAEE,SAAA,GAAAC,IAAC,UAAOD,SAAA,aAAiB,4BAC3B,IAAA,CAAEA,SAAA,GAAAC,IAAC,UAAOD,SAAA,aAAiB,0BAC3B,IAAA,CAAEA,SAAA,GAAAC,IAAC,UAAOD,SAAA,aAAiB,0BAC3B,IAAA,CAAEA,SAAA,GAAAC,IAAC,UAAOD,SAAA,eAAmB,yBAC7B,IAAA,CAAEA,SAAA,GAAAC,IAAC,UAAOD,SAAA,YAAgB,2BAKrC"}