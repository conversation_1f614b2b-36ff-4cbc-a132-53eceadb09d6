{"version": 3, "file": "page-apiintegration-CR3Uz1sg.js", "sources": ["../../src/pages/ApiIntegration.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react'\r\nimport { useApiStore, selectUsers, selectCurrentUser, selectPosts, selectLoadingStates, selectErrors, selectRequestLogs, selectStats } from '@/stores/apiStore'\r\n\r\nfunction ApiIntegration() {\r\n  // 使用Zustand获取状态\r\n  const users = useApiStore(selectUsers)\r\n  const currentUser = useApiStore(selectCurrentUser)\r\n  const posts = useApiStore(selectPosts)\r\n  const loadingStates = useApiStore(selectLoadingStates)\r\n  const errors = useApiStore(selectErrors)\r\n  const requestLogs = useApiStore(selectRequestLogs)\r\n  const stats = useApiStore(selectStats)\r\n\r\n  // 获取actions\r\n  const {\r\n    fetchUsers,\r\n    fetchUserById,\r\n    createUser,\r\n    updateUser,\r\n    deleteUser,\r\n    fetchPosts,\r\n    setCurrentUser,\r\n    clearError,\r\n    clearRequestLogs,\r\n    clearCache,\r\n    reset\r\n  } = useApiStore()\r\n\r\n  // 本地状态\r\n  const [selectedUserId, setSelectedUserId] = useState('')\r\n  const [newUser, setNewUser] = useState({\r\n    name: '',\r\n    username: '',\r\n    email: '',\r\n    phone: ''\r\n  })\r\n  const [editingUser, setEditingUser] = useState(null)\r\n  const [showCreateForm, setShowCreateForm] = useState(false)\r\n\r\n  // 组件挂载时获取用户列表\r\n  useEffect(() => {\r\n    if (users.length === 0) {\r\n      fetchUsers()\r\n    }\r\n  }, [fetchUsers, users.length])\r\n\r\n  // 处理用户选择\r\n  const handleUserSelect = async (userId) => {\r\n    setSelectedUserId(userId)\r\n    if (userId) {\r\n      try {\r\n        await fetchUserById(parseInt(userId))\r\n      } catch (error) {\r\n        console.error('获取用户详情失败:', error)\r\n      }\r\n    } else {\r\n      setCurrentUser(null)\r\n    }\r\n  }\r\n\r\n  // 创建新用户\r\n  const handleCreateUser = async () => {\r\n    if (!newUser.name.trim() || !newUser.email.trim()) {\r\n      alert('请填写用户名和邮箱')\r\n      return\r\n    }\r\n\r\n    try {\r\n      await createUser(newUser)\r\n      setNewUser({ name: '', username: '', email: '', phone: '' })\r\n      setShowCreateForm(false)\r\n      alert('用户创建成功！')\r\n    } catch (error) {\r\n      console.error('创建用户失败:', error)\r\n    }\r\n  }\r\n\r\n  // 更新用户\r\n  const handleUpdateUser = async () => {\r\n    if (!editingUser) return\r\n\r\n    try {\r\n      await updateUser(editingUser.id, editingUser)\r\n      setEditingUser(null)\r\n      alert('用户更新成功！')\r\n    } catch (error) {\r\n      console.error('更新用户失败:', error)\r\n    }\r\n  }\r\n\r\n  // 删除用户\r\n  const handleDeleteUser = async (userId) => {\r\n    if (!confirm('确定要删除这个用户吗？')) return\r\n\r\n    try {\r\n      await deleteUser(userId)\r\n      if (currentUser && currentUser.id === userId) {\r\n        setCurrentUser(null)\r\n        setSelectedUserId('')\r\n      }\r\n      alert('用户删除成功！')\r\n    } catch (error) {\r\n      console.error('删除用户失败:', error)\r\n    }\r\n  }\r\n\r\n  // 获取用户文章\r\n  const handleFetchUserPosts = async (userId) => {\r\n    try {\r\n      await fetchPosts(userId)\r\n    } catch (error) {\r\n      console.error('获取用户文章失败:', error)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"api-integration-page\">\r\n      <div className=\"card-header\">\r\n        <h1 className=\"card-title\">🌐 API集成</h1>\r\n        <p className=\"card-description\">\r\n          展示与外部API的集成，包括HTTP请求、缓存、错误处理、状态管理等功能\r\n        </p>\r\n      </div>\r\n\r\n      {/* 统计信息 */}\r\n      <div className=\"card mb-lg\">\r\n        <h2 className=\"card-title\">📊 API统计</h2>\r\n        <div className=\"grid grid-4\">\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-primary\">{stats.totalUsers}</div>\r\n            <div className=\"text-sm text-secondary\">用户总数</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-success\">{stats.totalRequests}</div>\r\n            <div className=\"text-sm text-secondary\">请求次数</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-warning\">{stats.cacheSize}</div>\r\n            <div className=\"text-sm text-secondary\">缓存条目</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-danger\">{Object.keys(errors).length}</div>\r\n            <div className=\"text-sm text-secondary\">错误数量</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 错误提示 */}\r\n      {Object.keys(errors).length > 0 && (\r\n        <div className=\"alert alert-danger mb-lg\">\r\n          <strong>API错误：</strong>\r\n          <ul className=\"mt-sm\">\r\n            {Object.entries(errors).map(([operation, error]) => (\r\n              <li key={operation}>\r\n                <strong>{operation}:</strong> {error}\r\n              </li>\r\n            ))}\r\n          </ul>\r\n          <button onClick={() => clearError()} className=\"btn btn-sm btn-danger mt-sm\">\r\n            清除所有错误\r\n          </button>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"grid grid-2\">\r\n        {/* 用户管理 */}\r\n        <div className=\"card\">\r\n          <div className=\"flex justify-between items-center mb-md\">\r\n            <h2 className=\"card-title\">👥 用户管理</h2>\r\n            <div className=\"flex gap-sm\">\r\n              <button \r\n                onClick={() => setShowCreateForm(!showCreateForm)}\r\n                className=\"btn btn-success btn-sm\"\r\n              >\r\n                {showCreateForm ? '取消' : '添加用户'}\r\n              </button>\r\n              <button \r\n                onClick={fetchUsers}\r\n                disabled={loadingStates.fetchUsers}\r\n                className=\"btn btn-primary btn-sm\"\r\n              >\r\n                {loadingStates.fetchUsers ? '加载中...' : '刷新列表'}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 创建用户表单 */}\r\n          {showCreateForm && (\r\n            <div className=\"mb-md p-md bg-surface rounded\">\r\n              <h3 className=\"font-semibold mb-md\">创建新用户</h3>\r\n              <div className=\"space-y-2\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={newUser.name}\r\n                  onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}\r\n                  className=\"form-control\"\r\n                  placeholder=\"用户名 *\"\r\n                />\r\n                <input\r\n                  type=\"text\"\r\n                  value={newUser.username}\r\n                  onChange={(e) => setNewUser({ ...newUser, username: e.target.value })}\r\n                  className=\"form-control\"\r\n                  placeholder=\"用户名简称\"\r\n                />\r\n                <input\r\n                  type=\"email\"\r\n                  value={newUser.email}\r\n                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}\r\n                  className=\"form-control\"\r\n                  placeholder=\"邮箱 *\"\r\n                />\r\n                <input\r\n                  type=\"tel\"\r\n                  value={newUser.phone}\r\n                  onChange={(e) => setNewUser({ ...newUser, phone: e.target.value })}\r\n                  className=\"form-control\"\r\n                  placeholder=\"电话\"\r\n                />\r\n              </div>\r\n              <div className=\"flex gap-sm mt-md\">\r\n                <button \r\n                  onClick={handleCreateUser}\r\n                  disabled={loadingStates.createUser}\r\n                  className=\"btn btn-success\"\r\n                >\r\n                  {loadingStates.createUser ? '创建中...' : '创建用户'}\r\n                </button>\r\n                <button \r\n                  onClick={() => setShowCreateForm(false)}\r\n                  className=\"btn btn-secondary\"\r\n                >\r\n                  取消\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* 用户列表 */}\r\n          <div className=\"space-y-1\">\r\n            {users.map((user) => (\r\n              <div \r\n                key={user.id} \r\n                className={`p-sm border rounded cursor-pointer transition-all ${\r\n                  selectedUserId == user.id ? 'bg-primary text-white' : 'hover:bg-surface'\r\n                }`}\r\n                onClick={() => handleUserSelect(user.id)}\r\n              >\r\n                <div className=\"flex justify-between items-center\">\r\n                  <div>\r\n                    <div className=\"font-semibold\">{user.name}</div>\r\n                    <div className=\"text-sm opacity-75\">{user.email}</div>\r\n                  </div>\r\n                  <div className=\"flex gap-xs\">\r\n                    <button\r\n                      onClick={(e) => {\r\n                        e.stopPropagation()\r\n                        setEditingUser({ ...user })\r\n                      }}\r\n                      className=\"btn btn-sm btn-primary\"\r\n                    >\r\n                      ✏️\r\n                    </button>\r\n                    <button\r\n                      onClick={(e) => {\r\n                        e.stopPropagation()\r\n                        handleDeleteUser(user.id)\r\n                      }}\r\n                      className=\"btn btn-sm btn-danger\"\r\n                    >\r\n                      🗑️\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n            \r\n            {users.length === 0 && !loadingStates.fetchUsers && (\r\n              <div className=\"text-center text-muted py-lg\">\r\n                暂无用户数据，点击刷新列表获取数据\r\n              </div>\r\n            )}\r\n\r\n            {loadingStates.fetchUsers && (\r\n              <div className=\"text-center py-lg\">\r\n                <div className=\"spinner\"></div>\r\n                <div className=\"text-sm text-muted mt-sm\">加载用户列表中...</div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* 用户详情 */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">👤 用户详情</h2>\r\n          \r\n          {currentUser ? (\r\n            <div>\r\n              <div className=\"mb-md\">\r\n                <h3 className=\"font-semibold text-lg\">{currentUser.name}</h3>\r\n                <div className=\"text-sm text-secondary space-y-1\">\r\n                  <div><strong>用户名:</strong> {currentUser.username}</div>\r\n                  <div><strong>邮箱:</strong> {currentUser.email}</div>\r\n                  <div><strong>电话:</strong> {currentUser.phone}</div>\r\n                  {currentUser.website && (\r\n                    <div><strong>网站:</strong> {currentUser.website}</div>\r\n                  )}\r\n                  {currentUser.company && (\r\n                    <div><strong>公司:</strong> {currentUser.company.name}</div>\r\n                  )}\r\n                  {currentUser.address && (\r\n                    <div><strong>地址:</strong> {currentUser.address.city}, {currentUser.address.street}</div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex gap-sm\">\r\n                <button\r\n                  onClick={() => handleFetchUserPosts(currentUser.id)}\r\n                  disabled={loadingStates.fetchPosts}\r\n                  className=\"btn btn-info btn-sm\"\r\n                >\r\n                  {loadingStates.fetchPosts ? '加载中...' : '获取文章'}\r\n                </button>\r\n                <button\r\n                  onClick={() => setEditingUser({ ...currentUser })}\r\n                  className=\"btn btn-warning btn-sm\"\r\n                >\r\n                  编辑用户\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"text-center text-muted py-lg\">\r\n              请从左侧选择一个用户查看详情\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 编辑用户弹窗 */}\r\n      {editingUser && (\r\n        <div className=\"card mt-lg\">\r\n          <h2 className=\"card-title\">✏️ 编辑用户</h2>\r\n          <div className=\"grid grid-2\">\r\n            <div>\r\n              <div className=\"form-group\">\r\n                <label className=\"form-label\">用户名</label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={editingUser.name}\r\n                  onChange={(e) => setEditingUser({ ...editingUser, name: e.target.value })}\r\n                  className=\"form-control\"\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label className=\"form-label\">用户名简称</label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={editingUser.username}\r\n                  onChange={(e) => setEditingUser({ ...editingUser, username: e.target.value })}\r\n                  className=\"form-control\"\r\n                />\r\n              </div>\r\n            </div>\r\n            <div>\r\n              <div className=\"form-group\">\r\n                <label className=\"form-label\">邮箱</label>\r\n                <input\r\n                  type=\"email\"\r\n                  value={editingUser.email}\r\n                  onChange={(e) => setEditingUser({ ...editingUser, email: e.target.value })}\r\n                  className=\"form-control\"\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label className=\"form-label\">电话</label>\r\n                <input\r\n                  type=\"tel\"\r\n                  value={editingUser.phone}\r\n                  onChange={(e) => setEditingUser({ ...editingUser, phone: e.target.value })}\r\n                  className=\"form-control\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex gap-sm\">\r\n            <button\r\n              onClick={handleUpdateUser}\r\n              disabled={loadingStates.updateUser}\r\n              className=\"btn btn-success\"\r\n            >\r\n              {loadingStates.updateUser ? '更新中...' : '保存更改'}\r\n            </button>\r\n            <button\r\n              onClick={() => setEditingUser(null)}\r\n              className=\"btn btn-secondary\"\r\n            >\r\n              取消\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* 文章列表 */}\r\n      {posts.length > 0 && (\r\n        <div className=\"card mt-lg\">\r\n          <h2 className=\"card-title\">📝 用户文章</h2>\r\n          <div className=\"space-y-2\">\r\n            {posts.slice(0, 5).map((post) => (\r\n              <div key={post.id} className=\"p-md bg-surface rounded\">\r\n                <h3 className=\"font-semibold mb-sm\">{post.title}</h3>\r\n                <p className=\"text-sm text-secondary\">{post.body}</p>\r\n              </div>\r\n            ))}\r\n            {posts.length > 5 && (\r\n              <div className=\"text-center text-muted\">\r\n                还有 {posts.length - 5} 篇文章...\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* 请求日志 */}\r\n      <div className=\"card mt-lg\">\r\n        <div className=\"flex justify-between items-center mb-md\">\r\n          <h2 className=\"card-title\">📋 请求日志</h2>\r\n          <div className=\"flex gap-sm\">\r\n            <button onClick={clearRequestLogs} className=\"btn btn-secondary btn-sm\">\r\n              清空日志\r\n            </button>\r\n            <button onClick={clearCache} className=\"btn btn-warning btn-sm\">\r\n              清除缓存\r\n            </button>\r\n            <button onClick={reset} className=\"btn btn-danger btn-sm\">\r\n              重置所有\r\n            </button>\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"space-y-1 max-h-64 overflow-y-auto\">\r\n          {requestLogs.slice(0, 10).map((log) => (\r\n            <div key={log.id} className=\"flex justify-between items-center p-sm bg-surface rounded text-sm\">\r\n              <div className=\"flex items-center gap-sm\">\r\n                <span className={`badge ${log.status === 'cached' ? 'badge-info' : \r\n                  typeof log.status === 'number' && log.status < 400 ? 'badge-success' : 'badge-danger'}`}>\r\n                  {log.method}\r\n                </span>\r\n                <span>{log.url}</span>\r\n                <span className=\"text-muted\">- {log.message}</span>\r\n              </div>\r\n              <span className=\"text-muted\">\r\n                {new Date(log.timestamp).toLocaleTimeString()}\r\n              </span>\r\n            </div>\r\n          ))}\r\n          \r\n          {requestLogs.length === 0 && (\r\n            <div className=\"text-center text-muted py-lg\">\r\n              暂无请求日志\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* API使用说明 */}\r\n      <div className=\"card mt-lg\">\r\n        <h2 className=\"card-title\">📖 API功能说明</h2>\r\n        <div className=\"grid grid-2\">\r\n          <div>\r\n            <h3 className=\"font-semibold mb-md\">🔧 功能特性</h3>\r\n            <ul className=\"text-sm text-secondary space-y-1\">\r\n              <li>• <strong>自动缓存</strong>：5分钟有效期，减少重复请求</li>\r\n              <li>• <strong>加载状态</strong>：细粒度的加载状态管理</li>\r\n              <li>• <strong>错误处理</strong>：统一的错误处理和展示</li>\r\n              <li>• <strong>请求日志</strong>：详细的请求历史记录</li>\r\n              <li>• <strong>CRUD操作</strong>：完整的增删改查功能</li>\r\n            </ul>\r\n          </div>\r\n          <div>\r\n            <h3 className=\"font-semibold mb-md\">🌐 API端点</h3>\r\n            <div className=\"text-sm text-secondary space-y-1\">\r\n              <div><strong>GET</strong> /users - 获取用户列表</div>\r\n              <div><strong>GET</strong> /users/:id - 获取用户详情</div>\r\n              <div><strong>POST</strong> /users - 创建新用户</div>\r\n              <div><strong>PUT</strong> /users/:id - 更新用户</div>\r\n              <div><strong>DELETE</strong> /users/:id - 删除用户</div>\r\n              <div><strong>GET</strong> /users/:id/posts - 获取用户文章</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default ApiIntegration "], "names": ["ApiIntegration", "users", "useApiStore", "selectUsers", "currentUser", "selectCurrentUser", "posts", "selectPosts", "loadingStates", "selectLoadingStates", "errors", "selectErrors", "requestLogs", "selectRequestLogs", "stats", "selectStats", "fetchUsers", "fetchUserById", "createUser", "updateUser", "deleteUser", "fetchPosts", "setCurrentUser", "clearError", "clearRequestLogs", "clearCache", "reset", "selectedUserId", "setSelectedUserId", "useState", "newUser", "setNewUser", "name", "username", "email", "phone", "editingUser", "setEditingUser", "showCreateForm", "setShowCreateForm", "useEffect", "length", "jsxs", "className", "children", "jsx", "totalUsers", "totalRequests", "cacheSize", "keys", "Object", "entries", "map", "operation", "error", "onClick", "disabled", "type", "value", "onChange", "e", "target", "placeholder", "async", "trim", "alert", "user", "id", "userId", "parseInt", "handleUserSelect", "stopPropagation", "confirm", "handleDeleteUser", "website", "company", "address", "city", "street", "handleFetchUserPosts", "slice", "post", "title", "body", "log", "status", "method", "url", "message", "Date", "timestamp", "toLocaleTimeString"], "mappings": "mJAGA,SAASA,IAEP,MAAMC,EAAQC,EAAYC,GACpBC,EAAcF,EAAYG,GAC1BC,EAAQJ,EAAYK,GACpBC,EAAgBN,EAAYO,GAC5BC,EAASR,EAAYS,GACrBC,EAAcV,EAAYW,GAC1BC,EAAQZ,EAAYa,IAGpBC,WACJA,EAAAC,cACAA,EAAAC,WACAA,EAAAC,WACAA,EAAAC,WACAA,EAAAC,WACAA,EAAAC,eACAA,EAAAC,WACAA,EAAAC,iBACAA,EAAAC,WACAA,EAAAC,MACAA,GACExB,KAGGyB,EAAgBC,GAAqBC,EAAAA,SAAS,KAC9CC,EAASC,GAAcF,WAAS,CACrCG,KAAM,GACNC,SAAU,GACVC,MAAO,GACPC,MAAO,MAEFC,EAAaC,GAAkBR,EAAAA,SAAS,OACxCS,EAAgBC,GAAqBV,EAAAA,UAAS,GAGrDW,EAAAA,UAAU,KACa,IAAjBvC,EAAMwC,QACRzB,KAED,CAACA,EAAYf,EAAMwC,SAuEtB,SACEC,KAAC,MAAA,CAAIC,UAAU,uBACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,aAC3BC,EAAAA,IAAC,IAAA,CAAEF,UAAU,mBAAmBC,SAAA,8CAMlCF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,eAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,kCAAmCC,SAAA9B,EAAMgC,aACxDD,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,cAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,kCAAmCC,SAAA9B,EAAMiC,gBACxDF,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,cAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,kCAAmCC,SAAA9B,EAAMkC,YACxDH,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,cAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,OAAIF,UAAU,iCAAkCC,gBAAOK,KAAKvC,GAAQ+B,SACrEI,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,kBAM7CM,OAAOD,KAAKvC,GAAQ+B,OAAS,GAC5BC,EAAAA,KAAC,MAAA,CAAIC,UAAU,2BACbC,SAAA,GAAAC,IAAC,UAAOD,SAAA,WACRC,EAAAA,IAAC,KAAA,CAAGF,UAAU,QACXC,gBAAOO,QAAQzC,GAAQ0C,IAAI,EAAEC,EAAWC,YACtC,KAAA,CACCV,SAAA,CAAAF,OAAC,SAAA,CAAQE,SAAA,CAAAS,EAAU,OAAU,IAAEC,IADxBD,MAKbR,MAAC,UAAOU,QAAS,IAAMhC,IAAcoB,UAAU,8BAA8BC,SAAA,gBAMjFF,KAAC,MAAA,CAAIC,UAAU,cAEbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,0CACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CACCU,QAAS,IAAMhB,GAAmBD,GAClCK,UAAU,yBAETC,WAAiB,KAAO,SAE3BC,EAAAA,IAAC,SAAA,CACCU,QAASvC,EACTwC,SAAUhD,EAAcQ,WACxB2B,UAAU,yBAETC,SAAApC,EAAcQ,WAAa,SAAW,eAM5CsB,GACCI,EAAAA,KAAC,MAAA,CAAIC,UAAU,gCACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,YACpCF,KAAC,MAAA,CAAIC,UAAU,YACbC,SAAA,CAAAC,EAAAA,IAAC,QAAA,CACCY,KAAK,OACLC,MAAO5B,EAAQE,KACf2B,SAAWC,GAAM7B,EAAW,IAAKD,EAASE,KAAM4B,EAAEC,OAAOH,QACzDf,UAAU,eACVmB,YAAY,UAEdjB,EAAAA,IAAC,QAAA,CACCY,KAAK,OACLC,MAAO5B,EAAQG,SACf0B,SAAWC,GAAM7B,EAAW,IAAKD,EAASG,SAAU2B,EAAEC,OAAOH,QAC7Df,UAAU,eACVmB,YAAY,UAEdjB,EAAAA,IAAC,QAAA,CACCY,KAAK,QACLC,MAAO5B,EAAQI,MACfyB,SAAWC,GAAM7B,EAAW,IAAKD,EAASI,MAAO0B,EAAEC,OAAOH,QAC1Df,UAAU,eACVmB,YAAY,SAEdjB,EAAAA,IAAC,QAAA,CACCY,KAAK,MACLC,MAAO5B,EAAQK,MACfwB,SAAWC,GAAM7B,EAAW,IAAKD,EAASK,MAAOyB,EAAEC,OAAOH,QAC1Df,UAAU,eACVmB,YAAY,YAGhBpB,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CACCU,QAjKSQ,UACvB,GAAKjC,EAAQE,KAAKgC,QAAWlC,EAAQI,MAAM8B,OAK3C,UACQ9C,EAAWY,GACjBC,EAAW,CAAEC,KAAM,GAAIC,SAAU,GAAIC,MAAO,GAAIC,MAAO,KACvDI,GAAkB,GAClB0B,MAAM,UACR,OAASX,GAET,MAXEW,MAAM,cAgKMT,SAAUhD,EAAcU,WACxByB,UAAU,kBAETC,SAAApC,EAAcU,WAAa,SAAW,SAEzC2B,EAAAA,IAAC,SAAA,CACCU,QAAS,IAAMhB,GAAkB,GACjCI,UAAU,oBACXC,SAAA,eAQPF,KAAC,MAAA,CAAIC,UAAU,YACZC,SAAA,CAAA3C,EAAMmD,IAAKc,GACVrB,EAAAA,IAAC,MAAA,CAECF,UAAW,sDACThB,GAAkBuC,EAAKC,GAAK,wBAA0B,oBAExDZ,QAAS,IAvMEQ,OAAOK,IAE9B,GADAxC,EAAkBwC,GACdA,EACF,UACQnD,EAAcoD,SAASD,GAC/B,OAASd,GAET,MAEAhC,EAAe,OA8LUgD,CAAiBJ,EAAKC,IAErCvB,SAAAF,EAAAA,KAAC,MAAA,CAAIC,UAAU,oCACbC,SAAA,CAAAF,OAAC,MAAA,CACCE,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,gBAAiBC,SAAAsB,EAAKlC,OACrCa,EAAAA,IAAC,MAAA,CAAIF,UAAU,qBAAsBC,WAAKV,aAE5CQ,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CACCU,QAAUK,IACRA,EAAEW,kBACFlC,EAAe,IAAK6B,KAEtBvB,UAAU,yBACXC,SAAA,OAGDC,EAAAA,IAAC,SAAA,CACCU,QAAUK,IACRA,EAAEW,kBA9KCR,OAAOK,IAC9B,GAAKI,QAAQ,eAEb,UACQpD,EAAWgD,GACbhE,GAAeA,EAAY+D,KAAOC,IACpC9C,EAAe,MACfM,EAAkB,KAEpBqC,MAAM,UACR,OAASX,GAET,GAmKoBmB,CAAiBP,EAAKC,KAExBxB,UAAU,wBACXC,SAAA,eA3BAsB,EAAKC,KAmCI,IAAjBlE,EAAMwC,SAAiBjC,EAAcQ,YACpC6B,EAAAA,IAAC,MAAA,CAAIF,UAAU,+BAA+BC,SAAA,sBAK/CpC,EAAcQ,YACb0B,OAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,GAAAC,IAAC,MAAA,CAAIF,UAAU,YACfE,EAAAA,IAAC,MAAA,CAAIF,UAAU,2BAA2BC,SAAA,0BAOlDF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,YAE1BxC,SACE,MAAA,CACCwC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,QACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,wBAAyBC,SAAAxC,EAAY4B,SACnDU,KAAC,MAAA,CAAIC,UAAU,mCACbC,SAAA,CAAAF,OAAC,MAAA,CAAIE,SAAA,GAAAC,IAAC,UAAOD,SAAA,SAAa,IAAExC,EAAY6B,mBACvC,MAAA,CAAIW,SAAA,GAAAC,IAAC,UAAOD,SAAA,QAAY,IAAExC,EAAY8B,gBACtC,MAAA,CAAIU,SAAA,GAAAC,IAAC,UAAOD,SAAA,QAAY,IAAExC,EAAY+B,SACtC/B,EAAYsE,SACXhC,EAAAA,KAAC,MAAA,CAAIE,SAAA,GAAAC,IAAC,UAAOD,SAAA,QAAY,IAAExC,EAAYsE,WAExCtE,EAAYuE,SACXjC,EAAAA,KAAC,MAAA,CAAIE,SAAA,GAAAC,IAAC,UAAOD,SAAA,QAAY,IAAExC,EAAYuE,QAAQ3C,QAEhD5B,EAAYwE,SACXlC,EAAAA,KAAC,MAAA,CAAIE,SAAA,GAAAC,IAAC,UAAOD,SAAA,QAAY,IAAExC,EAAYwE,QAAQC,KAAK,KAAGzE,EAAYwE,QAAQE,kBAKjFpC,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CACCU,QAAS,IAnNIQ,OAAOK,IAClC,UACQ/C,EAAW+C,EACnB,OAASd,GAET,GA8M6ByB,CAAqB3E,EAAY+D,IAChDX,SAAUhD,EAAca,WACxBsB,UAAU,sBAETC,SAAApC,EAAca,WAAa,SAAW,SAEzCwB,EAAAA,IAAC,SAAA,CACCU,QAAS,IAAMlB,EAAe,IAAKjC,IACnCuC,UAAU,yBACXC,SAAA,eAMLC,EAAAA,IAAC,MAAA,CAAIF,UAAU,+BAA+BC,SAAA,yBAQnDR,GACCM,EAAAA,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,OAAC,MAAA,CACCE,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,QAAA,CAAMF,UAAU,aAAaC,SAAA,QAC9BC,EAAAA,IAAC,QAAA,CACCY,KAAK,OACLC,MAAOtB,EAAYJ,KACnB2B,SAAWC,GAAMvB,EAAe,IAAKD,EAAaJ,KAAM4B,EAAEC,OAAOH,QACjEf,UAAU,sBAGdD,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,QAAA,CAAMF,UAAU,aAAaC,SAAA,UAC9BC,EAAAA,IAAC,QAAA,CACCY,KAAK,OACLC,MAAOtB,EAAYH,SACnB0B,SAAWC,GAAMvB,EAAe,IAAKD,EAAaH,SAAU2B,EAAEC,OAAOH,QACrEf,UAAU,8BAIf,MAAA,CACCC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,QAAA,CAAMF,UAAU,aAAaC,SAAA,OAC9BC,EAAAA,IAAC,QAAA,CACCY,KAAK,QACLC,MAAOtB,EAAYF,MACnByB,SAAWC,GAAMvB,EAAe,IAAKD,EAAaF,MAAO0B,EAAEC,OAAOH,QAClEf,UAAU,sBAGdD,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,QAAA,CAAMF,UAAU,aAAaC,SAAA,OAC9BC,EAAAA,IAAC,QAAA,CACCY,KAAK,MACLC,MAAOtB,EAAYD,MACnBwB,SAAWC,GAAMvB,EAAe,IAAKD,EAAaD,MAAOyB,EAAEC,OAAOH,QAClEf,UAAU,4BAKlBD,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CACCU,QAtTaQ,UACvB,GAAK3B,EAEL,UACQjB,EAAWiB,EAAY+B,GAAI/B,GACjCC,EAAe,MACf4B,MAAM,UACR,OAASX,GAET,GA8SUE,SAAUhD,EAAcW,WACxBwB,UAAU,kBAETC,SAAApC,EAAcW,WAAa,SAAW,SAEzC0B,EAAAA,IAAC,SAAA,CACCU,QAAS,IAAMlB,EAAe,MAC9BM,UAAU,oBACXC,SAAA,aAQNtC,EAAMmC,OAAS,GACdC,EAAAA,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAC3BF,KAAC,MAAA,CAAIC,UAAU,YACZC,SAAA,CAAAtC,EAAM0E,MAAM,EAAG,GAAG5B,IAAK6B,GACtBvC,EAAAA,KAAC,MAAA,CAAkBC,UAAU,0BAC3BC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAuBC,SAAAqC,EAAKC,QAC1CrC,EAAAA,IAAC,IAAA,CAAEF,UAAU,yBAA0BC,WAAKuC,SAFpCF,EAAKd,KAKhB7D,EAAMmC,OAAS,GACdC,EAAAA,KAAC,MAAA,CAAIC,UAAU,yBAAyBC,SAAA,CAAA,MAClCtC,EAAMmC,OAAS,EAAE,qBAQ/BC,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,0CACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,MAAC,SAAA,CAAOU,QAAS/B,EAAkBmB,UAAU,2BAA2BC,SAAA,eAGvE,SAAA,CAAOW,QAAS9B,EAAYkB,UAAU,yBAAyBC,SAAA,eAG/D,SAAA,CAAOW,QAAS7B,EAAOiB,UAAU,wBAAwBC,SAAA,iBAM9DF,KAAC,MAAA,CAAIC,UAAU,qCACZC,SAAA,CAAAhC,EAAYoE,MAAM,EAAG,IAAI5B,IAAKgC,GAC7B1C,EAAAA,KAAC,MAAA,CAAiBC,UAAU,oEAC1BC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,2BACbC,SAAA,CAAAC,MAAC,QAAKF,UAAW,UAAwB,WAAfyC,EAAIC,OAAsB,aAC5B,iBAAfD,EAAIC,QAAuBD,EAAIC,OAAS,IAAM,gBAAkB,gBACtEzC,WAAI0C,WAEPzC,IAAC,OAAA,CAAMD,SAAAwC,EAAIG,QACX7C,KAAC,OAAA,CAAKC,UAAU,aAAaC,SAAA,CAAA,KAAGwC,EAAII,cAEtC3C,EAAAA,IAAC,OAAA,CAAKF,UAAU,aACbC,SAAA,IAAI6C,KAAKL,EAAIM,WAAWC,yBAVnBP,EAAIjB,KAeQ,IAAvBvD,EAAY6B,cACV,MAAA,CAAIE,UAAU,+BAA+BC,SAAA,mBAQpDF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,iBAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,OAAC,MAAA,CACCE,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,cACpCF,KAAC,KAAA,CAAGC,UAAU,mCACZC,SAAA,CAAAF,OAAC,KAAA,CAAGE,SAAA,CAAA,OAAEC,IAAC,UAAOD,SAAA,SAAa,2BAC1B,KAAA,CAAGA,SAAA,CAAA,OAAEC,IAAC,UAAOD,SAAA,SAAa,wBAC1B,KAAA,CAAGA,SAAA,CAAA,OAAEC,IAAC,UAAOD,SAAA,SAAa,wBAC1B,KAAA,CAAGA,SAAA,CAAA,OAAEC,IAAC,UAAOD,SAAA,SAAa,uBAC1B,KAAA,CAAGA,SAAA,CAAA,OAAEC,IAAC,UAAOD,SAAA,WAAe,6BAGhC,MAAA,CACCA,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,eACpCF,KAAC,MAAA,CAAIC,UAAU,mCACbC,SAAA,CAAAF,OAAC,MAAA,CAAIE,SAAA,GAAAC,IAAC,UAAOD,SAAA,QAAY,6BACxB,MAAA,CAAIA,SAAA,GAAAC,IAAC,UAAOD,SAAA,QAAY,iCACxB,MAAA,CAAIA,SAAA,GAAAC,IAAC,UAAOD,SAAA,SAAa,4BACzB,MAAA,CAAIA,SAAA,GAAAC,IAAC,UAAOD,SAAA,QAAY,+BACxB,MAAA,CAAIA,SAAA,GAAAC,IAAC,UAAOD,SAAA,WAAe,+BAC3B,MAAA,CAAIA,SAAA,GAAAC,IAAC,UAAOD,SAAA,QAAY,8CAOvC"}