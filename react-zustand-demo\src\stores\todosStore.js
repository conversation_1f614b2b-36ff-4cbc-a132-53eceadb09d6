import { create } from 'zustand'
import { devtools, subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

// Todos store - 展示复杂列表状态管理
export const useTodosStore = create(
  devtools(
    subscribeWithSelector(
      immer((set, get) => ({
        // 状态
        todos: [],
        filter: 'all', // all, active, completed
        editingId: null,
        bulkSelectMode: false,
        selectedIds: [],
        nextId: 1,

        // 添加新todo
        addTodo: (todoData) => set((state) => {
          const todo = {
            id: state.nextId++,
            text: typeof todoData === 'string' ? todoData : todoData.text,
            completed: false,
            createdAt: new Date().toISOString(),
            priority: todoData?.priority || 'normal', // low, normal, high
            category: todoData?.category || 'general',
            tags: todoData?.tags || [],
          }
          state.todos.push(todo)
        }),

        // 切换todo完成状态
        toggleTodo: (id) => set((state) => {
          const todo = state.todos.find(t => t.id === id)
          if (todo) {
            todo.completed = !todo.completed
            todo.completedAt = todo.completed ? new Date().toISOString() : null
          }
        }),

        // 删除todo
        removeTodo: (id) => set((state) => {
          state.todos = state.todos.filter(todo => todo.id !== id)
          // 同时从选中列表中移除
          state.selectedIds = state.selectedIds.filter(selectedId => selectedId !== id)
        }),

        // 更新todo文本
        updateTodoText: (id, newText) => set((state) => {
          const todo = state.todos.find(t => t.id === id)
          if (todo && newText.trim()) {
            todo.text = newText.trim()
            todo.updatedAt = new Date().toISOString()
          }
        }),

        // 设置todo优先级
        setTodoPriority: (id, priority) => set((state) => {
          const todo = state.todos.find(t => t.id === id)
          if (todo) {
            todo.priority = priority
            todo.updatedAt = new Date().toISOString()
          }
        }),

        // 设置todo分类
        setTodoCategory: (id, category) => set((state) => {
          const todo = state.todos.find(t => t.id === id)
          if (todo) {
            todo.category = category
            todo.updatedAt = new Date().toISOString()
          }
        }),

        // 添加/移除标签
        toggleTodoTag: (id, tag) => set((state) => {
          const todo = state.todos.find(t => t.id === id)
          if (todo) {
            const tagIndex = todo.tags.indexOf(tag)
            if (tagIndex > -1) {
              todo.tags.splice(tagIndex, 1)
            } else {
              todo.tags.push(tag)
            }
            todo.updatedAt = new Date().toISOString()
          }
        }),

        // 设置过滤器
        setFilter: (filter) => set((state) => {
          state.filter = filter
        }),

        // 全选/取消全选
        toggleAll: () => set((state) => {
          const allCompleted = state.todos.length > 0 && 
            state.todos.every(todo => todo.completed)
          
          state.todos.forEach(todo => {
            todo.completed = !allCompleted
            todo.completedAt = !allCompleted ? new Date().toISOString() : null
          })
        }),

        // 清除已完成的todos
        clearCompleted: () => set((state) => {
          state.todos = state.todos.filter(todo => !todo.completed)
          // 清除选中状态中已删除的项目
          const remainingIds = state.todos.map(todo => todo.id)
          state.selectedIds = state.selectedIds.filter(id => remainingIds.includes(id))
        }),

        // 编辑状态管理
        startEditing: (id) => set((state) => {
          state.editingId = id
        }),

        stopEditing: () => set((state) => {
          state.editingId = null
        }),

        // 批量选择模式
        toggleBulkSelectMode: () => set((state) => {
          state.bulkSelectMode = !state.bulkSelectMode
          if (!state.bulkSelectMode) {
            state.selectedIds = []
          }
        }),

        // 切换todo选中状态
        toggleTodoSelection: (id) => set((state) => {
          const index = state.selectedIds.indexOf(id)
          if (index > -1) {
            state.selectedIds.splice(index, 1)
          } else {
            state.selectedIds.push(id)
          }
        }),

        // 全选/取消全选
        toggleSelectAll: () => set((state) => {
          const filteredTodos = get().getFilteredTodos()
          const allSelected = filteredTodos.length > 0 && 
            filteredTodos.every(todo => state.selectedIds.includes(todo.id))
          
          if (allSelected) {
            // 取消全选 - 从selectedIds中移除当前筛选的todos
            const filteredIds = filteredTodos.map(todo => todo.id)
            state.selectedIds = state.selectedIds.filter(id => !filteredIds.includes(id))
          } else {
            // 全选 - 添加当前筛选的todos到selectedIds
            const filteredIds = filteredTodos.map(todo => todo.id)
            filteredIds.forEach(id => {
              if (!state.selectedIds.includes(id)) {
                state.selectedIds.push(id)
              }
            })
          }
        }),

        // 批量删除选中的todos
        deleteSelected: () => set((state) => {
          state.todos = state.todos.filter(todo => !state.selectedIds.includes(todo.id))
          state.selectedIds = []
        }),

        // 批量标记选中的todos为完成/未完成
        markSelectedAsCompleted: (completed = true) => set((state) => {
          state.todos.forEach(todo => {
            if (state.selectedIds.includes(todo.id)) {
              todo.completed = completed
              todo.completedAt = completed ? new Date().toISOString() : null
            }
          })
        }),

        // 按优先级排序
        sortByPriority: () => set((state) => {
          const priorityOrder = { high: 3, normal: 2, low: 1 }
          state.todos.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority])
        }),

        // 按创建时间排序
        sortByCreatedTime: (ascending = true) => set((state) => {
          state.todos.sort((a, b) => {
            const timeA = new Date(a.createdAt).getTime()
            const timeB = new Date(b.createdAt).getTime()
            return ascending ? timeA - timeB : timeB - timeA
          })
        }),

        // 批量添加todos
        addBatchTodos: (todosText) => set((state) => {
          const lines = todosText.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0)
          
          lines.forEach(text => {
            state.todos.push({
              id: state.nextId++,
              text,
              completed: false,
              createdAt: new Date().toISOString(),
              priority: 'normal',
              category: 'batch',
              tags: []
            })
          })
        }),

        // 重置store
        reset: () => set((state) => {
          state.todos = []
          state.filter = 'all'
          state.editingId = null
          state.bulkSelectMode = false
          state.selectedIds = []
          state.nextId = 1
        }),

        // 计算属性
        getFilteredTodos: () => {
          const { todos, filter } = get()
          switch (filter) {
            case 'active':
              return todos.filter(todo => !todo.completed)
            case 'completed':
              return todos.filter(todo => todo.completed)
            default:
              return todos
          }
        },

        getTodosByCategory: (category) => {
          return get().todos.filter(todo => todo.category === category)
        },

        getTodosByPriority: (priority) => {
          return get().todos.filter(todo => todo.priority === priority)
        },

        getTodosByTag: (tag) => {
          return get().todos.filter(todo => todo.tags.includes(tag))
        },

        getStats: () => {
          const { todos } = get()
          const completed = todos.filter(todo => todo.completed).length
          const total = todos.length
          
          return {
            total,
            completed,
            active: total - completed,
            completionRate: total > 0 ? Math.round((completed / total) * 100) : 0,
            categories: [...new Set(todos.map(todo => todo.category))],
            priorities: {
              high: todos.filter(todo => todo.priority === 'high').length,
              normal: todos.filter(todo => todo.priority === 'normal').length,
              low: todos.filter(todo => todo.priority === 'low').length,
            },
            allTags: [...new Set(todos.flatMap(todo => todo.tags))]
          }
        },

        getSelectedStats: () => {
          const { selectedIds, todos } = get()
          const selectedTodos = todos.filter(todo => selectedIds.includes(todo.id))
          const completed = selectedTodos.filter(todo => todo.completed).length
          
          return {
            total: selectedTodos.length,
            completed,
            active: selectedTodos.length - completed,
          }
        }
      })),
      { name: 'todos-store' }
    )
  )
)

// Selectors
export const selectTodos = (state) => state.todos
export const selectFilter = (state) => state.filter
export const selectFilteredTodos = (state) => {
  const { todos, filter } = state
  return filter === 'all' 
    ? todos 
    : todos.filter(todo => filter === 'active' ? !todo.completed : todo.completed)
}
export const selectEditingId = (state) => state.editingId
export const selectBulkSelectMode = (state) => state.bulkSelectMode
export const selectSelectedIds = (state) => state.selectedIds
export const selectTodoStats = (state) => {
  const { todos } = state
  const completed = todos.filter(todo => todo.completed).length
  const active = todos.length - completed
  const categories = [...new Set(todos.map(todo => todo.category))]
  return {
    total: todos.length,
    completed,
    active,
    categories: categories.length > 0 ? categories : []
  }
}
export const selectSelectedStats = (state) => {
  const selectedTodos = state.todos.filter(todo => state.selectedIds.includes(todo.id))
  return {
    count: selectedTodos.length,
    completed: selectedTodos.filter(todo => todo.completed).length
  }
}

// 按分类的选择器
export const selectTodosByCategory = (category) => (state) => state.getTodosByCategory(category)
export const selectTodosByPriority = (priority) => (state) => state.getTodosByPriority(priority)
export const selectTodosByTag = (tag) => (state) => state.getTodosByTag(tag)

// 复合选择器
export const selectActiveTodos = (state) => state.todos.filter(todo => !todo.completed)
export const selectCompletedTodos = (state) => state.todos.filter(todo => todo.completed)
export const selectHighPriorityTodos = (state) => state.todos.filter(todo => todo.priority === 'high')

// 订阅示例
export const subscribeToTodosChange = (callback) => {
  return useTodosStore.subscribe(
    (state) => state.todos,
    callback
  )
}

export const subscribeToFilterChange = (callback) => {
  return useTodosStore.subscribe(
    (state) => state.filter,
    callback
  )
} 