/* ===== 全局重置 ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* ===== 根样式和CSS变量 ===== */
:root {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;

  /* 浅色主题变量 */
  --color-primary: #0066cc;
  --color-secondary: #6c757d;
  --color-success: #28a745;
  --color-warning: #ffc107;
  --color-danger: #dc3545;
  --color-info: #17a2b8;

  --color-bg: #ffffff;
  --color-surface: #f8f9fa;
  --color-text: #212529;
  --color-text-secondary: #6c757d;
  --color-text-muted: #999999;
  --color-border: #e9ecef;
  --color-shadow: rgba(0, 0, 0, 0.1);

  --color-header-bg: #ffffff;
  --color-nav-hover: #f8f9fa;
  --color-nav-active: #0066cc;
  --color-footer-bg: #f8f9fa;

  /* 间距 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;

  /* 边框圆角 */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;

  /* 阴影 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);

  /* 动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* ===== 深色主题 ===== */
.theme-dark {
  --color-bg: #1a1a1a;
  --color-surface: #2d2d2d;
  --color-text: #ffffff;
  --color-text-secondary: #b3b3b3;
  --color-text-muted: #888888;
  --color-border: #404040;
  --color-shadow: rgba(0, 0, 0, 0.3);

  --color-header-bg: #2d2d2d;
  --color-nav-hover: #404040;
  --color-nav-active: #4da6ff;
  --color-footer-bg: #2d2d2d;
}

/* ===== 自动主题（跟随系统） ===== */
@media (prefers-color-scheme: dark) {
  .theme-auto {
    --color-bg: #1a1a1a;
    --color-surface: #2d2d2d;
    --color-text: #ffffff;
    --color-text-secondary: #b3b3b3;
    --color-text-muted: #888888;
    --color-border: #404040;
    --color-shadow: rgba(0, 0, 0, 0.3);

    --color-header-bg: #2d2d2d;
    --color-nav-hover: #404040;
    --color-nav-active: #4da6ff;
    --color-footer-bg: #2d2d2d;
  }
}

/* ===== 基础样式 ===== */
body {
  background-color: var(--color-bg);
  color: var(--color-text);
  margin: 0;
  min-height: 100vh;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ===== 布局组件 ===== */
.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  width: 100%;
}

.main {
  flex: 1;
  padding: var(--spacing-xl) 0;
}

/* ===== 头部样式 ===== */
.header {
  background-color: var(--color-header-bg);
  border-bottom: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header .container {
  display: flex;
  flex-direction: column;
  padding: var(--spacing-md);
}

.title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-primary);
  margin-bottom: var(--spacing-md);
}

.title .icon {
  font-size: 2rem;
}

/* ===== 导航样式 ===== */
.nav {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  color: var(--color-text);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-size: 0.9rem;
  white-space: nowrap;
}

.nav-link:hover {
  background-color: var(--color-nav-hover);
  color: var(--color-primary);
}

.nav-link.active {
  background-color: var(--color-primary);
  color: white;
}

.nav-icon {
  font-size: 1.1rem;
}

.nav-label {
  font-weight: 500;
}

/* ===== 底部样式 ===== */
.footer {
  background-color: var(--color-footer-bg);
  border-top: 1px solid var(--color-border);
  padding: var(--spacing-lg) 0;
  text-align: center;
  color: var(--color-text-secondary);
  font-size: 0.9rem;
}

.footer p {
  margin-bottom: var(--spacing-sm);
}

.footer p:last-child {
  margin-bottom: 0;
}

/* ===== 通用组件样式 ===== */
.card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  margin-bottom: var(--spacing-lg);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--spacing-sm);
}

.card-description {
  color: var(--color-text-secondary);
  font-size: 0.9rem;
}

/* ===== 按钮样式 ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  background-color: transparent;
  color: var(--color-text);
}

.btn:hover {
  opacity: 0.8;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-secondary {
  background-color: var(--color-secondary);
  color: white;
}

.btn-success {
  background-color: var(--color-success);
  color: white;
}

.btn-warning {
  background-color: var(--color-warning);
  color: white;
}

.btn-danger {
  background-color: var(--color-danger);
  color: white;
}

.btn-outline {
  border-color: currentColor;
}

.btn-outline:hover {
  background-color: currentColor;
  color: white;
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.8rem;
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1.1rem;
}

/* ===== 表单样式 ===== */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--color-text);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  font-size: 1rem;
  background-color: var(--color-bg);
  color: var(--color-text);
  transition: border-color var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

.form-control::placeholder {
  color: var(--color-text-muted);
}

/* ===== 网格布局 ===== */
.grid {
  display: grid;
  gap: var(--spacing-lg);
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* ===== Flex工具类 ===== */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.gap-xs {
  gap: var(--spacing-xs);
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

/* ===== 间距工具类 ===== */
.mb-sm {
  margin-bottom: var(--spacing-sm);
}

.mb-md {
  margin-bottom: var(--spacing-md);
}

.mb-lg {
  margin-bottom: var(--spacing-lg);
}

.mb-xl {
  margin-bottom: var(--spacing-xl);
}

.mt-sm {
  margin-top: var(--spacing-sm);
}

.mt-md {
  margin-top: var(--spacing-md);
}

.mt-lg {
  margin-top: var(--spacing-lg);
}

.mt-xl {
  margin-top: var(--spacing-xl);
}

.p-sm {
  padding: var(--spacing-sm);
}

.p-md {
  padding: var(--spacing-md);
}

.p-lg {
  padding: var(--spacing-lg);
}

/* ===== 文本样式 ===== */
.text-xs {
  font-size: 0.75rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.text-muted {
  color: var(--color-text-muted);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-primary {
  color: var(--color-primary);
}

.text-success {
  color: var(--color-success);
}

.text-warning {
  color: var(--color-warning);
}

.text-danger {
  color: var(--color-danger);
}

/* ===== 状态指示器 ===== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  background-color: var(--color-secondary);
  color: white;
}

.badge-primary {
  background-color: var(--color-primary);
}

.badge-success {
  background-color: var(--color-success);
}

.badge-warning {
  background-color: var(--color-warning);
}

.badge-danger {
  background-color: var(--color-danger);
}

.spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--color-border);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== 代码块样式 ===== */
.code-block {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  overflow-x: auto;
  white-space: pre-wrap;
  color: var(--color-text);
}

/* ===== 警告框样式 ===== */
.alert {
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-md);
  border-left: 4px solid;
}

.alert-info {
  background-color: rgba(23, 162, 184, 0.1);
  border-color: var(--color-info);
  color: var(--color-info);
}

.alert-success {
  background-color: rgba(40, 167, 69, 0.1);
  border-color: var(--color-success);
  color: var(--color-success);
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border-color: var(--color-warning);
  color: var(--color-warning);
}

.alert-danger {
  background-color: rgba(220, 53, 69, 0.1);
  border-color: var(--color-danger);
  color: var(--color-danger);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .header .container {
    padding: var(--spacing-sm);
  }

  .title {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-sm);
  }

  .nav {
    gap: var(--spacing-xs);
  }

  .nav-link {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.8rem;
  }

  .nav-label {
    display: none;
  }

  .container {
    padding: 0 var(--spacing-sm);
  }

  .main {
    padding: var(--spacing-lg) 0;
  }

  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }

  .card {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .nav-link {
    padding: var(--spacing-xs);
  }

  .nav-icon {
    font-size: 1.2rem;
  }

  .title .icon {
    font-size: 1.5rem;
  }

  .title {
    font-size: 1.1rem;
  }
}

/* ===== 可访问性 ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== 打印样式 ===== */
@media print {
  .header,
  .footer,
  .nav {
    display: none;
  }

  .main {
    padding: 0;
  }

  .card {
    box-shadow: none;
    border: 1px solid #000;
  }
} 

/* 加载动画和错误边界样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 2rem;
}

.loading-container .spinner {
  width: 40px;
  height: 40px;
  margin-bottom: 1rem;
}

.loading-container p {
  color: var(--color-text-secondary);
  font-size: 0.9rem;
}

.error-boundary {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 2rem;
  text-align: center;
  background: var(--color-surface);
  border-radius: 8px;
  border: 1px solid var(--color-border);
  margin: 1rem;
}

.error-boundary h2 {
  color: var(--color-danger);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.error-boundary p {
  color: var(--color-text-secondary);
  margin-bottom: 1.5rem;
  max-width: 400px;
}

/* 性能优化相关样式 */
.performance-hint {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--color-primary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.8rem;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 预加载动画 */
.preload-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
  animation: preloadProgress 1.5s infinite;
  z-index: 1001;
}

@keyframes preloadProgress {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 优化滚动性能 */
.scroll-container {
  will-change: scroll-position;
  -webkit-overflow-scrolling: touch;
}

/* 图片懒加载占位符 */
.image-placeholder {
  background: var(--color-surface);
  border: 1px dashed var(--color-border);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-muted);
  min-height: 200px;
  border-radius: 4px;
}

/* 虚拟化列表优化 */
.virtual-list-container {
  height: 400px;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.virtual-list-item {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 1rem;
  border-bottom: 1px solid var(--color-border);
  transition: background-color 0.2s ease;
}

.virtual-list-item:hover {
  background-color: var(--color-surface);
}

/* 代码分割加载状态 */
.chunk-loading {
  opacity: 0.7;
  pointer-events: none;
  transition: opacity 0.2s ease;
}

.chunk-loaded {
  opacity: 1;
  pointer-events: auto;
}

/* 性能监控面板 */
.performance-monitor {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  z-index: 1000;
  max-width: 300px;
  backdrop-filter: blur(10px);
}

.performance-monitor.hidden {
  display: none;
}

.performance-metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.performance-metric:last-child {
  margin-bottom: 0;
}

.metric-label {
  opacity: 0.8;
}

.metric-value {
  font-weight: bold;
  color: var(--color-success);
}

.metric-value.warning {
  color: var(--color-warning);
}

.metric-value.danger {
  color: var(--color-danger);
} 

/* ===== 无障碍访问 ===== */

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --color-primary: #000000;
    --color-secondary: #333333;
    --color-success: #006600;
    --color-warning: #cc6600;
    --color-danger: #cc0000;
    --color-info: #0066cc;
    --color-text: #000000;
    --color-background: #ffffff;
    --color-surface: #f5f5f5;
    --border-color: #000000;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .spinner {
    animation: none;
  }
}

/* 强制颜色模式 */
@media (forced-colors: active) {
  .btn {
    border: 2px solid ButtonText;
  }
  
  .btn:hover,
  .btn:focus {
    border-color: Highlight;
    background-color: Highlight;
    color: HighlightText;
  }
  
  .card {
    border: 1px solid CanvasText;
  }
  
  .form-control {
    border: 2px solid ButtonText;
  }
  
  .form-control:focus {
    border-color: Highlight;
  }
}

/* 焦点指示器增强 */
.focus-visible,
*:focus-visible {
  outline: 3px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--border-radius-sm);
}

/* 跳转到主内容链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: var(--color-text-inverse);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--border-radius-sm);
  z-index: 1000;
  transition: top 0.3s ease;
}

.skip-link:focus {
  top: 6px;
}

/* 屏幕阅读器专用内容 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* 提高触摸目标大小 */
@media (pointer: coarse) {
  .btn,
  .form-control,
  .nav-link,
  a,
  button,
  input,
  select,
  textarea {
    min-height: 44px;
    min-width: 44px;
  }
  
  .btn-sm {
    min-height: 36px;
    min-width: 36px;
  }
}

/* 键盘导航增强 */
.keyboard-nav {
  outline: none;
}

.keyboard-nav *:focus {
  outline: 3px solid var(--color-primary);
  outline-offset: 2px;
}

/* 错误状态增强 */
.form-control[aria-invalid="true"] {
  border-color: var(--color-danger);
  box-shadow: 0 0 0 2px rgba(var(--color-danger-rgb), 0.2);
}

.error-message {
  color: var(--color-danger);
  font-size: 0.875rem;
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.error-message::before {
  content: "⚠️";
  flex-shrink: 0;
}

/* 成功状态 */
.success-message {
  color: var(--color-success);
  font-size: 0.875rem;
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.success-message::before {
  content: "✅";
  flex-shrink: 0;
}

/* 加载状态无障碍改进 */
.loading-spinner[aria-busy="true"] {
  position: relative;
}

.loading-spinner[aria-busy="true"]::after {
  content: "正在加载...";
  position: absolute;
  left: -9999px;
  top: -9999px;
}

/* 模态框无障碍 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: var(--color-background);
  border-radius: var(--border-radius-lg);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* 高对比度文本 */
.high-contrast {
  color: var(--color-text);
  background: var(--color-background);
}

.high-contrast .text-muted {
  color: var(--color-secondary);
}

/* 放大模式支持 */
@media (min-width: 1400px) {
  .large-text {
    font-size: 1.125rem;
    line-height: 1.6;
  }
  
  .large-text .btn {
    font-size: 1rem;
    padding: var(--spacing-md) var(--spacing-lg);
  }
  
  .large-text .form-control {
    font-size: 1rem;
    padding: var(--spacing-md);
  }
}

/* 颜色盲友好模式 */
.colorblind-friendly {
  /* 使用图案和形状而不仅仅是颜色来表示状态 */
}

.colorblind-friendly .btn-success::before {
  content: "✓ ";
}

.colorblind-friendly .btn-danger::before {
  content: "✗ ";
}

.colorblind-friendly .btn-warning::before {
  content: "⚠ ";
}

.colorblind-friendly .btn-info::before {
  content: "ℹ ";
}

/* 语音控制支持 */
[data-voice-command] {
  position: relative;
}

[data-voice-command]::after {
  content: attr(data-voice-command);
  position: absolute;
  left: -9999px;
  top: -9999px;
} 