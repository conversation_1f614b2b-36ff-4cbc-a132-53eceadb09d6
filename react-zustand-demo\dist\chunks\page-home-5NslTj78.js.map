{"version": 3, "file": "page-home-5NslTj78.js", "sources": ["../../src/pages/Home.jsx"], "sourcesContent": ["import React from 'react'\r\nimport { <PERSON> } from 'react-router-dom'\r\nimport { useBasicStore, selectStats } from '@/stores/basicStore'\r\nimport { useTodosStore, selectTodoStats } from '@/stores/todosStore'\r\nimport { useApiStore, selectStats as selectApiStats } from '@/stores/apiStore'\r\nimport { useSettingsStore, selectSettingsSummary } from '@/stores/settingsStore'\r\n\r\nfunction Home() {\r\n  // 从各个store获取统计信息\r\n  const basicStats = useBasicStore(selectStats)\r\n  const todoStats = useTodosStore(selectTodoStats)\r\n  const apiStats = useApiStore(selectApiStats)\r\n  const settingsSummary = useSettingsStore(selectSettingsSummary)\r\n\r\n  const features = [\r\n    {\r\n      icon: '🐻',\r\n      title: 'Zustand',\r\n      description: '轻量级的状态管理库，无样板代码，TypeScript友好',\r\n      highlights: ['简单易用', '性能优秀', '无样板代码']\r\n    },\r\n    {\r\n      icon: '🔄',\r\n      title: '状态订阅',\r\n      description: '细粒度的状态订阅，支持选择器和中间件',\r\n      highlights: ['选择器优化', '中间件支持', '订阅管理']\r\n    },\r\n    {\r\n      icon: '🎯',\r\n      title: '不可变更新',\r\n      description: '使用Immer中间件实现直观的不可变状态更新',\r\n      highlights: ['Immer集成', '直观语法', '性能优化']\r\n    },\r\n    {\r\n      icon: '🔧',\r\n      title: '中间件系统',\r\n      description: '丰富的中间件生态，支持devtools、持久化等功能',\r\n      highlights: ['DevTools', '状态持久化', '自定义中间件']\r\n    },\r\n    {\r\n      icon: '💾',\r\n      title: '状态持久化',\r\n      description: '自动状态持久化，支持localStorage、sessionStorage等',\r\n      highlights: ['自动保存', '选择性持久化', '版本迁移']\r\n    },\r\n    {\r\n      icon: '🌐',\r\n      title: '异步处理',\r\n      description: '原生支持异步操作，无需额外的异步处理库',\r\n      highlights: ['原生异步', '错误处理', '加载状态']\r\n    }\r\n  ]\r\n\r\n  const examples = [\r\n    {\r\n      title: '基础Store演示',\r\n      path: '/basic-store',\r\n      icon: '📦',\r\n      description: '计数器、状态管理、异步操作等基础功能',\r\n      color: 'var(--color-success)'\r\n    },\r\n    {\r\n      title: 'Todos管理',\r\n      path: '/todos-management',\r\n      icon: '📝',\r\n      description: '复杂的列表管理、过滤、批量操作',\r\n      color: 'var(--color-info)'\r\n    },\r\n    {\r\n      title: 'API集成',\r\n      path: '/api-integration',\r\n      icon: '🌐',\r\n      description: '与JSONPlaceholder API的完整集成示例',\r\n      color: 'var(--color-warning)'\r\n    },\r\n    {\r\n      title: '设置演示',\r\n      path: '/settings-demo',\r\n      icon: '⚙️',\r\n      description: '复杂设置管理、持久化、导入导出',\r\n      color: 'var(--color-primary)'\r\n    },\r\n    {\r\n      title: '中间件演示',\r\n      path: '/middleware-demo',\r\n      icon: '🔧',\r\n      description: '自定义中间件、状态监控、日志记录',\r\n      color: 'var(--color-danger)'\r\n    },\r\n    {\r\n      title: '高级特性',\r\n      path: '/advanced-features',\r\n      icon: '🚀',\r\n      description: '性能优化、状态派生、订阅模式',\r\n      color: '#9c27b0'\r\n    }\r\n  ]\r\n\r\n  const techStack = [\r\n    { name: 'React', version: '19.1.0', icon: '⚛️', description: '前端框架' },\r\n    { name: 'Zustand', version: '5.0.6', icon: '🐻', description: '状态管理' },\r\n    { name: 'React Router', version: '7.7.0', icon: '🛣️', description: '路由管理' },\r\n    { name: 'Axios', version: '1.10.0', icon: '🌐', description: 'HTTP客户端' },\r\n    { name: 'Immer', version: '10.1.1', icon: '🔄', description: '不可变更新' },\r\n    { name: 'Vite', version: '7.0.5', icon: '⚡', description: '构建工具' },\r\n  ]\r\n\r\n  return (\r\n    <div className=\"home-page\">\r\n      <section className=\"hero-section mb-xl\">\r\n        <div className=\"card text-center\">\r\n          <h1 className=\"text-2xl font-bold mb-md\">\r\n            🐻 React + Zustand 全方向API演示\r\n          </h1>\r\n          <p className=\"text-lg text-secondary mb-lg\">\r\n            一个全面展示 Zustand 状态管理功能的完整演示应用，从基础用法到高级特性的全方位学习资源\r\n          </p>\r\n          <div className=\"flex justify-center gap-md\">\r\n            <Link to=\"/basic-store\" className=\"btn btn-primary btn-lg\">\r\n              🚀 开始探索\r\n            </Link>\r\n            <a \r\n              href=\"https://github.com/pmndrs/zustand\" \r\n              target=\"_blank\" \r\n              rel=\"noopener noreferrer\" \r\n              className=\"btn btn-outline btn-lg\"\r\n            >\r\n              📚 Zustand文档\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* 统计信息 */}\r\n      <section className=\"stats-section mb-xl\">\r\n        <h2 className=\"text-xl font-semibold mb-lg\">📊 实时状态统计</h2>\r\n        <div className=\"grid grid-4\">\r\n          <div className=\"card text-center\">\r\n            <div className=\"text-2xl font-bold text-primary mb-sm\">{basicStats.totalActions}</div>\r\n            <div className=\"text-sm text-secondary\">基础操作次数</div>\r\n            <div className=\"text-xs text-muted mt-sm\">\r\n              最后操作: {basicStats.lastAction}\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"card text-center\">\r\n            <div className=\"text-2xl font-bold text-success mb-sm\">{todoStats.total}</div>\r\n            <div className=\"text-sm text-secondary\">待办事项总数</div>\r\n            <div className=\"text-xs text-muted mt-sm\">\r\n              完成率: {todoStats.completionRate}%\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"card text-center\">\r\n            <div className=\"text-2xl font-bold text-warning mb-sm\">{apiStats.totalRequests}</div>\r\n            <div className=\"text-sm text-secondary\">API请求次数</div>\r\n            <div className=\"text-xs text-muted mt-sm\">\r\n              缓存大小: {apiStats.cacheSize}\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"card text-center\">\r\n            <div className=\"text-2xl font-bold text-info mb-sm\">{settingsSummary.theme}</div>\r\n            <div className=\"text-sm text-secondary\">当前主题</div>\r\n            <div className=\"text-xs text-muted mt-sm\">\r\n              语言: {settingsSummary.language}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* 核心特性 */}\r\n      <section className=\"features-section mb-xl\">\r\n        <h2 className=\"text-xl font-semibold mb-lg\">✨ Zustand 核心特性</h2>\r\n        <div className=\"grid grid-3\">\r\n          {features.map((feature, index) => (\r\n            <div key={index} className=\"card\">\r\n              <div className=\"flex items-start gap-md mb-md\">\r\n                <span className=\"text-2xl\">{feature.icon}</span>\r\n                <div>\r\n                  <h3 className=\"font-semibold mb-sm\" style={{ color: 'var(--color-primary)' }}>\r\n                    {feature.title}\r\n                  </h3>\r\n                  <p className=\"text-sm text-secondary mb-sm\">\r\n                    {feature.description}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex gap-xs flex-wrap\">\r\n                {feature.highlights.map((highlight, idx) => (\r\n                  <span key={idx} className=\"badge badge-primary\">\r\n                    {highlight}\r\n                  </span>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </section>\r\n\r\n      {/* 演示页面 */}\r\n      <section className=\"examples-section mb-xl\">\r\n        <h2 className=\"text-xl font-semibold mb-lg\">🎯 功能演示页面</h2>\r\n        <div className=\"grid grid-2\">\r\n          {examples.map((example, index) => (\r\n            <Link \r\n              key={index} \r\n              to={example.path} \r\n              className=\"card\" \r\n              style={{ textDecoration: 'none', color: 'inherit' }}\r\n            >\r\n              <div className=\"flex items-start gap-md mb-md\">\r\n                <span \r\n                  className=\"text-2xl p-sm\" \r\n                  style={{ \r\n                    backgroundColor: example.color, \r\n                    color: 'white', \r\n                    borderRadius: 'var(--border-radius-md)' \r\n                  }}\r\n                >\r\n                  {example.icon}\r\n                </span>\r\n                <div className=\"flex-1\">\r\n                  <h3 className=\"font-semibold mb-sm\">\r\n                    {example.title}\r\n                  </h3>\r\n                  <p className=\"text-sm text-secondary\">\r\n                    {example.description}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex justify-end\">\r\n                <span className=\"text-sm text-primary\">\r\n                  探索 →\r\n                </span>\r\n              </div>\r\n            </Link>\r\n          ))}\r\n        </div>\r\n      </section>\r\n\r\n      {/* 技术栈 */}\r\n      <section className=\"tech-stack-section mb-xl\">\r\n        <h2 className=\"text-xl font-semibold mb-lg\">🛠️ 技术栈</h2>\r\n        <div className=\"grid grid-3\">\r\n          {techStack.map((tech, index) => (\r\n            <div key={index} className=\"card text-center\">\r\n              <div className=\"text-2xl mb-sm\">{tech.icon}</div>\r\n              <h3 className=\"font-semibold mb-xs\">{tech.name}</h3>\r\n              <div className=\"text-sm text-secondary mb-xs\">v{tech.version}</div>\r\n              <div className=\"text-xs text-muted\">{tech.description}</div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </section>\r\n\r\n      {/* Zustand核心概念 */}\r\n      <section className=\"concepts-section mb-xl\">\r\n        <h2 className=\"text-xl font-semibold mb-lg\">📖 Zustand 核心概念</h2>\r\n        <div className=\"grid grid-2\">\r\n          <div className=\"card\">\r\n            <h3 className=\"font-semibold mb-md\" style={{ color: 'var(--color-success)' }}>\r\n              🏪 Store（状态存储）\r\n            </h3>\r\n            <p className=\"text-sm text-secondary mb-sm\">\r\n              使用create函数创建状态存储，支持状态和行为的统一管理\r\n            </p>\r\n            <div className=\"code-block text-sm\">\r\n{`const useStore = create((set) => ({\r\n  count: 0,\r\n  increment: () => set((state) => ({ \r\n    count: state.count + 1 \r\n  })),\r\n}))`}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"card\">\r\n            <h3 className=\"font-semibold mb-md\" style={{ color: 'var(--color-warning)' }}>\r\n              🎯 Selectors（选择器）\r\n            </h3>\r\n            <p className=\"text-sm text-secondary mb-sm\">\r\n              高效的状态选择，自动订阅变化，支持计算属性\r\n            </p>\r\n            <div className=\"code-block text-sm\">\r\n{`// 基础选择\r\nconst count = useStore(state => state.count)\r\n\r\n// 计算属性\r\nconst doubleCount = useStore(state => state.count * 2)`}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"card\">\r\n            <h3 className=\"font-semibold mb-md\" style={{ color: 'var(--color-info)' }}>\r\n              🔄 Immer Integration\r\n            </h3>\r\n            <p className=\"text-sm text-secondary mb-sm\">\r\n              使用Immer中间件实现直观的不可变更新语法\r\n            </p>\r\n            <div className=\"code-block text-sm\">\r\n{`const useStore = create(immer((set) => ({\r\n  todos: [],\r\n  addTodo: (text) => set((state) => {\r\n    state.todos.push({ id: Date.now(), text })\r\n  })\r\n})))`}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"card\">\r\n            <h3 className=\"font-semibold mb-md\" style={{ color: 'var(--color-danger)' }}>\r\n              🔧 Middleware（中间件）\r\n            </h3>\r\n            <p className=\"text-sm text-secondary mb-sm\">\r\n              丰富的中间件系统，支持devtools、持久化、订阅等功能\r\n            </p>\r\n            <div className=\"code-block text-sm\">\r\n{`const useStore = create(\r\n  devtools(\r\n    persist(\r\n      subscribeWithSelector(/* store */),\r\n      { name: 'my-store' }\r\n    )\r\n  )\r\n)`}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* 快速开始 */}\r\n      <section className=\"quick-start-section\">\r\n        <div className=\"card\">\r\n          <h2 className=\"text-xl font-semibold mb-lg\">🚀 快速开始</h2>\r\n          <div className=\"grid grid-2\">\r\n            <div>\r\n              <h3 className=\"font-semibold mb-md\">📖 学习路径</h3>\r\n              <ol className=\"text-sm text-secondary space-y-1\">\r\n                <li>1. 访问 <Link to=\"/basic-store\" className=\"text-primary\">基础Store</Link> 了解基本概念</li>\r\n                <li>2. 探索 <Link to=\"/todos-management\" className=\"text-primary\">Todos管理</Link> 学习复杂状态</li>\r\n                <li>3. 查看 <Link to=\"/api-integration\" className=\"text-primary\">API集成</Link> 掌握异步操作</li>\r\n                <li>4. 体验 <Link to=\"/settings-demo\" className=\"text-primary\">设置演示</Link> 了解持久化</li>\r\n                <li>5. 学习 <Link to=\"/advanced-features\" className=\"text-primary\">高级特性</Link> 提升技能</li>\r\n              </ol>\r\n            </div>\r\n            <div>\r\n              <h3 className=\"font-semibold mb-md\">🎯 核心优势</h3>\r\n              <ul className=\"text-sm text-secondary space-y-1\">\r\n                <li>• <strong>轻量级</strong>：只有2.6kb（gzipped）</li>\r\n                <li>• <strong>无样板代码</strong>：简洁的API设计</li>\r\n                <li>• <strong>TypeScript支持</strong>：完整的类型推断</li>\r\n                <li>• <strong>性能优秀</strong>：精确的重新渲染</li>\r\n                <li>• <strong>中间件丰富</strong>：强大的扩展能力</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Home "], "names": ["Home", "basicStats", "useBasicStore", "selectStats", "todoStats", "useTodosStore", "selectTodoStats", "apiStats", "useApiStore", "selectApiStats", "settingsSummary", "useSettingsStore", "selectSettingsSummary", "jsxs", "className", "children", "jsx", "Link", "to", "href", "target", "rel", "totalActions", "lastAction", "total", "completionRate", "totalRequests", "cacheSize", "theme", "language", "icon", "title", "description", "highlights", "map", "feature", "index", "style", "color", "highlight", "idx", "path", "example", "textDecoration", "backgroundColor", "borderRadius", "name", "version", "tech"], "mappings": "+RAOA,SAASA,IAEP,MAAMC,EAAaC,EAAcC,GAC3BC,EAAYC,EAAcC,GAC1BC,EAAWC,EAAYC,GACvBC,EAAkBC,EAAiBC,GA+FzC,SACEC,KAAC,MAAA,CAAIC,UAAU,YACbC,SAAA,CAAAC,EAAAA,IAAC,WAAQF,UAAU,qBACjBC,SAAAF,EAAAA,KAAC,MAAA,CAAIC,UAAU,mBACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,2BAA2BC,SAAA,gCAGzCC,EAAAA,IAAC,IAAA,CAAEF,UAAU,+BAA+BC,SAAA,sDAG5CF,KAAC,MAAA,CAAIC,UAAU,6BACbC,SAAA,CAAAC,MAACC,EAAA,CAAKC,GAAG,eAAeJ,UAAU,yBAAyBC,SAAA,YAG3DC,EAAAA,IAAC,IAAA,CACCG,KAAK,oCACLC,OAAO,SACPC,IAAI,sBACJP,UAAU,yBACXC,SAAA,2BAQPF,KAAC,UAAA,CAAQC,UAAU,sBACjBC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,8BAA8BC,SAAA,gBAC5CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,mBACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,wCAAyCC,SAAAd,EAAWqB,eACnEN,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,aACxCF,KAAC,MAAA,CAAIC,UAAU,2BAA2BC,SAAA,CAAA,SACjCd,EAAWsB,mBAItBV,KAAC,MAAA,CAAIC,UAAU,mBACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,wCAAyCC,SAAAX,EAAUoB,QAClER,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,aACxCF,KAAC,MAAA,CAAIC,UAAU,2BAA2BC,SAAA,CAAA,QAClCX,EAAUqB,eAAe,YAInCZ,KAAC,MAAA,CAAIC,UAAU,mBACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,wCAAyCC,SAAAR,EAASmB,gBACjEV,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,cACxCF,KAAC,MAAA,CAAIC,UAAU,2BAA2BC,SAAA,CAAA,SACjCR,EAASoB,kBAIpBd,KAAC,MAAA,CAAIC,UAAU,mBACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,qCAAsCC,SAAAL,EAAgBkB,QACrEZ,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,WACxCF,KAAC,MAAA,CAAIC,UAAU,2BAA2BC,SAAA,CAAA,OACnCL,EAAgBmB,uBAO7BhB,KAAC,UAAA,CAAQC,UAAU,yBACjBC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,8BAA8BC,SAAA,qBAC5CC,IAAC,MAAA,CAAIF,UAAU,cACZC,SAjKQ,CACf,CACEe,KAAM,KACNC,MAAO,UACPC,YAAa,+BACbC,WAAY,CAAC,OAAQ,OAAQ,UAE/B,CACEH,KAAM,KACNC,MAAO,OACPC,YAAa,qBACbC,WAAY,CAAC,QAAS,QAAS,SAEjC,CACEH,KAAM,KACNC,MAAO,QACPC,YAAa,yBACbC,WAAY,CAAC,UAAW,OAAQ,SAElC,CACEH,KAAM,KACNC,MAAO,QACPC,YAAa,6BACbC,WAAY,CAAC,WAAY,QAAS,WAEpC,CACEH,KAAM,KACNC,MAAO,QACPC,YAAa,yCACbC,WAAY,CAAC,OAAQ,SAAU,SAEjC,CACEH,KAAM,KACNC,MAAO,OACPC,YAAa,sBACbC,WAAY,CAAC,OAAQ,OAAQ,UA8HfC,IAAI,CAACC,EAASC,IACtBvB,EAAAA,KAAC,MAAA,CAAgBC,UAAU,OACzBC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,gCACbC,SAAA,CAAAC,EAAAA,IAAC,OAAA,CAAKF,UAAU,WAAYC,SAAAoB,EAAQL,cACnC,MAAA,CACCf,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBuB,MAAO,CAAEC,MAAO,wBACjDvB,SAAAoB,EAAQJ,QAEXf,EAAAA,IAAC,IAAA,CAAEF,UAAU,+BACVC,WAAQiB,0BAId,MAAA,CAAIlB,UAAU,wBACZC,SAAAoB,EAAQF,WAAWC,IAAI,CAACK,EAAWC,UACjC,OAAA,CAAe1B,UAAU,sBACvBC,SAAAwB,GADQC,QAdPJ,WAyBhBvB,KAAC,UAAA,CAAQC,UAAU,yBACjBC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,8BAA8BC,SAAA,cAC5CC,EAAAA,IAAC,OAAIF,UAAU,cACZC,SAvJQ,CACf,CACEgB,MAAO,YACPU,KAAM,eACNX,KAAM,KACNE,YAAa,qBACbM,MAAO,wBAET,CACEP,MAAO,UACPU,KAAM,oBACNX,KAAM,KACNE,YAAa,kBACbM,MAAO,qBAET,CACEP,MAAO,QACPU,KAAM,mBACNX,KAAM,KACNE,YAAa,8BACbM,MAAO,wBAET,CACEP,MAAO,OACPU,KAAM,iBACNX,KAAM,KACNE,YAAa,kBACbM,MAAO,wBAET,CACEP,MAAO,QACPU,KAAM,mBACNX,KAAM,KACNE,YAAa,mBACbM,MAAO,uBAET,CACEP,MAAO,OACPU,KAAM,qBACNX,KAAM,KACNE,YAAa,iBACbM,MAAO,YA8GOJ,IAAI,CAACQ,EAASN,IACtBvB,EAAAA,KAACI,EAAA,CAECC,GAAIwB,EAAQD,KACZ3B,UAAU,OACVuB,MAAO,CAAEM,eAAgB,OAAQL,MAAO,WAExCvB,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,gCACbC,SAAA,CAAAC,EAAAA,IAAC,OAAA,CACCF,UAAU,gBACVuB,MAAO,CACLO,gBAAiBF,EAAQJ,MACzBA,MAAO,QACPO,aAAc,2BAGf9B,SAAA2B,EAAQZ,SAEXjB,KAAC,MAAA,CAAIC,UAAU,SACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBACXC,SAAA2B,EAAQX,QAEXf,EAAAA,IAAC,IAAA,CAAEF,UAAU,yBACVC,WAAQiB,sBAIfhB,IAAC,OAAIF,UAAU,mBACbC,eAAC,OAAA,CAAKD,UAAU,uBAAuBC,SAAA,aA1BpCqB,WAoCbvB,KAAC,UAAA,CAAQC,UAAU,2BACjBC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,8BAA8BC,SAAA,cAC5CC,IAAC,MAAA,CAAIF,UAAU,cACZC,SAnJS,CAChB,CAAE+B,KAAM,QAASC,QAAS,SAAUjB,KAAM,KAAME,YAAa,QAC7D,CAAEc,KAAM,UAAWC,QAAS,QAASjB,KAAM,KAAME,YAAa,QAC9D,CAAEc,KAAM,eAAgBC,QAAS,QAASjB,KAAM,MAAOE,YAAa,QACpE,CAAEc,KAAM,QAASC,QAAS,SAAUjB,KAAM,KAAME,YAAa,WAC7D,CAAEc,KAAM,QAASC,QAAS,SAAUjB,KAAM,KAAME,YAAa,SAC7D,CAAEc,KAAM,OAAQC,QAAS,QAASjB,KAAM,IAAKE,YAAa,SA6IzCE,IAAI,CAACc,EAAMZ,IACpBvB,EAAAA,KAAC,MAAA,CAAgBC,UAAU,mBACzBC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,iBAAkBC,SAAAiC,EAAKlB,OACtCd,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAuBC,WAAK+B,SAC1CjC,KAAC,MAAA,CAAIC,UAAU,+BAA+BC,SAAA,CAAA,IAAEiC,EAAKD,WACrD/B,EAAAA,IAAC,MAAA,CAAIF,UAAU,qBAAsBC,WAAKiB,gBAJlCI,WAWhBvB,KAAC,UAAA,CAAQC,UAAU,yBACjBC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,8BAA8BC,SAAA,sBAC5CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBuB,MAAO,CAAEC,MAAO,wBAA0BvB,SAAA,mBAG9EC,EAAAA,IAAC,IAAA,CAAEF,UAAU,+BAA+BC,SAAA,oCAG5CC,IAAC,MAAA,CAAIF,UAAU,qBAC1BC,SAAA,2IASSF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBuB,MAAO,CAAEC,MAAO,wBAA0BvB,SAAA,sBAG9EC,EAAAA,IAAC,IAAA,CAAEF,UAAU,+BAA+BC,SAAA,4BAG5CC,IAAC,MAAA,CAAIF,UAAU,qBAC1BC,SAAA,kIAQSF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBuB,MAAO,CAAEC,MAAO,qBAAuBvB,SAAA,yBAG3EC,EAAAA,IAAC,IAAA,CAAEF,UAAU,+BAA+BC,SAAA,6BAG5CC,IAAC,MAAA,CAAIF,UAAU,qBAC1BC,SAAA,oKASSF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBuB,MAAO,CAAEC,MAAO,uBAAyBvB,SAAA,uBAG7EC,EAAAA,IAAC,IAAA,CAAEF,UAAU,+BAA+BC,SAAA,oCAG5CC,IAAC,MAAA,CAAIF,UAAU,qBAC1BC,SAAA,6JAcM,UAAA,CAAQD,UAAU,sBACjBC,SAAAF,EAAAA,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,8BAA8BC,SAAA,cAC5CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,OAAC,MAAA,CACCE,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,cACpCF,KAAC,KAAA,CAAGC,UAAU,mCACZC,SAAA,CAAAF,OAAC,KAAA,CAAGE,SAAA,CAAA,eAAOE,EAAA,CAAKC,GAAG,eAAeJ,UAAU,eAAeC,SAAA,YAAc,oBACxE,KAAA,CAAGA,SAAA,CAAA,eAAOE,EAAA,CAAKC,GAAG,oBAAoBJ,UAAU,eAAeC,SAAA,YAAc,oBAC7E,KAAA,CAAGA,SAAA,CAAA,eAAOE,EAAA,CAAKC,GAAG,mBAAmBJ,UAAU,eAAeC,SAAA,UAAY,oBAC1E,KAAA,CAAGA,SAAA,CAAA,eAAOE,EAAA,CAAKC,GAAG,iBAAiBJ,UAAU,eAAeC,SAAA,SAAW,mBACvE,KAAA,CAAGA,SAAA,CAAA,eAAOE,EAAA,CAAKC,GAAG,qBAAqBJ,UAAU,eAAeC,SAAA,SAAW,wBAG/E,MAAA,CACCA,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,sBAAsBC,SAAA,cACpCF,KAAC,KAAA,CAAGC,UAAU,mCACZC,SAAA,CAAAF,OAAC,KAAA,CAAGE,SAAA,CAAA,OAAEC,IAAC,UAAOD,SAAA,QAAY,8BACzB,KAAA,CAAGA,SAAA,CAAA,OAAEC,IAAC,UAAOD,SAAA,UAAc,sBAC3B,KAAA,CAAGA,SAAA,CAAA,OAAEC,IAAC,UAAOD,SAAA,iBAAqB,qBAClC,KAAA,CAAGA,SAAA,CAAA,OAAEC,IAAC,UAAOD,SAAA,SAAa,qBAC1B,KAAA,CAAGA,SAAA,CAAA,OAAEC,IAAC,UAAOD,SAAA,UAAc,8BAQ5C"}