{"version": 3, "file": "store-basicstore-DgVIqOAD.js", "sources": ["../../src/stores/basicStore.js"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools, subscribeWithSelector } from 'zustand/middleware'\r\nimport { immer } from 'zustand/middleware/immer'\r\n\r\n// 基础store - 展示Zustand核心功能\r\nexport const useBasicStore = create(\r\n  devtools(\r\n    subscribeWithSelector(\r\n      immer((set, get) => ({\r\n        // 状态\r\n        count: 0,\r\n        name: '',\r\n        items: [],\r\n        user: { name: '', email: '' },\r\n        isLoading: false,\r\n        error: null,\r\n        history: [],\r\n\r\n        // 基础计数器操作\r\n        increment: () => set((state) => {\r\n          state.count += 1\r\n          state.history.push({\r\n            action: 'increment',\r\n            timestamp: new Date().toISOString(),\r\n            value: state.count\r\n          })\r\n        }),\r\n\r\n        decrement: () => set((state) => {\r\n          state.count -= 1\r\n          state.history.push({\r\n            action: 'decrement',\r\n            timestamp: new Date().toISOString(),\r\n            value: state.count\r\n          })\r\n        }),\r\n\r\n        incrementByAmount: (amount) => set((state) => {\r\n          state.count += amount\r\n          state.history.push({\r\n            action: 'incrementByAmount',\r\n            timestamp: new Date().toISOString(),\r\n            value: state.count,\r\n            amount\r\n          })\r\n        }),\r\n\r\n        // 重置计数器\r\n        resetCount: () => set((state) => {\r\n          state.count = 0\r\n          state.history.push({\r\n            action: 'resetCount',\r\n            timestamp: new Date().toISOString(),\r\n            value: 0\r\n          })\r\n        }),\r\n\r\n        // 姓名管理\r\n        setName: (name) => set((state) => {\r\n          state.name = name\r\n          state.history.push({\r\n            action: 'setName',\r\n            timestamp: new Date().toISOString(),\r\n            value: name\r\n          })\r\n        }),\r\n\r\n        // 项目列表管理\r\n        addItem: (item) => set((state) => {\r\n          const newItem = {\r\n            id: Date.now(),\r\n            text: item,\r\n            createdAt: new Date().toISOString()\r\n          }\r\n          state.items.push(newItem)\r\n          state.history.push({\r\n            action: 'addItem',\r\n            timestamp: new Date().toISOString(),\r\n            value: item\r\n          })\r\n        }),\r\n\r\n        removeItem: (id) => set((state) => {\r\n          const index = state.items.findIndex(item => item.id === id)\r\n          if (index > -1) {\r\n            const removedItem = state.items[index]\r\n            state.items.splice(index, 1)\r\n            state.history.push({\r\n              action: 'removeItem',\r\n              timestamp: new Date().toISOString(),\r\n              value: removedItem.text\r\n            })\r\n          }\r\n        }),\r\n\r\n        updateItem: (id, newText) => set((state) => {\r\n          const item = state.items.find(item => item.id === id)\r\n          if (item) {\r\n            const oldText = item.text\r\n            item.text = newText\r\n            item.updatedAt = new Date().toISOString()\r\n            state.history.push({\r\n              action: 'updateItem',\r\n              timestamp: new Date().toISOString(),\r\n              value: `${oldText} → ${newText}`\r\n            })\r\n          }\r\n        }),\r\n\r\n        // 用户信息管理\r\n        updateUser: (userData) => set((state) => {\r\n          Object.assign(state.user, userData)\r\n          state.history.push({\r\n            action: 'updateUser',\r\n            timestamp: new Date().toISOString(),\r\n            value: userData\r\n          })\r\n        }),\r\n\r\n        // 批量更新\r\n        batchUpdate: (updates) => set((state) => {\r\n          if (updates.count !== undefined) state.count = updates.count\r\n          if (updates.name !== undefined) state.name = updates.name\r\n          if (updates.user !== undefined) Object.assign(state.user, updates.user)\r\n          \r\n          state.history.push({\r\n            action: 'batchUpdate',\r\n            timestamp: new Date().toISOString(),\r\n            value: updates\r\n          })\r\n        }),\r\n\r\n        // 异步操作模拟\r\n        simulateAsyncOperation: async (delay = 2000) => {\r\n          set((state) => {\r\n            state.isLoading = true\r\n            state.error = null\r\n          })\r\n\r\n          try {\r\n            await new Promise((resolve, reject) => {\r\n              setTimeout(() => {\r\n                // 随机决定成功或失败\r\n                if (Math.random() > 0.8) {\r\n                  reject(new Error('模拟的异步操作失败'))\r\n                } else {\r\n                  resolve()\r\n                }\r\n              }, delay)\r\n            })\r\n\r\n            set((state) => {\r\n              state.isLoading = false\r\n              state.count += 10\r\n              state.history.push({\r\n                action: 'simulateAsyncOperation',\r\n                timestamp: new Date().toISOString(),\r\n                value: '异步操作成功'\r\n              })\r\n            })\r\n          } catch (error) {\r\n            set((state) => {\r\n              state.isLoading = false\r\n              state.error = error.message\r\n              state.history.push({\r\n                action: 'simulateAsyncOperation',\r\n                timestamp: new Date().toISOString(),\r\n                value: `异步操作失败: ${error.message}`\r\n              })\r\n            })\r\n          }\r\n        },\r\n\r\n        // 清除错误\r\n        clearError: () => set((state) => {\r\n          state.error = null\r\n        }),\r\n\r\n        // 清除历史记录\r\n        clearHistory: () => set((state) => {\r\n          state.history = []\r\n        }),\r\n\r\n        // 重置整个store\r\n        reset: () => set((state) => {\r\n          state.count = 0\r\n          state.name = ''\r\n          state.items = []\r\n          state.user = { name: '', email: '' }\r\n          state.isLoading = false\r\n          state.error = null\r\n          state.history = [{\r\n            action: 'reset',\r\n            timestamp: new Date().toISOString(),\r\n            value: '状态已重置'\r\n          }]\r\n        }),\r\n\r\n        // 计算属性 (使用selectors)\r\n        getDoubleCount: () => get().count * 2,\r\n        getTripleCount: () => get().count * 3,\r\n        getFormattedUser: () => {\r\n          const { user } = get()\r\n          return user.name && user.email \r\n            ? `${user.name} (${user.email})`\r\n            : '未设置用户信息'\r\n        },\r\n        getRecentHistory: () => get().history.slice(-10),\r\n        getStats: () => {\r\n          const state = get()\r\n          return {\r\n            totalItems: state.items.length,\r\n            totalActions: state.history.length,\r\n            lastAction: state.history[state.history.length - 1]?.action || 'none',\r\n            hasError: !!state.error,\r\n            isActive: state.count > 0 || state.items.length > 0\r\n          }\r\n        }\r\n      })),\r\n      { name: 'basic-store' }\r\n    )\r\n  )\r\n)\r\n\r\n// Selectors - 用于计算派生状态\r\nexport const selectCount = (state) => state.count\r\nexport const selectDoubleCount = (state) => state.getDoubleCount()\r\nexport const selectTripleCount = (state) => state.getTripleCount()\r\nexport const selectName = (state) => state.name\r\nexport const selectItems = (state) => state.items\r\nexport const selectUser = (state) => state.user\r\nexport const selectFormattedUser = (state) => state.getFormattedUser()\r\nexport const selectIsLoading = (state) => state.isLoading\r\nexport const selectError = (state) => state.error\r\nexport const selectHistory = (state) => state.history\r\nexport const selectRecentHistory = (state) => state.getRecentHistory()\r\nexport const selectStats = (state) => state.getStats()\r\n\r\n// 订阅示例 - 监听特定状态变化\r\nexport const subscribeToCount = (callback) => {\r\n  return useBasicStore.subscribe(\r\n    (state) => state.count,\r\n    callback\r\n  )\r\n}\r\n\r\nexport const subscribeToError = (callback) => {\r\n  return useBasicStore.subscribe(\r\n    (state) => state.error,\r\n    callback\r\n  )\r\n} "], "names": ["useBasicStore", "create", "devtools", "subscribeWithSelector", "immer", "set", "get", "count", "name", "items", "user", "email", "isLoading", "error", "history", "increment", "state", "push", "action", "timestamp", "Date", "toISOString", "value", "decrement", "incrementByAmount", "amount", "resetCount", "setName", "addItem", "item", "newItem", "id", "now", "text", "createdAt", "removeItem", "index", "findIndex", "removedItem", "splice", "updateItem", "newText", "find", "oldText", "updatedAt", "updateUser", "userData", "Object", "assign", "batchUpdate", "updates", "simulateAsyncOperation", "async", "delay", "Promise", "resolve", "reject", "setTimeout", "Math", "random", "Error", "message", "clearError", "clearHistory", "reset", "getDoubleCount", "getTripleCount", "getFormattedUser", "getRecentHistory", "slice", "getStats", "totalItems", "length", "totalActions", "lastAction", "<PERSON><PERSON><PERSON><PERSON>", "isActive", "selectCount", "selectDoubleCount", "selectTripleCount", "selectName", "selectItems", "selectUser", "selectFormattedUser", "selectIsLoading", "selectError", "selectHistory", "selectRecentHistory", "selectStats"], "mappings": "oEAKY,MAACA,EAAgBC,EAC3BC,EACEC,EACEC,EAAM,CAACC,EAAKC,KAAA,CAEVC,MAAO,EACPC,KAAM,GACNC,MAAO,GACPC,KAAM,CAAEF,KAAM,GAAIG,MAAO,IACzBC,WAAW,EACXC,MAAO,KACPC,QAAS,GAGTC,UAAW,IAAMV,EAAKW,IACpBA,EAAMT,OAAS,EACfS,EAAMF,QAAQG,KAAK,CACjBC,OAAQ,YACRC,WAAA,IAAeC,MAAOC,cACtBC,MAAON,EAAMT,UAIjBgB,UAAW,IAAMlB,EAAKW,IACpBA,EAAMT,OAAS,EACfS,EAAMF,QAAQG,KAAK,CACjBC,OAAQ,YACRC,WAAA,IAAeC,MAAOC,cACtBC,MAAON,EAAMT,UAIjBiB,kBAAoBC,GAAWpB,EAAKW,IAClCA,EAAMT,OAASkB,EACfT,EAAMF,QAAQG,KAAK,CACjBC,OAAQ,oBACRC,WAAA,IAAeC,MAAOC,cACtBC,MAAON,EAAMT,MACbkB,aAKJC,WAAY,IAAMrB,EAAKW,IACrBA,EAAMT,MAAQ,EACdS,EAAMF,QAAQG,KAAK,CACjBC,OAAQ,aACRC,WAAA,IAAeC,MAAOC,cACtBC,MAAO,MAKXK,QAAUnB,GAASH,EAAKW,IACtBA,EAAMR,KAAOA,EACbQ,EAAMF,QAAQG,KAAK,CACjBC,OAAQ,UACRC,WAAA,IAAeC,MAAOC,cACtBC,MAAOd,MAKXoB,QAAUC,GAASxB,EAAKW,IACtB,MAAMc,EAAU,CACdC,GAAIX,KAAKY,MACTC,KAAMJ,EACNK,WAAA,IAAed,MAAOC,eAExBL,EAAMP,MAAMQ,KAAKa,GACjBd,EAAMF,QAAQG,KAAK,CACjBC,OAAQ,UACRC,WAAA,IAAeC,MAAOC,cACtBC,MAAOO,MAIXM,WAAaJ,GAAO1B,EAAKW,IACvB,MAAMoB,EAAQpB,EAAMP,MAAM4B,UAAUR,GAAQA,EAAKE,KAAOA,GACxD,GAAIK,GAAQ,EAAI,CACd,MAAME,EAActB,EAAMP,MAAM2B,GAChCpB,EAAMP,MAAM8B,OAAOH,EAAO,GAC1BpB,EAAMF,QAAQG,KAAK,CACjBC,OAAQ,aACRC,WAAA,IAAeC,MAAOC,cACtBC,MAAOgB,EAAYL,MAEvB,IAGFO,WAAY,CAACT,EAAIU,IAAYpC,EAAKW,IAChC,MAAMa,EAAOb,EAAMP,MAAMiC,KAAKb,GAAQA,EAAKE,KAAOA,GAClD,GAAIF,EAAM,CACR,MAAMc,EAAUd,EAAKI,KACrBJ,EAAKI,KAAOQ,EACZZ,EAAKe,WAAA,IAAgBxB,MAAOC,cAC5BL,EAAMF,QAAQG,KAAK,CACjBC,OAAQ,aACRC,WAAA,IAAeC,MAAOC,cACtBC,MAAO,GAAGqB,OAAaF,KAE3B,IAIFI,WAAaC,GAAazC,EAAKW,IAC7B+B,OAAOC,OAAOhC,EAAMN,KAAMoC,GAC1B9B,EAAMF,QAAQG,KAAK,CACjBC,OAAQ,aACRC,WAAA,IAAeC,MAAOC,cACtBC,MAAOwB,MAKXG,YAAcC,GAAY7C,EAAKW,SACP,IAAlBkC,EAAQ3C,QAAqBS,EAAMT,MAAQ2C,EAAQ3C,YAClC,IAAjB2C,EAAQ1C,OAAoBQ,EAAMR,KAAO0C,EAAQ1C,WAChC,IAAjB0C,EAAQxC,MAAoBqC,OAAOC,OAAOhC,EAAMN,KAAMwC,EAAQxC,MAElEM,EAAMF,QAAQG,KAAK,CACjBC,OAAQ,cACRC,WAAA,IAAeC,MAAOC,cACtBC,MAAO4B,MAKXC,uBAAwBC,MAAOC,EAAQ,OACrChD,EAAKW,IACHA,EAAMJ,WAAY,EAClBI,EAAMH,MAAQ,OAGhB,UACQ,IAAIyC,QAAQ,CAACC,EAASC,KAC1BC,WAAW,KAELC,KAAKC,SAAW,GAClBH,EAAO,IAAII,MAAM,cAEjBL,KAEDF,KAGLhD,EAAKW,IACHA,EAAMJ,WAAY,EAClBI,EAAMT,OAAS,GACfS,EAAMF,QAAQG,KAAK,CACjBC,OAAQ,yBACRC,WAAA,IAAeC,MAAOC,cACtBC,MAAO,YAGb,OAAST,GACPR,EAAKW,IACHA,EAAMJ,WAAY,EAClBI,EAAMH,MAAQA,EAAMgD,QACpB7C,EAAMF,QAAQG,KAAK,CACjBC,OAAQ,yBACRC,WAAA,IAAeC,MAAOC,cACtBC,MAAO,WAAWT,EAAMgD,aAG9B,GAIFC,WAAY,IAAMzD,EAAKW,IACrBA,EAAMH,MAAQ,OAIhBkD,aAAc,IAAM1D,EAAKW,IACvBA,EAAMF,QAAU,KAIlBkD,MAAO,IAAM3D,EAAKW,IAChBA,EAAMT,MAAQ,EACdS,EAAMR,KAAO,GACbQ,EAAMP,MAAQ,GACdO,EAAMN,KAAO,CAAEF,KAAM,GAAIG,MAAO,IAChCK,EAAMJ,WAAY,EAClBI,EAAMH,MAAQ,KACdG,EAAMF,QAAU,CAAC,CACfI,OAAQ,QACRC,WAAA,IAAeC,MAAOC,cACtBC,MAAO,YAKX2C,eAAgB,IAAoB,EAAd3D,IAAMC,MAC5B2D,eAAgB,IAAoB,EAAd5D,IAAMC,MAC5B4D,iBAAkB,KAChB,MAAMzD,KAAEA,GAASJ,IACjB,OAAOI,EAAKF,MAAQE,EAAKC,MACrB,GAAGD,EAAKF,SAASE,EAAKC,SACtB,WAENyD,iBAAkB,IAAM9D,IAAMQ,QAAQuD,OAAM,IAC5CC,SAAU,KACR,MAAMtD,EAAQV,IACd,MAAO,CACLiE,WAAYvD,EAAMP,MAAM+D,OACxBC,aAAczD,EAAMF,QAAQ0D,OAC5BE,WAAY1D,EAAMF,QAAQE,EAAMF,QAAQ0D,OAAS,IAAItD,QAAU,OAC/DyD,WAAY3D,EAAMH,MAClB+D,SAAU5D,EAAMT,MAAQ,GAAKS,EAAMP,MAAM+D,OAAS,UAUjDK,EAAe7D,GAAUA,EAAMT,MAC/BuE,EAAqB9D,GAAUA,EAAMiD,iBACrCc,EAAqB/D,GAAUA,EAAMkD,iBACrCc,EAAchE,GAAUA,EAAMR,KAC9ByE,EAAejE,GAAUA,EAAMP,MAC/ByE,EAAclE,GAAUA,EAAMN,KAC9ByE,EAAuBnE,GAAUA,EAAMmD,mBACvCiB,EAAmBpE,GAAUA,EAAMJ,UACnCyE,EAAerE,GAAUA,EAAMH,MAC/ByE,EAAiBtE,GAAUA,EAAMF,QACjCyE,EAAuBvE,GAAUA,EAAMoD,mBACvCoB,EAAexE,GAAUA,EAAMsD"}