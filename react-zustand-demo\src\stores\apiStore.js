import { create } from 'zustand'
import { devtools, subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: 'https://jsonplaceholder.typicode.com',
  timeout: 10000,
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('🚀 发送请求:', config.method.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('❌ 请求配置错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('✅ 响应成功:', response.status, response.config.url)
    return response
  },
  (error) => {
    console.error('❌ 响应错误:', error.response?.status, error.message)
    return Promise.reject(error)
  }
)

// API store - 展示异步操作和HTTP请求管理
export const useApiStore = create(
  devtools(
    subscribeWithSelector(
      immer((set, get) => ({
        // 状态
        users: [],
        currentUser: null,
        posts: [],
        comments: [],
        isLoading: false,
        loadingStates: {}, // 分别跟踪不同操作的加载状态
        errors: {},
        requestLogs: [],
        cache: {}, // 简单的请求缓存

        // 工具方法
        addRequestLog: (log) => set((state) => {
          state.requestLogs.unshift({
            id: Date.now(),
            timestamp: new Date().toISOString(),
            ...log
          })
          // 保持最近100条日志
          if (state.requestLogs.length > 100) {
            state.requestLogs = state.requestLogs.slice(0, 100)
          }
        }),

        setLoading: (operation, loading) => set((state) => {
          state.loadingStates[operation] = loading
          // 更新全局loading状态
          state.isLoading = Object.values(state.loadingStates).some(Boolean)
        }),

        setError: (operation, error) => set((state) => {
          if (error) {
            state.errors[operation] = error
          } else {
            delete state.errors[operation]
          }
        }),

        // 获取用户列表
        fetchUsers: async () => {
          const cacheKey = 'users'
          const cached = get().cache[cacheKey]
          
          // 检查缓存（5分钟有效期）
          if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
            set((state) => {
              state.users = cached.data
            })
            get().addRequestLog({
              method: 'GET',
              url: '/users',
              status: 'cached',
              message: '从缓存获取用户列表'
            })
            return cached.data
          }

          get().setLoading('fetchUsers', true)
          get().setError('fetchUsers', null)

          try {
            const response = await api.get('/users')
            const users = response.data

            set((state) => {
              state.users = users
              state.cache[cacheKey] = {
                data: users,
                timestamp: Date.now()
              }
            })

            get().addRequestLog({
              method: 'GET',
              url: '/users',
              status: response.status,
              message: `成功获取${users.length}个用户`
            })

            return users
          } catch (error) {
            const errorMessage = error.response?.data?.message || error.message || '获取用户列表失败'
            
            get().setError('fetchUsers', errorMessage)
            get().addRequestLog({
              method: 'GET',
              url: '/users',
              status: error.response?.status || 'error',
              message: errorMessage
            })
            
            throw error
          } finally {
            get().setLoading('fetchUsers', false)
          }
        },

        // 获取单个用户
        fetchUserById: async (id) => {
          const cacheKey = `user-${id}`
          const cached = get().cache[cacheKey]
          
          if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
            set((state) => {
              state.currentUser = cached.data
            })
            get().addRequestLog({
              method: 'GET',
              url: `/users/${id}`,
              status: 'cached',
              message: '从缓存获取用户详情'
            })
            return cached.data
          }

          get().setLoading('fetchUser', true)
          get().setError('fetchUser', null)

          try {
            const response = await api.get(`/users/${id}`)
            const user = response.data

            set((state) => {
              state.currentUser = user
              state.cache[cacheKey] = {
                data: user,
                timestamp: Date.now()
              }
            })

            get().addRequestLog({
              method: 'GET',
              url: `/users/${id}`,
              status: response.status,
              message: `成功获取用户: ${user.name}`
            })

            return user
          } catch (error) {
            const errorMessage = error.response?.status === 404 
              ? '用户不存在' 
              : error.response?.data?.message || error.message || '获取用户详情失败'
            
            get().setError('fetchUser', errorMessage)
            get().addRequestLog({
              method: 'GET',
              url: `/users/${id}`,
              status: error.response?.status || 'error',
              message: errorMessage
            })
            
            throw error
          } finally {
            get().setLoading('fetchUser', false)
          }
        },

        // 创建用户
        createUser: async (userData) => {
          get().setLoading('createUser', true)
          get().setError('createUser', null)

          try {
            const response = await api.post('/users', userData)
            const newUser = { ...response.data, id: Date.now() } // 模拟真实ID

            set((state) => {
              state.users.unshift(newUser)
            })

            get().addRequestLog({
              method: 'POST',
              url: '/users',
              status: response.status,
              message: `成功创建用户: ${newUser.name}`
            })

            return newUser
          } catch (error) {
            const errorMessage = error.response?.data?.message || error.message || '创建用户失败'
            
            get().setError('createUser', errorMessage)
            get().addRequestLog({
              method: 'POST',
              url: '/users',
              status: error.response?.status || 'error',
              message: errorMessage
            })
            
            throw error
          } finally {
            get().setLoading('createUser', false)
          }
        },

        // 更新用户
        updateUser: async (id, userData) => {
          get().setLoading('updateUser', true)
          get().setError('updateUser', null)

          try {
            const response = await api.put(`/users/${id}`, userData)
            const updatedUser = response.data

            set((state) => {
              const index = state.users.findIndex(user => user.id === id)
              if (index > -1) {
                state.users[index] = { ...state.users[index], ...updatedUser }
              }
              
              if (state.currentUser && state.currentUser.id === id) {
                state.currentUser = { ...state.currentUser, ...updatedUser }
              }

              // 清除相关缓存
              delete state.cache[`user-${id}`]
            })

            get().addRequestLog({
              method: 'PUT',
              url: `/users/${id}`,
              status: response.status,
              message: `成功更新用户: ${updatedUser.name || id}`
            })

            return updatedUser
          } catch (error) {
            const errorMessage = error.response?.data?.message || error.message || '更新用户失败'
            
            get().setError('updateUser', errorMessage)
            get().addRequestLog({
              method: 'PUT',
              url: `/users/${id}`,
              status: error.response?.status || 'error',
              message: errorMessage
            })
            
            throw error
          } finally {
            get().setLoading('updateUser', false)
          }
        },

        // 删除用户
        deleteUser: async (id) => {
          get().setLoading('deleteUser', true)
          get().setError('deleteUser', null)

          try {
            const response = await api.delete(`/users/${id}`)

            set((state) => {
              state.users = state.users.filter(user => user.id !== id)
              
              if (state.currentUser && state.currentUser.id === id) {
                state.currentUser = null
              }

              // 清除相关缓存
              delete state.cache[`user-${id}`]
            })

            get().addRequestLog({
              method: 'DELETE',
              url: `/users/${id}`,
              status: response.status,
              message: `成功删除用户: ${id}`
            })

            return true
          } catch (error) {
            const errorMessage = error.response?.data?.message || error.message || '删除用户失败'
            
            get().setError('deleteUser', errorMessage)
            get().addRequestLog({
              method: 'DELETE',
              url: `/users/${id}`,
              status: error.response?.status || 'error',
              message: errorMessage
            })
            
            throw error
          } finally {
            get().setLoading('deleteUser', false)
          }
        },

        // 获取文章列表
        fetchPosts: async (userId = null) => {
          const url = userId ? `/users/${userId}/posts` : '/posts'
          const cacheKey = userId ? `posts-user-${userId}` : 'posts'
          const cached = get().cache[cacheKey]
          
          if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
            set((state) => {
              state.posts = cached.data
            })
            get().addRequestLog({
              method: 'GET',
              url,
              status: 'cached',
              message: '从缓存获取文章列表'
            })
            return cached.data
          }

          get().setLoading('fetchPosts', true)
          get().setError('fetchPosts', null)

          try {
            const response = await api.get(url)
            const posts = response.data

            set((state) => {
              state.posts = posts
              state.cache[cacheKey] = {
                data: posts,
                timestamp: Date.now()
              }
            })

            get().addRequestLog({
              method: 'GET',
              url,
              status: response.status,
              message: `成功获取${posts.length}篇文章`
            })

            return posts
          } catch (error) {
            const errorMessage = error.response?.data?.message || error.message || '获取文章列表失败'
            
            get().setError('fetchPosts', errorMessage)
            get().addRequestLog({
              method: 'GET',
              url,
              status: error.response?.status || 'error',
              message: errorMessage
            })
            
            throw error
          } finally {
            get().setLoading('fetchPosts', false)
          }
        },

        // 获取评论
        fetchComments: async (postId) => {
          const url = `/posts/${postId}/comments`
          const cacheKey = `comments-${postId}`
          const cached = get().cache[cacheKey]
          
          if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
            set((state) => {
              state.comments = cached.data
            })
            return cached.data
          }

          get().setLoading('fetchComments', true)
          get().setError('fetchComments', null)

          try {
            const response = await api.get(url)
            const comments = response.data

            set((state) => {
              state.comments = comments
              state.cache[cacheKey] = {
                data: comments,
                timestamp: Date.now()
              }
            })

            get().addRequestLog({
              method: 'GET',
              url,
              status: response.status,
              message: `成功获取${comments.length}条评论`
            })

            return comments
          } catch (error) {
            const errorMessage = error.response?.data?.message || error.message || '获取评论失败'
            
            get().setError('fetchComments', errorMessage)
            get().addRequestLog({
              method: 'GET',
              url,
              status: error.response?.status || 'error',
              message: errorMessage
            })
            
            throw error
          } finally {
            get().setLoading('fetchComments', false)
          }
        },

        // 设置当前用户
        setCurrentUser: (user) => set((state) => {
          state.currentUser = user
        }),

        // 清除错误
        clearError: (operation = null) => set((state) => {
          if (operation) {
            delete state.errors[operation]
          } else {
            state.errors = {}
          }
        }),

        // 清除请求日志
        clearRequestLogs: () => set((state) => {
          state.requestLogs = []
        }),

        // 清除缓存
        clearCache: () => set((state) => {
          state.cache = {}
        }),

        // 重置store
        reset: () => set((state) => {
          state.users = []
          state.currentUser = null
          state.posts = []
          state.comments = []
          state.isLoading = false
          state.loadingStates = {}
          state.errors = {}
          state.requestLogs = []
          state.cache = {}
        }),

        // 计算属性
        getStats: () => {
          const state = get()
          return {
            totalUsers: state.users.length,
            totalPosts: state.posts.length,
            totalComments: state.comments.length,
            totalRequests: state.requestLogs.length,
            cacheSize: Object.keys(state.cache).length,
            hasErrors: Object.keys(state.errors).length > 0,
            isAnyLoading: state.isLoading
          }
        },

        getErrorSummary: () => {
          const errors = get().errors
          return Object.keys(errors).map(operation => ({
            operation,
            error: errors[operation]
          }))
        },

        getRequestsByMethod: (method) => {
          return get().requestLogs.filter(log => log.method === method)
        },

        getSuccessfulRequests: () => {
          return get().requestLogs.filter(log => 
            typeof log.status === 'number' && log.status >= 200 && log.status < 300
          )
        },

        getFailedRequests: () => {
          return get().requestLogs.filter(log => 
            log.status === 'error' || (typeof log.status === 'number' && log.status >= 400)
          )
        }
      })),
      { name: 'api-store' }
    )
  )
)

// Selectors
export const selectUsers = (state) => state.users
export const selectCurrentUser = (state) => state.currentUser
export const selectPosts = (state) => state.posts
export const selectComments = (state) => state.comments
export const selectIsLoading = (state) => state.isLoading
export const selectLoadingStates = (state) => state.loadingStates
export const selectErrors = (state) => state.errors
export const selectRequestLogs = (state) => state.requestLogs
export const selectCache = (state) => state.cache
export const selectStats = (state) => {
  const { users, requestLogs, errors } = state
  const totalRequests = requestLogs.length
  const errorCount = Object.values(errors).filter(Boolean).length
  return {
    totalUsers: users.length,
    totalRequests,
    errorCount,
    successRate: totalRequests > 0 ? ((totalRequests - errorCount) / totalRequests * 100).toFixed(1) : '100'
  }
}
export const selectErrorSummary = (state) => {
  const errors = Object.values(state.errors).filter(Boolean)
  return {
    hasErrors: errors.length > 0,
    errorCount: errors.length,
    latestError: errors[errors.length - 1] || null
  }
}

// 特定操作的加载状态选择器
export const selectIsLoadingUsers = (state) => state.loadingStates.fetchUsers || false
export const selectIsLoadingUser = (state) => state.loadingStates.fetchUser || false
export const selectIsCreatingUser = (state) => state.loadingStates.createUser || false
export const selectIsUpdatingUser = (state) => state.loadingStates.updateUser || false
export const selectIsDeletingUser = (state) => state.loadingStates.deleteUser || false

// 错误选择器
export const selectUsersError = (state) => state.errors.fetchUsers
export const selectUserError = (state) => state.errors.fetchUser
export const selectCreateUserError = (state) => state.errors.createUser

// 请求日志选择器
export const selectSuccessfulRequests = (state) => state.requestLogs.filter(log => log.status && log.status < 400)
export const selectFailedRequests = (state) => state.requestLogs.filter(log => log.status && log.status >= 400)
export const selectRecentRequests = (state) => state.requestLogs.slice(0, 10)

// 订阅示例
export const subscribeToUsers = (callback) => {
  return useApiStore.subscribe(
    (state) => state.users,
    callback
  )
}

export const subscribeToErrors = (callback) => {
  return useApiStore.subscribe(
    (state) => state.errors,
    callback
  )
} 