{"version": 3, "file": "page-basicstore-ECzgDzuM.js", "sources": ["../../src/pages/BasicStore.jsx"], "sourcesContent": ["import React, { useState } from 'react'\r\nimport { useBasicStore, selectCount, selectDoubleCount, selectTripleCount, selectName, selectItems, selectFormattedUser, selectIsLoading, selectError, selectRecentHistory } from '@/stores/basicStore'\r\n\r\nfunction BasicStore() {\r\n  // 使用Zustand选择器获取状态\r\n  const count = useBasicStore(selectCount)\r\n  const doubleCount = useBasicStore(selectDoubleCount)\r\n  const tripleCount = useBasicStore(selectTripleCount)\r\n  const name = useBasicStore(selectName)\r\n  const items = useBasicStore(selectItems)\r\n  const formattedUser = useBasicStore(selectFormattedUser)\r\n  const isLoading = useBasicStore(selectIsLoading)\r\n  const error = useBasicStore(selectError)\r\n  const recentHistory = useBasicStore(selectRecentHistory)\r\n\r\n  // 获取actions\r\n  const { \r\n    increment, \r\n    decrement, \r\n    incrementByAmount, \r\n    resetCount,\r\n    setName, \r\n    addItem, \r\n    removeItem, \r\n    updateItem,\r\n    updateUser,\r\n    batchUpdate,\r\n    simulateAsyncOperation,\r\n    clearError,\r\n    clearHistory,\r\n    reset \r\n  } = useBasicStore()\r\n\r\n  // 本地状态\r\n  const [incrementAmount, setIncrementAmount] = useState(5)\r\n  const [newName, setNewName] = useState('')\r\n  const [newItem, setNewItem] = useState('')\r\n  const [editingIndex, setEditingIndex] = useState(-1)\r\n  const [editingValue, setEditingValue] = useState('')\r\n  const [userForm, setUserForm] = useState({ name: '', email: '' })\r\n\r\n  // 事件处理函数\r\n  const handleIncrementByAmount = () => {\r\n    incrementByAmount(incrementAmount)\r\n  }\r\n\r\n  const handleSetName = () => {\r\n    if (newName.trim()) {\r\n      setName(newName.trim())\r\n      setNewName('')\r\n    }\r\n  }\r\n\r\n  const handleAddItem = () => {\r\n    if (newItem.trim()) {\r\n      addItem(newItem.trim())\r\n      setNewItem('')\r\n    }\r\n  }\r\n\r\n  const handleEditItem = (index) => {\r\n    setEditingIndex(index)\r\n    setEditingValue(items[index].text)\r\n  }\r\n\r\n  const handleSaveEdit = () => {\r\n    if (editingValue.trim()) {\r\n      updateItem(items[editingIndex].id, editingValue.trim())\r\n    }\r\n    setEditingIndex(-1)\r\n    setEditingValue('')\r\n  }\r\n\r\n  const handleCancelEdit = () => {\r\n    setEditingIndex(-1)\r\n    setEditingValue('')\r\n  }\r\n\r\n  const handleUpdateUser = () => {\r\n    if (userForm.name.trim() || userForm.email.trim()) {\r\n      updateUser(userForm)\r\n      setUserForm({ name: '', email: '' })\r\n    }\r\n  }\r\n\r\n  const handleBatchUpdate = () => {\r\n    batchUpdate({\r\n      count: 100,\r\n      name: '批量更新用户',\r\n      user: { name: '张三', email: '<EMAIL>' }\r\n    })\r\n  }\r\n\r\n  return (\r\n    <div className=\"basic-store-page\">\r\n      <div className=\"card-header\">\r\n        <h1 className=\"card-title\">📦 基础Store演示</h1>\r\n        <p className=\"card-description\">\r\n          展示Zustand的基础功能：状态管理、选择器、异步操作和历史记录\r\n        </p>\r\n      </div>\r\n\r\n      {/* 错误提示 */}\r\n      {error && (\r\n        <div className=\"alert alert-danger\">\r\n          <strong>错误：</strong> {error}\r\n          <button onClick={clearError} className=\"btn btn-sm ml-auto\">\r\n            清除\r\n          </button>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"grid grid-2\">\r\n        {/* 计数器部分 */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">🔢 计数器操作</h2>\r\n          \r\n          <div className=\"text-center mb-lg\">\r\n            <div className=\"text-2xl font-bold text-primary mb-sm\">{count}</div>\r\n            <div className=\"text-sm text-muted\">\r\n              双倍: {doubleCount} | 三倍: {tripleCount}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex gap-sm mb-md\">\r\n            <button onClick={increment} className=\"btn btn-success flex-1\">\r\n              ➕ 增加\r\n            </button>\r\n            <button onClick={decrement} className=\"btn btn-danger flex-1\">\r\n              ➖ 减少\r\n            </button>\r\n          </div>\r\n\r\n          <div className=\"flex gap-sm mb-md\">\r\n            <input\r\n              type=\"number\"\r\n              value={incrementAmount}\r\n              onChange={(e) => setIncrementAmount(Number(e.target.value))}\r\n              className=\"form-control\"\r\n              placeholder=\"增加数量\"\r\n            />\r\n            <button onClick={handleIncrementByAmount} className=\"btn btn-primary\">\r\n              增加 {incrementAmount}\r\n            </button>\r\n          </div>\r\n\r\n          <button onClick={resetCount} className=\"btn btn-secondary btn-sm\">\r\n            🔄 重置计数器\r\n          </button>\r\n        </div>\r\n\r\n        {/* 姓名管理 */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">👤 姓名管理</h2>\r\n          \r\n          <div className=\"mb-md\">\r\n            <div className=\"text-lg font-semibold\">\r\n              当前姓名: <span className=\"text-primary\">{name || '未设置'}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex gap-sm\">\r\n            <input\r\n              type=\"text\"\r\n              value={newName}\r\n              onChange={(e) => setNewName(e.target.value)}\r\n              onKeyPress={(e) => e.key === 'Enter' && handleSetName()}\r\n              className=\"form-control\"\r\n              placeholder=\"输入新姓名\"\r\n            />\r\n            <button onClick={handleSetName} className=\"btn btn-primary\">\r\n              设置\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 项目列表 */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">📝 项目列表管理</h2>\r\n          \r\n          <div className=\"flex gap-sm mb-md\">\r\n            <input\r\n              type=\"text\"\r\n              value={newItem}\r\n              onChange={(e) => setNewItem(e.target.value)}\r\n              onKeyPress={(e) => e.key === 'Enter' && handleAddItem()}\r\n              className=\"form-control\"\r\n              placeholder=\"添加新项目\"\r\n            />\r\n            <button onClick={handleAddItem} className=\"btn btn-success\">\r\n              ➕ 添加\r\n            </button>\r\n          </div>\r\n\r\n          <div className=\"space-y-2\">\r\n            {items.map((item, index) => (\r\n              <div key={item.id} className=\"flex items-center gap-sm p-sm border rounded\">\r\n                {editingIndex === index ? (\r\n                  <>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={editingValue}\r\n                      onChange={(e) => setEditingValue(e.target.value)}\r\n                      className=\"form-control flex-1\"\r\n                      autoFocus\r\n                    />\r\n                    <button onClick={handleSaveEdit} className=\"btn btn-success btn-sm\">\r\n                      ✓\r\n                    </button>\r\n                    <button onClick={handleCancelEdit} className=\"btn btn-secondary btn-sm\">\r\n                      ✗\r\n                    </button>\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <span className=\"flex-1\">{item.text}</span>\r\n                    <button \r\n                      onClick={() => handleEditItem(index)} \r\n                      className=\"btn btn-primary btn-sm\"\r\n                    >\r\n                      ✏️\r\n                    </button>\r\n                    <button \r\n                      onClick={() => removeItem(item.id)} \r\n                      className=\"btn btn-danger btn-sm\"\r\n                    >\r\n                      🗑️\r\n                    </button>\r\n                  </>\r\n                )}\r\n              </div>\r\n            ))}\r\n            {items.length === 0 && (\r\n              <div className=\"text-center text-muted py-lg\">\r\n                暂无项目，请添加一些项目\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* 用户信息 */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">👨‍💼 用户信息管理</h2>\r\n          \r\n          <div className=\"mb-md\">\r\n            <div className=\"text-lg font-semibold\">\r\n              {formattedUser}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"space-y-2 mb-md\">\r\n            <input\r\n              type=\"text\"\r\n              value={userForm.name}\r\n              onChange={(e) => setUserForm({ ...userForm, name: e.target.value })}\r\n              className=\"form-control\"\r\n              placeholder=\"用户姓名\"\r\n            />\r\n            <input\r\n              type=\"email\"\r\n              value={userForm.email}\r\n              onChange={(e) => setUserForm({ ...userForm, email: e.target.value })}\r\n              className=\"form-control\"\r\n              placeholder=\"用户邮箱\"\r\n            />\r\n          </div>\r\n\r\n          <button onClick={handleUpdateUser} className=\"btn btn-primary\">\r\n            更新用户信息\r\n          </button>\r\n        </div>\r\n\r\n        {/* 异步操作 */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">⚡ 异步操作演示</h2>\r\n          \r\n          <div className=\"mb-md\">\r\n            <p className=\"text-sm text-secondary\">\r\n              模拟异步操作，随机成功或失败（80%成功率）\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"flex gap-sm\">\r\n            <button \r\n              onClick={() => simulateAsyncOperation(1000)} \r\n              disabled={isLoading}\r\n              className=\"btn btn-warning flex-1\"\r\n            >\r\n              {isLoading ? (\r\n                <>\r\n                  <span className=\"spinner\"></span>\r\n                  执行中...\r\n                </>\r\n              ) : (\r\n                '🚀 执行异步操作'\r\n              )}\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 批量操作 */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">📦 批量操作</h2>\r\n          \r\n          <div className=\"flex gap-sm mb-md\">\r\n            <button onClick={handleBatchUpdate} className=\"btn btn-info\">\r\n              🔄 批量更新\r\n            </button>\r\n            <button onClick={reset} className=\"btn btn-danger\">\r\n              🗑️ 重置所有\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 操作历史 */}\r\n      <div className=\"card mt-xl\">\r\n        <div className=\"flex justify-between items-center mb-md\">\r\n          <h2 className=\"card-title\">📜 操作历史（最近10条）</h2>\r\n          <button onClick={clearHistory} className=\"btn btn-secondary btn-sm\">\r\n            清空历史\r\n          </button>\r\n        </div>\r\n        \r\n        <div className=\"space-y-1\">\r\n          {recentHistory.map((entry, index) => (\r\n            <div key={index} className=\"flex justify-between items-center p-sm bg-surface rounded text-sm\">\r\n              <span>\r\n                <strong>{entry.action}</strong>\r\n                {entry.value && `: ${JSON.stringify(entry.value)}`}\r\n              </span>\r\n              <span className=\"text-muted\">\r\n                {new Date(entry.timestamp).toLocaleTimeString()}\r\n              </span>\r\n            </div>\r\n          ))}\r\n          {recentHistory.length === 0 && (\r\n            <div className=\"text-center text-muted py-lg\">\r\n              暂无操作历史\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default BasicStore "], "names": ["BasicStore", "count", "useBasicStore", "selectCount", "doubleCount", "selectDoubleCount", "tripleCount", "selectTripleCount", "name", "selectName", "items", "selectItems", "formattedUser", "selectFormattedUser", "isLoading", "selectIsLoading", "error", "selectError", "recentHistory", "selectRecentHistory", "increment", "decrement", "incrementByAmount", "resetCount", "setName", "addItem", "removeItem", "updateItem", "updateUser", "batchUpdate", "simulateAsyncOperation", "clearError", "clearHistory", "reset", "incrementAmount", "setIncrementAmount", "useState", "newName", "setNewName", "newItem", "setNewItem", "editingIndex", "setEditingIndex", "editingValue", "setEditingValue", "userForm", "setUserForm", "email", "handleSetName", "trim", "handleAddItem", "handleSaveEdit", "id", "handleCancelEdit", "jsxs", "className", "children", "jsx", "onClick", "type", "value", "onChange", "e", "Number", "target", "placeholder", "onKeyPress", "key", "map", "item", "index", "Fragment", "autoFocus", "text", "handleEditItem", "length", "disabled", "user", "entry", "action", "JSON", "stringify", "Date", "timestamp", "toLocaleTimeString"], "mappings": "wKAGA,SAASA,IAEP,MAAMC,EAAQC,EAAcC,GACtBC,EAAcF,EAAcG,GAC5BC,EAAcJ,EAAcK,GAC5BC,EAAON,EAAcO,GACrBC,EAAQR,EAAcS,GACtBC,EAAgBV,EAAcW,GAC9BC,EAAYZ,EAAca,GAC1BC,EAAQd,EAAce,GACtBC,EAAgBhB,EAAciB,IAG9BC,UACJA,EAAAC,UACAA,EAAAC,kBACAA,EAAAC,WACAA,EAAAC,QACAA,EAAAC,QACAA,EAAAC,WACAA,EAAAC,WACAA,EAAAC,WACAA,EAAAC,YACAA,EAAAC,uBACAA,EAAAC,WACAA,EAAAC,aACAA,EAAAC,MACAA,GACE/B,KAGGgC,EAAiBC,GAAsBC,EAAAA,SAAS,IAChDC,EAASC,GAAcF,EAAAA,SAAS,KAChCG,EAASC,GAAcJ,EAAAA,SAAS,KAChCK,EAAcC,GAAmBN,EAAAA,UAAS,IAC1CO,EAAcC,GAAmBR,EAAAA,SAAS,KAC1CS,EAAUC,GAAeV,EAAAA,SAAS,CAAE5B,KAAM,GAAIuC,MAAO,KAOtDC,EAAgB,KAChBX,EAAQY,SACVzB,EAAQa,EAAQY,QAChBX,EAAW,MAITY,EAAgB,KAChBX,EAAQU,SACVxB,EAAQc,EAAQU,QAChBT,EAAW,MASTW,EAAiB,KACjBR,EAAaM,QACftB,EAAWjB,EAAM+B,GAAcW,GAAIT,EAAaM,QAElDP,GAAgB,GAChBE,EAAgB,KAGZS,EAAmB,KACvBX,GAAgB,GAChBE,EAAgB,KAkBlB,SACEU,KAAC,MAAA,CAAIC,UAAU,mBACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,iBAC3BC,EAAAA,IAAC,IAAA,CAAEF,UAAU,mBAAmBC,SAAA,yCAMjCxC,GACCsC,EAAAA,KAAC,MAAA,CAAIC,UAAU,qBACbC,SAAA,GAAAC,IAAC,UAAOD,SAAA,QAAY,IAAExC,QACrB,SAAA,CAAO0C,QAAS3B,EAAYwB,UAAU,qBAAqBC,SAAA,YAMhEF,KAAC,MAAA,CAAIC,UAAU,cAEbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,eAE3BF,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,wCAAyCC,SAAAvD,MACxDqD,KAAC,MAAA,CAAIC,UAAU,qBAAqBC,SAAA,CAAA,OAC7BpD,EAAY,UAAQE,UAI7BgD,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAC,MAAC,SAAA,CAAOC,QAAStC,EAAWmC,UAAU,yBAAyBC,SAAA,eAG9D,SAAA,CAAOE,QAASrC,EAAWkC,UAAU,wBAAwBC,SAAA,cAKhEF,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAC,EAAAA,IAAC,QAAA,CACCE,KAAK,SACLC,MAAO1B,EACP2B,SAAWC,GAAM3B,EAAmB4B,OAAOD,EAAEE,OAAOJ,QACpDL,UAAU,eACVU,YAAY,SAEdX,EAAAA,KAAC,SAAA,CAAOI,QAnGc,KAC9BpC,EAAkBY,IAkGgCqB,UAAU,kBAAkBC,SAAA,CAAA,MAChEtB,cAIP,SAAA,CAAOwB,QAASnC,EAAYgC,UAAU,2BAA2BC,SAAA,kBAMpEF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,kBAE1B,MAAA,CAAID,UAAU,QACbC,SAAAF,EAAAA,KAAC,MAAA,CAAIC,UAAU,wBAAwBC,SAAA,CAAA,SAC/BC,EAAAA,IAAC,OAAA,CAAKF,UAAU,eAAgBC,YAAQ,eAIlDF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,QAAA,CACCE,KAAK,OACLC,MAAOvB,EACPwB,SAAWC,GAAMxB,EAAWwB,EAAEE,OAAOJ,OACrCM,WAAaJ,GAAgB,UAAVA,EAAEK,KAAmBnB,IACxCO,UAAU,eACVU,YAAY,gBAEb,SAAA,CAAOP,QAASV,EAAeO,UAAU,kBAAkBC,SAAA,eAOhEF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,gBAE3BF,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAC,EAAAA,IAAC,QAAA,CACCE,KAAK,OACLC,MAAOrB,EACPsB,SAAWC,GAAMtB,EAAWsB,EAAEE,OAAOJ,OACrCM,WAAaJ,GAAgB,UAAVA,EAAEK,KAAmBjB,IACxCK,UAAU,eACVU,YAAY,gBAEb,SAAA,CAAOP,QAASR,EAAeK,UAAU,kBAAkBC,SAAA,cAK9DF,KAAC,MAAA,CAAIC,UAAU,YACZC,SAAA,CAAA9C,EAAM0D,IAAI,CAACC,EAAMC,IAChBb,EAAAA,IAAC,OAAkBF,UAAU,+CAC1BC,SAAAf,IAAiB6B,EAChBhB,EAAAA,KAAAiB,EAAAA,SAAA,CACEf,SAAA,CAAAC,EAAAA,IAAC,QAAA,CACCE,KAAK,OACLC,MAAOjB,EACPkB,SAAWC,GAAMlB,EAAgBkB,EAAEE,OAAOJ,OAC1CL,UAAU,sBACViB,WAAS,UAEV,SAAA,CAAOd,QAASP,EAAgBI,UAAU,yBAAyBC,SAAA,YAGnE,SAAA,CAAOE,QAASL,EAAkBE,UAAU,2BAA2BC,SAAA,SAK1EF,EAAAA,KAAAiB,EAAAA,SAAA,CACEf,SAAA,CAAAC,EAAAA,IAAC,OAAA,CAAKF,UAAU,SAAUC,SAAAa,EAAKI,OAC/BhB,EAAAA,IAAC,SAAA,CACCC,QAAS,IA7JN,CAACY,IACtB5B,EAAgB4B,GAChB1B,EAAgBlC,EAAM4D,GAAOG,OA2JIC,CAAeJ,GAC9Bf,UAAU,yBACXC,SAAA,OAGDC,EAAAA,IAAC,SAAA,CACCC,QAAS,IAAMhC,EAAW2C,EAAKjB,IAC/BG,UAAU,wBACXC,SAAA,YA7BGa,EAAKjB,KAoCC,IAAjB1C,EAAMiE,cACJ,MAAA,CAAIpB,UAAU,+BAA+BC,SAAA,yBAQpDF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,mBAE3BC,IAAC,OAAIF,UAAU,QACbC,eAAC,MAAA,CAAID,UAAU,wBACZC,SAAA5C,QAIL0C,KAAC,MAAA,CAAIC,UAAU,kBACbC,SAAA,CAAAC,EAAAA,IAAC,QAAA,CACCE,KAAK,OACLC,MAAOf,EAASrC,KAChBqD,SAAWC,GAAMhB,EAAY,IAAKD,EAAUrC,KAAMsD,EAAEE,OAAOJ,QAC3DL,UAAU,eACVU,YAAY,SAEdR,EAAAA,IAAC,QAAA,CACCE,KAAK,QACLC,MAAOf,EAASE,MAChBc,SAAWC,GAAMhB,EAAY,IAAKD,EAAUE,MAAOe,EAAEE,OAAOJ,QAC5DL,UAAU,eACVU,YAAY,kBAIf,SAAA,CAAOP,QA7LS,MACnBb,EAASrC,KAAKyC,QAAUJ,EAASE,MAAME,UACzCrB,EAAWiB,GACXC,EAAY,CAAEtC,KAAM,GAAIuC,MAAO,OA0LQQ,UAAU,kBAAkBC,SAAA,gBAMjEF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,eAE3BC,IAAC,OAAIF,UAAU,QACbC,eAAC,IAAA,CAAED,UAAU,yBAAyBC,SAAA,+BAKxCC,IAAC,MAAA,CAAIF,UAAU,cACbC,SAAAC,EAAAA,IAAC,SAAA,CACCC,QAAS,IAAM5B,EAAuB,KACtC8C,SAAU9D,EACVyC,UAAU,yBAETC,WACCF,EAAAA,KAAAiB,EAAAA,SAAA,CACEf,SAAA,GAAAC,IAAC,OAAA,CAAKF,UAAU,YAAiB,YAInC,qBAORD,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAE3BF,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAC,MAAC,SAAA,CAAOC,QA5NQ,KACxB7B,EAAY,CACV5B,MAAO,IACPO,KAAM,SACNqE,KAAM,CAAErE,KAAM,KAAMuC,MAAO,2BAwNeQ,UAAU,eAAeC,SAAA,kBAG5D,SAAA,CAAOE,QAASzB,EAAOsB,UAAU,iBAAiBC,SAAA,wBAQzDF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,0CACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,yBAC1B,SAAA,CAAOE,QAAS1B,EAAcuB,UAAU,2BAA2BC,SAAA,cAKtEF,KAAC,MAAA,CAAIC,UAAU,YACZC,SAAA,CAAAtC,EAAckD,IAAI,CAACU,EAAOR,IACzBhB,OAAC,MAAA,CAAgBC,UAAU,oEACzBC,SAAA,CAAAF,OAAC,OAAA,CACCE,SAAA,GAAAC,IAAC,SAAA,CAAQD,WAAMuB,SACdD,EAAMlB,OAAS,KAAKoB,KAAKC,UAAUH,EAAMlB,YAE5CH,EAAAA,IAAC,OAAA,CAAKF,UAAU,aACbC,SAAA,IAAI0B,KAAKJ,EAAMK,WAAWC,yBANrBd,IAUc,IAAzBpD,EAAcyD,cACZ,MAAA,CAAIpB,UAAU,+BAA+BC,SAAA,mBAQ1D"}