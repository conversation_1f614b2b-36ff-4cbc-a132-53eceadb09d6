{"version": 3, "file": "store-settingsstore-CkjrIQl7.js", "sources": ["../../src/stores/settingsStore.js"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools, subscribeWithSelector, persist, createJSONStorage } from 'zustand/middleware'\r\nimport { immer } from 'zustand/middleware/immer'\r\n\r\n// 设置store - 展示状态持久化和本地存储\r\nexport const useSettingsStore = create(\r\n  devtools(\r\n    persist(\r\n      subscribeWithSelector(\r\n        immer((set, get) => ({\r\n          // 基础设置\r\n          theme: 'auto', // light, dark, auto\r\n          language: 'zh', // zh, en\r\n          notifications: true,\r\n          autoSave: true,\r\n          fontSize: 14,\r\n          layout: 'default', // default, compact, wide\r\n\r\n          // 偏好设置\r\n          preferences: {\r\n            showWelcome: true,\r\n            enableAnimations: true,\r\n            soundEnabled: false,\r\n            emailNotifications: true,\r\n            pushNotifications: false,\r\n            autoRefresh: true,\r\n            showTooltips: true,\r\n            compactMode: false,\r\n          },\r\n\r\n          // 高级设置\r\n          advancedSettings: {\r\n            debugMode: false,\r\n            experimentalFeatures: false,\r\n            performanceMode: false,\r\n            logLevel: 'info', // debug, info, warn, error\r\n            cacheSize: 100,\r\n            requestTimeout: 10000,\r\n            retryAttempts: 3,\r\n            enableMetrics: true,\r\n          },\r\n\r\n          // 用户自定义设置\r\n          customSettings: {\r\n            shortcuts: {\r\n              save: 'Ctrl+S',\r\n              search: 'Ctrl+F',\r\n              help: 'F1',\r\n              console: 'Ctrl+`',\r\n            },\r\n            colors: {\r\n              primary: '#0066cc',\r\n              secondary: '#6c757d',\r\n              success: '#28a745',\r\n              warning: '#ffc107',\r\n              danger: '#dc3545',\r\n            },\r\n            colorScheme: {\r\n              primary: '#0066cc',\r\n              secondary: '#6c757d',\r\n              background: '#ffffff',\r\n              surface: '#f8f9fa',\r\n              text: '#212529',\r\n            },\r\n            dashboard: {\r\n              widgets: ['stats', 'recent', 'quick-actions'],\r\n              layout: 'grid',\r\n              density: 'normal',\r\n            },\r\n          },\r\n\r\n          // 元数据\r\n          lastSaved: null,\r\n          isDirty: false,\r\n          version: '1.0.0',\r\n\r\n          // 基础设置操作\r\n          setTheme: (theme) => set((state) => {\r\n            state.theme = theme\r\n            state.isDirty = true\r\n            state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n            if (state.autoSave) state.isDirty = false\r\n          }),\r\n\r\n          setLanguage: (language) => set((state) => {\r\n            state.language = language\r\n            state.isDirty = true\r\n            state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n            if (state.autoSave) state.isDirty = false\r\n          }),\r\n\r\n          toggleNotifications: () => set((state) => {\r\n            state.notifications = !state.notifications\r\n            state.isDirty = true\r\n            state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n            if (state.autoSave) state.isDirty = false\r\n          }),\r\n\r\n          toggleAutoSave: () => set((state) => {\r\n            state.autoSave = !state.autoSave\r\n            state.isDirty = true\r\n            if (state.autoSave) {\r\n              state.lastSaved = new Date().toISOString()\r\n              state.isDirty = false\r\n            }\r\n          }),\r\n\r\n          setFontSize: (size) => set((state) => {\r\n            if (size >= 10 && size <= 24) {\r\n              state.fontSize = size\r\n              state.isDirty = true\r\n              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n              if (state.autoSave) state.isDirty = false\r\n            }\r\n          }),\r\n\r\n          setLayout: (layout) => set((state) => {\r\n            state.layout = layout\r\n            state.isDirty = true\r\n            state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n            if (state.autoSave) state.isDirty = false\r\n          }),\r\n\r\n          // 偏好设置操作\r\n          updatePreference: (key, value) => set((state) => {\r\n            if (key in state.preferences) {\r\n              state.preferences[key] = value\r\n              state.isDirty = true\r\n              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n              if (state.autoSave) state.isDirty = false\r\n            }\r\n          }),\r\n\r\n          updatePreferences: (preferences) => set((state) => {\r\n            Object.assign(state.preferences, preferences)\r\n            state.isDirty = true\r\n            state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n            if (state.autoSave) state.isDirty = false\r\n          }),\r\n\r\n          togglePreference: (key) => set((state) => {\r\n            if (key in state.preferences && typeof state.preferences[key] === 'boolean') {\r\n              state.preferences[key] = !state.preferences[key]\r\n              state.isDirty = true\r\n              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n              if (state.autoSave) state.isDirty = false\r\n            }\r\n          }),\r\n\r\n          // 高级设置操作\r\n          updateAdvancedSetting: (key, value) => set((state) => {\r\n            if (key in state.advancedSettings) {\r\n              state.advancedSettings[key] = value\r\n              state.isDirty = true\r\n              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n              if (state.autoSave) state.isDirty = false\r\n            }\r\n          }),\r\n\r\n          updateAdvancedSettings: (settings) => set((state) => {\r\n            Object.assign(state.advancedSettings, settings)\r\n            state.isDirty = true\r\n            state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n            if (state.autoSave) state.isDirty = false\r\n          }),\r\n\r\n          toggleAdvancedSetting: (key) => set((state) => {\r\n            if (key in state.advancedSettings && typeof state.advancedSettings[key] === 'boolean') {\r\n              state.advancedSettings[key] = !state.advancedSettings[key]\r\n              state.isDirty = true\r\n              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n              if (state.autoSave) state.isDirty = false\r\n            }\r\n          }),\r\n\r\n          // 自定义设置操作\r\n          updateCustomSetting: (category, key, value) => set((state) => {\r\n            if (category in state.customSettings && key in state.customSettings[category]) {\r\n              state.customSettings[category][key] = value\r\n              state.isDirty = true\r\n              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n              if (state.autoSave) state.isDirty = false\r\n            }\r\n          }),\r\n\r\n          updateShortcut: (action, shortcut) => set((state) => {\r\n            if (action in state.customSettings.shortcuts) {\r\n              state.customSettings.shortcuts[action] = shortcut\r\n              state.isDirty = true\r\n              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n              if (state.autoSave) state.isDirty = false\r\n            }\r\n          }),\r\n\r\n          updateColor: (colorName, color) => set((state) => {\r\n            if (colorName in state.customSettings.colors) {\r\n              state.customSettings.colors[colorName] = color\r\n              state.isDirty = true\r\n              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n              if (state.autoSave) state.isDirty = false\r\n            }\r\n            if (colorName in state.customSettings.colorScheme) {\r\n              state.customSettings.colorScheme[colorName] = color\r\n              state.isDirty = true\r\n              state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n              if (state.autoSave) state.isDirty = false\r\n            }\r\n          }),\r\n\r\n          // 批量操作\r\n          batchUpdate: (updates) => set((state) => {\r\n            // 基础设置\r\n            if (updates.theme) state.theme = updates.theme\r\n            if (updates.language) state.language = updates.language\r\n            if (updates.fontSize) state.fontSize = updates.fontSize\r\n            if (updates.layout) state.layout = updates.layout\r\n            if (typeof updates.notifications === 'boolean') state.notifications = updates.notifications\r\n            if (typeof updates.autoSave === 'boolean') state.autoSave = updates.autoSave\r\n\r\n            // 偏好设置\r\n            if (updates.preferences) {\r\n              Object.assign(state.preferences, updates.preferences)\r\n            }\r\n\r\n            // 高级设置\r\n            if (updates.advancedSettings) {\r\n              Object.assign(state.advancedSettings, updates.advancedSettings)\r\n            }\r\n\r\n            // 自定义设置\r\n            if (updates.customSettings) {\r\n              Object.assign(state.customSettings, updates.customSettings)\r\n            }\r\n\r\n            state.isDirty = true\r\n            state.lastSaved = state.autoSave ? new Date().toISOString() : state.lastSaved\r\n            if (state.autoSave) state.isDirty = false\r\n          }),\r\n\r\n          // 预设配置\r\n          applyPreset: (presetName) => set(() => {\r\n            const presets = {\r\n              developer: {\r\n                theme: 'dark',\r\n                fontSize: 14,\r\n                layout: 'compact',\r\n                preferences: {\r\n                  enableAnimations: false,\r\n                  showWelcome: false,\r\n                  soundEnabled: false,\r\n                  compactMode: true,\r\n                },\r\n                advancedSettings: {\r\n                  debugMode: true,\r\n                  experimentalFeatures: true,\r\n                  performanceMode: true,\r\n                  logLevel: 'debug',\r\n                  enableMetrics: true,\r\n                }\r\n              },\r\n              designer: {\r\n                theme: 'light',\r\n                fontSize: 16,\r\n                layout: 'wide',\r\n                preferences: {\r\n                  enableAnimations: true,\r\n                  showWelcome: true,\r\n                  soundEnabled: true,\r\n                  compactMode: false,\r\n                },\r\n                advancedSettings: {\r\n                  debugMode: false,\r\n                  experimentalFeatures: false,\r\n                  performanceMode: false,\r\n                  logLevel: 'info',\r\n                  enableMetrics: false,\r\n                }\r\n              },\r\n              minimal: {\r\n                theme: 'auto',\r\n                fontSize: 14,\r\n                layout: 'default',\r\n                preferences: {\r\n                  enableAnimations: false,\r\n                  showWelcome: false,\r\n                  soundEnabled: false,\r\n                  compactMode: true,\r\n                },\r\n                advancedSettings: {\r\n                  debugMode: false,\r\n                  experimentalFeatures: false,\r\n                  performanceMode: true,\r\n                  logLevel: 'warn',\r\n                  enableMetrics: false,\r\n                }\r\n              },\r\n              accessibility: {\r\n                theme: 'auto',\r\n                fontSize: 18,\r\n                layout: 'wide',\r\n                preferences: {\r\n                  enableAnimations: false,\r\n                  showWelcome: true,\r\n                  soundEnabled: true,\r\n                  compactMode: false,\r\n                },\r\n                advancedSettings: {\r\n                  debugMode: false,\r\n                  experimentalFeatures: false,\r\n                  performanceMode: false,\r\n                  logLevel: 'info',\r\n                  enableMetrics: false,\r\n                }\r\n              }\r\n            }\r\n\r\n            const preset = presets[presetName]\r\n            if (preset) {\r\n              get().batchUpdate(preset)\r\n            }\r\n          }),\r\n\r\n          // 导入导出\r\n          exportSettings: () => {\r\n            const state = get()\r\n            return {\r\n              theme: state.theme,\r\n              language: state.language,\r\n              notifications: state.notifications,\r\n              autoSave: state.autoSave,\r\n              fontSize: state.fontSize,\r\n              layout: state.layout,\r\n              preferences: state.preferences,\r\n              advancedSettings: state.advancedSettings,\r\n              customSettings: state.customSettings,\r\n              exportedAt: new Date().toISOString(),\r\n              version: state.version,\r\n            }\r\n          },\r\n\r\n          importSettings: (importedSettings) => set((state) => {\r\n            try {\r\n              // 验证并导入设置\r\n              if (importedSettings.theme && ['light', 'dark', 'auto'].includes(importedSettings.theme)) {\r\n                state.theme = importedSettings.theme\r\n              }\r\n              \r\n              if (importedSettings.language && ['zh', 'en'].includes(importedSettings.language)) {\r\n                state.language = importedSettings.language\r\n              }\r\n              \r\n              if (typeof importedSettings.notifications === 'boolean') {\r\n                state.notifications = importedSettings.notifications\r\n              }\r\n              \r\n              if (typeof importedSettings.autoSave === 'boolean') {\r\n                state.autoSave = importedSettings.autoSave\r\n              }\r\n              \r\n              if (importedSettings.fontSize && importedSettings.fontSize >= 10 && importedSettings.fontSize <= 24) {\r\n                state.fontSize = importedSettings.fontSize\r\n              }\r\n              \r\n              if (importedSettings.layout && ['default', 'compact', 'wide'].includes(importedSettings.layout)) {\r\n                state.layout = importedSettings.layout\r\n              }\r\n              \r\n              if (importedSettings.preferences && typeof importedSettings.preferences === 'object') {\r\n                Object.assign(state.preferences, importedSettings.preferences)\r\n              }\r\n              \r\n              if (importedSettings.advancedSettings && typeof importedSettings.advancedSettings === 'object') {\r\n                Object.assign(state.advancedSettings, importedSettings.advancedSettings)\r\n              }\r\n              \r\n              if (importedSettings.customSettings && typeof importedSettings.customSettings === 'object') {\r\n                Object.assign(state.customSettings, importedSettings.customSettings)\r\n              }\r\n              \r\n              state.lastSaved = new Date().toISOString()\r\n              state.isDirty = false\r\n            } catch (error) {\r\n              console.error('导入设置失败:', error)\r\n              throw new Error('导入设置失败：文件格式错误')\r\n            }\r\n          }),\r\n\r\n          // 手动保存\r\n          saveSettings: () => set((state) => {\r\n            state.lastSaved = new Date().toISOString()\r\n            state.isDirty = false\r\n          }),\r\n\r\n          // 重置为默认设置\r\n          resetToDefaults: () => set((state) => {\r\n            const autoSave = state.autoSave // 保留自动保存设置\r\n            \r\n            // 重置基础设置\r\n            state.theme = 'auto'\r\n            state.language = 'zh'\r\n            state.notifications = true\r\n            state.fontSize = 14\r\n            state.layout = 'default'\r\n            \r\n            // 重置偏好设置\r\n            state.preferences = {\r\n              showWelcome: true,\r\n              enableAnimations: true,\r\n              soundEnabled: false,\r\n              emailNotifications: true,\r\n              pushNotifications: false,\r\n              autoRefresh: true,\r\n              showTooltips: true,\r\n              compactMode: false,\r\n            }\r\n            \r\n            // 重置高级设置\r\n            state.advancedSettings = {\r\n              debugMode: false,\r\n              experimentalFeatures: false,\r\n              performanceMode: false,\r\n              logLevel: 'info',\r\n              cacheSize: 100,\r\n              requestTimeout: 10000,\r\n              retryAttempts: 3,\r\n              enableMetrics: true,\r\n            }\r\n            \r\n            // 重置自定义设置\r\n            state.customSettings = {\r\n              shortcuts: {\r\n                save: 'Ctrl+S',\r\n                search: 'Ctrl+F',\r\n                help: 'F1',\r\n                console: 'Ctrl+`',\r\n              },\r\n              colors: {\r\n                primary: '#0066cc',\r\n                secondary: '#6c757d',\r\n                success: '#28a745',\r\n                warning: '#ffc107',\r\n                danger: '#dc3545',\r\n              },\r\n              dashboard: {\r\n                widgets: ['stats', 'recent', 'quick-actions'],\r\n                layout: 'grid',\r\n                density: 'normal',\r\n              },\r\n            }\r\n            \r\n            state.autoSave = autoSave\r\n            state.lastSaved = new Date().toISOString()\r\n            state.isDirty = false\r\n          }),\r\n\r\n          // 重置（包括自动保存）\r\n          reset: () => set((state) => {\r\n            state.theme = 'auto'\r\n            state.language = 'zh'\r\n            state.notifications = true\r\n            state.autoSave = true\r\n            state.fontSize = 14\r\n            state.layout = 'default'\r\n            state.preferences = {\r\n              showWelcome: true,\r\n              enableAnimations: true,\r\n              soundEnabled: false,\r\n              emailNotifications: true,\r\n              pushNotifications: false,\r\n              autoRefresh: true,\r\n              showTooltips: true,\r\n              compactMode: false,\r\n            }\r\n            state.advancedSettings = {\r\n              debugMode: false,\r\n              experimentalFeatures: false,\r\n              performanceMode: false,\r\n              logLevel: 'info',\r\n              cacheSize: 100,\r\n              requestTimeout: 10000,\r\n              retryAttempts: 3,\r\n              enableMetrics: true,\r\n            }\r\n            state.customSettings = {\r\n              shortcuts: {\r\n                save: 'Ctrl+S',\r\n                search: 'Ctrl+F',\r\n                help: 'F1',\r\n                console: 'Ctrl+`',\r\n              },\r\n              colors: {\r\n                primary: '#0066cc',\r\n                secondary: '#6c757d',\r\n                success: '#28a745',\r\n                warning: '#ffc107',\r\n                danger: '#dc3545',\r\n              },\r\n              dashboard: {\r\n                widgets: ['stats', 'recent', 'quick-actions'],\r\n                layout: 'grid',\r\n                density: 'normal',\r\n              },\r\n            }\r\n            state.lastSaved = null\r\n            state.isDirty = false\r\n            state.version = '1.0.0'\r\n          }),\r\n\r\n          // 计算属性\r\n          getThemeLabel: () => {\r\n            const theme = get().theme\r\n            const labels = { light: '浅色', dark: '深色', auto: '自动' }\r\n            return labels[theme] || theme\r\n          },\r\n\r\n          getLanguageLabel: () => {\r\n            const language = get().language\r\n            const labels = { zh: '中文', en: 'English' }\r\n            return labels[language] || language\r\n          },\r\n\r\n          getLayoutLabel: () => {\r\n            const layout = get().layout\r\n            const labels = { default: '默认', compact: '紧凑', wide: '宽屏' }\r\n            return labels[layout] || layout\r\n          },\r\n\r\n          getAllSettings: () => {\r\n            const state = get()\r\n            return {\r\n              theme: state.theme,\r\n              language: state.language,\r\n              notifications: state.notifications,\r\n              autoSave: state.autoSave,\r\n              fontSize: state.fontSize,\r\n              layout: state.layout,\r\n              preferences: state.preferences,\r\n              advancedSettings: state.advancedSettings,\r\n              customSettings: state.customSettings,\r\n            }\r\n          },\r\n\r\n          getSettingsSummary: () => {\r\n            const state = get()\r\n            return {\r\n              theme: state.getThemeLabel(),\r\n              language: state.getLanguageLabel(),\r\n              layout: state.getLayoutLabel(),\r\n              fontSize: `${state.fontSize}px`,\r\n              notifications: state.notifications ? '开启' : '关闭',\r\n              autoSave: state.autoSave ? '开启' : '关闭',\r\n              lastSaved: state.lastSaved,\r\n              isDirty: state.isDirty,\r\n              enabledPreferences: Object.keys(state.preferences).filter(key => state.preferences[key]).length,\r\n              debugMode: state.advancedSettings.debugMode ? '开启' : '关闭',\r\n            }\r\n          }\r\n        })),\r\n        { name: 'settings-store' }\r\n      ),\r\n      {\r\n        name: 'zustand-settings-storage',\r\n        storage: createJSONStorage(() => localStorage),\r\n        partialize: (state) => ({\r\n          theme: state.theme,\r\n          language: state.language,\r\n          notifications: state.notifications,\r\n          autoSave: state.autoSave,\r\n          fontSize: state.fontSize,\r\n          layout: state.layout,\r\n          preferences: state.preferences,\r\n          advancedSettings: state.advancedSettings,\r\n          customSettings: state.customSettings,\r\n          version: state.version,\r\n        }),\r\n        version: 1,\r\n        migrate: (persistedState, version) => {\r\n          // 处理版本迁移\r\n          if (version === 0) {\r\n            // 从旧版本迁移\r\n            persistedState.version = '1.0.0'\r\n          }\r\n          return persistedState\r\n        },\r\n      }\r\n    )\r\n  )\r\n)\r\n\r\n// Selectors\r\nexport const selectTheme = (state) => state.theme\r\nexport const selectLanguage = (state) => state.language\r\nexport const selectNotifications = (state) => state.notifications\r\nexport const selectAutoSave = (state) => state.autoSave\r\nexport const selectFontSize = (state) => state.fontSize\r\nexport const selectLayout = (state) => state.layout\r\nexport const selectPreferences = (state) => state.preferences\r\nexport const selectAdvancedSettings = (state) => state.advancedSettings\r\nexport const selectCustomSettings = (state) => state.customSettings\r\nexport const selectLastSaved = (state) => state.lastSaved\r\nexport const selectIsDirty = (state) => state.isDirty\r\n\r\n// 标签选择器\r\nexport const selectThemeLabel = (state) => state.getThemeLabel()\r\nexport const selectLanguageLabel = (state) => state.getLanguageLabel()\r\nexport const selectLayoutLabel = (state) => state.getLayoutLabel()\r\n\r\n// 复合选择器\r\nexport const selectAllSettings = (state) => state.getAllSettings()\r\nexport const selectSettingsSummary = (state) => state.getSettingsSummary()\r\n\r\n// 特定偏好选择器\r\nexport const selectShowWelcome = (state) => state.preferences.showWelcome\r\nexport const selectEnableAnimations = (state) => state.preferences.enableAnimations\r\nexport const selectSoundEnabled = (state) => state.preferences.soundEnabled\r\n\r\n// 特定高级设置选择器\r\nexport const selectDebugMode = (state) => state.advancedSettings.debugMode\r\nexport const selectExperimentalFeatures = (state) => state.advancedSettings.experimentalFeatures\r\nexport const selectPerformanceMode = (state) => state.advancedSettings.performanceMode\r\nexport const selectLogLevel = (state) => state.advancedSettings.logLevel\r\n\r\n// 自定义设置选择器\r\nexport const selectShortcuts = (state) => state.customSettings.shortcuts\r\nexport const selectColors = (state) => state.customSettings.colors\r\nexport const selectColorScheme = (state) => state.customSettings.colorScheme\r\nexport const selectDashboard = (state) => state.customSettings.dashboard\r\n\r\n// 订阅示例\r\nexport const subscribeToTheme = (callback) => {\r\n  return useSettingsStore.subscribe(\r\n    (state) => state.theme,\r\n    callback\r\n  )\r\n}\r\n\r\nexport const subscribeToLanguage = (callback) => {\r\n  return useSettingsStore.subscribe(\r\n    (state) => state.language,\r\n    callback\r\n  )\r\n}\r\n\r\nexport const subscribeToSettingsChange = (callback) => {\r\n  return useSettingsStore.subscribe(\r\n    (state) => [state.theme, state.language, state.fontSize, state.layout],\r\n    callback\r\n  )\r\n} "], "names": ["useSettingsStore", "create", "devtools", "persist", "subscribeWithSelector", "immer", "set", "get", "theme", "language", "notifications", "autoSave", "fontSize", "layout", "preferences", "showWelcome", "enableAnimations", "soundEnabled", "emailNotifications", "pushNotifications", "autoRefresh", "showTooltips", "compactMode", "advancedSettings", "debugMode", "experimentalFeatures", "performanceMode", "logLevel", "cacheSize", "requestTimeout", "retryAttempts", "enableMetrics", "customSettings", "shortcuts", "save", "search", "help", "console", "colors", "primary", "secondary", "success", "warning", "danger", "colorScheme", "background", "surface", "text", "dashboard", "widgets", "density", "lastSaved", "isDirty", "version", "setTheme", "state", "Date", "toISOString", "setLanguage", "toggleNotifications", "toggleAutoSave", "setFontSize", "size", "setLayout", "updatePreference", "key", "value", "updatePreferences", "Object", "assign", "togglePreference", "updateAdvancedSetting", "updateAdvancedSettings", "settings", "toggleAdvancedSetting", "updateCustomSetting", "category", "updateShortcut", "action", "shortcut", "updateColor", "colorName", "color", "batchUpdate", "updates", "applyPreset", "presetName", "preset", "developer", "designer", "minimal", "accessibility", "exportSettings", "exportedAt", "importSettings", "importedSettings", "includes", "error", "Error", "saveSettings", "resetToDefaults", "reset", "getThemeLabel", "light", "dark", "auto", "getLanguageLabel", "zh", "en", "getLayoutLabel", "default", "compact", "wide", "getAllSettings", "getSettingsSummary", "enabledPreferences", "keys", "filter", "length", "name", "storage", "createJSONStorage", "localStorage", "partialize", "migrate", "persistedState", "selectTheme", "selectLanguage", "selectNotifications", "selectAutoSave", "selectFontSize", "selectLayout", "selectPreferences", "selectAdvancedSettings", "selectThemeLabel", "selectLanguageLabel", "selectLayoutLabel", "selectAllSettings", "selectSettingsSummary", "selectShortcuts", "selectColorScheme"], "mappings": "kFAKY,MAACA,EAAmBC,EAC9BC,EACEC,EACEC,EACEC,EAAM,CAACC,EAAKC,KAAA,CAEVC,MAAO,OACPC,SAAU,KACVC,eAAe,EACfC,UAAU,EACVC,SAAU,GACVC,OAAQ,UAGRC,YAAa,CACXC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,oBAAoB,EACpBC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,aAAa,GAIfC,iBAAkB,CAChBC,WAAW,EACXC,sBAAsB,EACtBC,iBAAiB,EACjBC,SAAU,OACVC,UAAW,IACXC,eAAgB,IAChBC,cAAe,EACfC,eAAe,GAIjBC,eAAgB,CACdC,UAAW,CACTC,KAAM,SACNC,OAAQ,SACRC,KAAM,KACNC,QAAS,UAEXC,OAAQ,CACNC,QAAS,UACTC,UAAW,UACXC,QAAS,UACTC,QAAS,UACTC,OAAQ,WAEVC,YAAa,CACXL,QAAS,UACTC,UAAW,UACXK,WAAY,UACZC,QAAS,UACTC,KAAM,WAERC,UAAW,CACTC,QAAS,CAAC,QAAS,SAAU,iBAC7BpC,OAAQ,OACRqC,QAAS,WAKbC,UAAW,KACXC,SAAS,EACTC,QAAS,QAGTC,SAAW9C,GAAUF,EAAKiD,IACxBA,EAAM/C,MAAQA,EACd+C,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,KAGtCM,YAAcjD,GAAaH,EAAKiD,IAC9BA,EAAM9C,SAAWA,EACjB8C,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,KAGtCO,oBAAqB,IAAMrD,EAAKiD,IAC9BA,EAAM7C,eAAiB6C,EAAM7C,cAC7B6C,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,KAGtCQ,eAAgB,IAAMtD,EAAKiD,IACzBA,EAAM5C,UAAY4C,EAAM5C,SACxB4C,EAAMH,SAAU,EACZG,EAAM5C,WACR4C,EAAMJ,WAAA,IAAgBK,MAAOC,cAC7BF,EAAMH,SAAU,KAIpBS,YAAcC,GAASxD,EAAKiD,IACtBO,GAAQ,IAAMA,GAAQ,KACxBP,EAAM3C,SAAWkD,EACjBP,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,MAIxCW,UAAYlD,GAAWP,EAAKiD,IAC1BA,EAAM1C,OAASA,EACf0C,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,KAItCY,iBAAkB,CAACC,EAAKC,IAAU5D,EAAKiD,IACjCU,KAAOV,EAAMzC,cACfyC,EAAMzC,YAAYmD,GAAOC,EACzBX,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,MAIxCe,kBAAoBrD,GAAgBR,EAAKiD,IACvCa,OAAOC,OAAOd,EAAMzC,YAAaA,GACjCyC,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,KAGtCkB,iBAAmBL,GAAQ3D,EAAKiD,IAC1BU,KAAOV,EAAMzC,aAAiD,kBAA3ByC,EAAMzC,YAAYmD,KACvDV,EAAMzC,YAAYmD,IAAQV,EAAMzC,YAAYmD,GAC5CV,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,MAKxCmB,sBAAuB,CAACN,EAAKC,IAAU5D,EAAKiD,IACtCU,KAAOV,EAAMhC,mBACfgC,EAAMhC,iBAAiB0C,GAAOC,EAC9BX,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,MAIxCoB,uBAAyBC,GAAanE,EAAKiD,IACzCa,OAAOC,OAAOd,EAAMhC,iBAAkBkD,GACtClB,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,KAGtCsB,sBAAwBT,GAAQ3D,EAAKiD,IAC/BU,KAAOV,EAAMhC,kBAA2D,kBAAhCgC,EAAMhC,iBAAiB0C,KACjEV,EAAMhC,iBAAiB0C,IAAQV,EAAMhC,iBAAiB0C,GACtDV,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,MAKxCuB,oBAAqB,CAACC,EAAUX,EAAKC,IAAU5D,EAAKiD,IAC9CqB,KAAYrB,EAAMvB,gBAAkBiC,KAAOV,EAAMvB,eAAe4C,KAClErB,EAAMvB,eAAe4C,GAAUX,GAAOC,EACtCX,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,MAIxCyB,eAAgB,CAACC,EAAQC,IAAazE,EAAKiD,IACrCuB,KAAUvB,EAAMvB,eAAeC,YACjCsB,EAAMvB,eAAeC,UAAU6C,GAAUC,EACzCxB,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,MAIxC4B,YAAa,CAACC,EAAWC,IAAU5E,EAAKiD,IAClC0B,KAAa1B,EAAMvB,eAAeM,SACpCiB,EAAMvB,eAAeM,OAAO2C,GAAaC,EACzC3B,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,IAElC6B,KAAa1B,EAAMvB,eAAeY,cACpCW,EAAMvB,eAAeY,YAAYqC,GAAaC,EAC9C3B,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,MAKxC+B,YAAcC,GAAY9E,EAAKiD,IAEzB6B,EAAQ5E,QAAO+C,EAAM/C,MAAQ4E,EAAQ5E,OACrC4E,EAAQ3E,WAAU8C,EAAM9C,SAAW2E,EAAQ3E,UAC3C2E,EAAQxE,WAAU2C,EAAM3C,SAAWwE,EAAQxE,UAC3CwE,EAAQvE,SAAQ0C,EAAM1C,OAASuE,EAAQvE,QACN,kBAA1BuE,EAAQ1E,gBAA6B6C,EAAM7C,cAAgB0E,EAAQ1E,eAC9C,kBAArB0E,EAAQzE,WAAwB4C,EAAM5C,SAAWyE,EAAQzE,UAGhEyE,EAAQtE,aACVsD,OAAOC,OAAOd,EAAMzC,YAAasE,EAAQtE,aAIvCsE,EAAQ7D,kBACV6C,OAAOC,OAAOd,EAAMhC,iBAAkB6D,EAAQ7D,kBAI5C6D,EAAQpD,gBACVoC,OAAOC,OAAOd,EAAMvB,eAAgBoD,EAAQpD,gBAG9CuB,EAAMH,SAAU,EAChBG,EAAMJ,UAAYI,EAAM5C,UAAA,IAAe6C,MAAOC,cAAgBF,EAAMJ,UAChEI,EAAM5C,WAAU4C,EAAMH,SAAU,KAItCiC,YAAcC,GAAehF,EAAI,KAC/B,MA2EMiF,EA3EU,CACdC,UAAW,CACThF,MAAO,OACPI,SAAU,GACVC,OAAQ,UACRC,YAAa,CACXE,kBAAkB,EAClBD,aAAa,EACbE,cAAc,EACdK,aAAa,GAEfC,iBAAkB,CAChBC,WAAW,EACXC,sBAAsB,EACtBC,iBAAiB,EACjBC,SAAU,QACVI,eAAe,IAGnB0D,SAAU,CACRjF,MAAO,QACPI,SAAU,GACVC,OAAQ,OACRC,YAAa,CACXE,kBAAkB,EAClBD,aAAa,EACbE,cAAc,EACdK,aAAa,GAEfC,iBAAkB,CAChBC,WAAW,EACXC,sBAAsB,EACtBC,iBAAiB,EACjBC,SAAU,OACVI,eAAe,IAGnB2D,QAAS,CACPlF,MAAO,OACPI,SAAU,GACVC,OAAQ,UACRC,YAAa,CACXE,kBAAkB,EAClBD,aAAa,EACbE,cAAc,EACdK,aAAa,GAEfC,iBAAkB,CAChBC,WAAW,EACXC,sBAAsB,EACtBC,iBAAiB,EACjBC,SAAU,OACVI,eAAe,IAGnB4D,cAAe,CACbnF,MAAO,OACPI,SAAU,GACVC,OAAQ,OACRC,YAAa,CACXE,kBAAkB,EAClBD,aAAa,EACbE,cAAc,EACdK,aAAa,GAEfC,iBAAkB,CAChBC,WAAW,EACXC,sBAAsB,EACtBC,iBAAiB,EACjBC,SAAU,OACVI,eAAe,KAKEuD,GACnBC,GACFhF,IAAM4E,YAAYI,KAKtBK,eAAgB,KACd,MAAMrC,EAAQhD,IACd,MAAO,CACLC,MAAO+C,EAAM/C,MACbC,SAAU8C,EAAM9C,SAChBC,cAAe6C,EAAM7C,cACrBC,SAAU4C,EAAM5C,SAChBC,SAAU2C,EAAM3C,SAChBC,OAAQ0C,EAAM1C,OACdC,YAAayC,EAAMzC,YACnBS,iBAAkBgC,EAAMhC,iBACxBS,eAAgBuB,EAAMvB,eACtB6D,YAAA,IAAgBrC,MAAOC,cACvBJ,QAASE,EAAMF,UAInByC,eAAiBC,GAAqBzF,EAAKiD,IACzC,IAEMwC,EAAiBvF,OAAS,CAAC,QAAS,OAAQ,QAAQwF,SAASD,EAAiBvF,SAChF+C,EAAM/C,MAAQuF,EAAiBvF,OAG7BuF,EAAiBtF,UAAY,CAAC,KAAM,MAAMuF,SAASD,EAAiBtF,YACtE8C,EAAM9C,SAAWsF,EAAiBtF,UAGU,kBAAnCsF,EAAiBrF,gBAC1B6C,EAAM7C,cAAgBqF,EAAiBrF,eAGA,kBAA9BqF,EAAiBpF,WAC1B4C,EAAM5C,SAAWoF,EAAiBpF,UAGhCoF,EAAiBnF,UAAYmF,EAAiBnF,UAAY,IAAMmF,EAAiBnF,UAAY,KAC/F2C,EAAM3C,SAAWmF,EAAiBnF,UAGhCmF,EAAiBlF,QAAU,CAAC,UAAW,UAAW,QAAQmF,SAASD,EAAiBlF,UACtF0C,EAAM1C,OAASkF,EAAiBlF,QAG9BkF,EAAiBjF,aAAuD,iBAAjCiF,EAAiBjF,aAC1DsD,OAAOC,OAAOd,EAAMzC,YAAaiF,EAAiBjF,aAGhDiF,EAAiBxE,kBAAiE,iBAAtCwE,EAAiBxE,kBAC/D6C,OAAOC,OAAOd,EAAMhC,iBAAkBwE,EAAiBxE,kBAGrDwE,EAAiB/D,gBAA6D,iBAApC+D,EAAiB/D,gBAC7DoC,OAAOC,OAAOd,EAAMvB,eAAgB+D,EAAiB/D,gBAGvDuB,EAAMJ,WAAA,IAAgBK,MAAOC,cAC7BF,EAAMH,SAAU,CAClB,OAAS6C,GAEP,MAAM,IAAIC,MAAM,gBAClB,IAIFC,aAAc,IAAM7F,EAAKiD,IACvBA,EAAMJ,WAAA,IAAgBK,MAAOC,cAC7BF,EAAMH,SAAU,IAIlBgD,gBAAiB,IAAM9F,EAAKiD,IAC1B,MAAM5C,EAAW4C,EAAM5C,SAGvB4C,EAAM/C,MAAQ,OACd+C,EAAM9C,SAAW,KACjB8C,EAAM7C,eAAgB,EACtB6C,EAAM3C,SAAW,GACjB2C,EAAM1C,OAAS,UAGf0C,EAAMzC,YAAc,CAClBC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,oBAAoB,EACpBC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,aAAa,GAIfiC,EAAMhC,iBAAmB,CACvBC,WAAW,EACXC,sBAAsB,EACtBC,iBAAiB,EACjBC,SAAU,OACVC,UAAW,IACXC,eAAgB,IAChBC,cAAe,EACfC,eAAe,GAIjBwB,EAAMvB,eAAiB,CACrBC,UAAW,CACTC,KAAM,SACNC,OAAQ,SACRC,KAAM,KACNC,QAAS,UAEXC,OAAQ,CACNC,QAAS,UACTC,UAAW,UACXC,QAAS,UACTC,QAAS,UACTC,OAAQ,WAEVK,UAAW,CACTC,QAAS,CAAC,QAAS,SAAU,iBAC7BpC,OAAQ,OACRqC,QAAS,WAIbK,EAAM5C,SAAWA,EACjB4C,EAAMJ,WAAA,IAAgBK,MAAOC,cAC7BF,EAAMH,SAAU,IAIlBiD,MAAO,IAAM/F,EAAKiD,IAChBA,EAAM/C,MAAQ,OACd+C,EAAM9C,SAAW,KACjB8C,EAAM7C,eAAgB,EACtB6C,EAAM5C,UAAW,EACjB4C,EAAM3C,SAAW,GACjB2C,EAAM1C,OAAS,UACf0C,EAAMzC,YAAc,CAClBC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,oBAAoB,EACpBC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,aAAa,GAEfiC,EAAMhC,iBAAmB,CACvBC,WAAW,EACXC,sBAAsB,EACtBC,iBAAiB,EACjBC,SAAU,OACVC,UAAW,IACXC,eAAgB,IAChBC,cAAe,EACfC,eAAe,GAEjBwB,EAAMvB,eAAiB,CACrBC,UAAW,CACTC,KAAM,SACNC,OAAQ,SACRC,KAAM,KACNC,QAAS,UAEXC,OAAQ,CACNC,QAAS,UACTC,UAAW,UACXC,QAAS,UACTC,QAAS,UACTC,OAAQ,WAEVK,UAAW,CACTC,QAAS,CAAC,QAAS,SAAU,iBAC7BpC,OAAQ,OACRqC,QAAS,WAGbK,EAAMJ,UAAY,KAClBI,EAAMH,SAAU,EAChBG,EAAMF,QAAU,UAIlBiD,cAAe,KACb,MAAM9F,EAAQD,IAAMC,MAEpB,MADe,CAAE+F,MAAO,KAAMC,KAAM,KAAMC,KAAM,MAClCjG,IAAUA,GAG1BkG,iBAAkB,KAChB,MAAMjG,EAAWF,IAAME,SAEvB,MADe,CAAEkG,GAAI,KAAMC,GAAI,WACjBnG,IAAaA,GAG7BoG,eAAgB,KACd,MAAMhG,EAASN,IAAMM,OAErB,MADe,CAAEiG,QAAS,KAAMC,QAAS,KAAMC,KAAM,MACvCnG,IAAWA,GAG3BoG,eAAgB,KACd,MAAM1D,EAAQhD,IACd,MAAO,CACLC,MAAO+C,EAAM/C,MACbC,SAAU8C,EAAM9C,SAChBC,cAAe6C,EAAM7C,cACrBC,SAAU4C,EAAM5C,SAChBC,SAAU2C,EAAM3C,SAChBC,OAAQ0C,EAAM1C,OACdC,YAAayC,EAAMzC,YACnBS,iBAAkBgC,EAAMhC,iBACxBS,eAAgBuB,EAAMvB,iBAI1BkF,mBAAoB,KAClB,MAAM3D,EAAQhD,IACd,MAAO,CACLC,MAAO+C,EAAM+C,gBACb7F,SAAU8C,EAAMmD,mBAChB7F,OAAQ0C,EAAMsD,iBACdjG,SAAU,GAAG2C,EAAM3C,aACnBF,cAAe6C,EAAM7C,cAAgB,KAAO,KAC5CC,SAAU4C,EAAM5C,SAAW,KAAO,KAClCwC,UAAWI,EAAMJ,UACjBC,QAASG,EAAMH,QACf+D,mBAAoB/C,OAAOgD,KAAK7D,EAAMzC,aAAauG,OAAOpD,GAAOV,EAAMzC,YAAYmD,IAAMqD,OACzF9F,UAAW+B,EAAMhC,iBAAiBC,UAAY,KAAO,WAM7D,CACE+F,KAAM,2BACNC,QAASC,EAAkB,IAAMC,cACjCC,WAAapE,IAAA,CACX/C,MAAO+C,EAAM/C,MACbC,SAAU8C,EAAM9C,SAChBC,cAAe6C,EAAM7C,cACrBC,SAAU4C,EAAM5C,SAChBC,SAAU2C,EAAM3C,SAChBC,OAAQ0C,EAAM1C,OACdC,YAAayC,EAAMzC,YACnBS,iBAAkBgC,EAAMhC,iBACxBS,eAAgBuB,EAAMvB,eACtBqB,QAASE,EAAMF,UAEjBA,QAAS,EACTuE,QAAS,CAACC,EAAgBxE,KAER,IAAZA,IAEFwE,EAAexE,QAAU,SAEpBwE,OAQJC,EAAevE,GAAUA,EAAM/C,MAC/BuH,EAAkBxE,GAAUA,EAAM9C,SAClCuH,EAAuBzE,GAAUA,EAAM7C,cACvCuH,EAAkB1E,GAAUA,EAAM5C,SAClCuH,EAAkB3E,GAAUA,EAAM3C,SAClCuH,EAAgB5E,GAAUA,EAAM1C,OAChCuH,EAAqB7E,GAAUA,EAAMzC,YACrCuH,EAA0B9E,GAAUA,EAAMhC,iBAM1C+G,EAAoB/E,GAAUA,EAAM+C,gBACpCiC,EAAuBhF,GAAUA,EAAMmD,mBACvC8B,EAAqBjF,GAAUA,EAAMsD,iBAGrC4B,EAAqBlF,GAAUA,EAAM0D,iBACrCyB,EAAyBnF,GAAUA,EAAM2D,qBAczCyB,EAAmBpF,GAAUA,EAAMvB,eAAeC,UAElD2G,EAAqBrF,GAAUA,EAAMvB,eAAeY"}