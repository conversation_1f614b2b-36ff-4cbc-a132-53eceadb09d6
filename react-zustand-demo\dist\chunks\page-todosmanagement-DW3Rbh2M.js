import{r as e,j as s}from"./react-vendor-CyNirxNk.js";import{u as t,c as a,s as l,a as n,b as c,d as r,e as i,f as d}from"./store-todosstore-DE5vS_Wk.js";function o(){const o=t(a),m=t(l),x=t(n),h=t(c),j=t(r),b=t(i),u=t(d),{addTodo:g,toggleTodo:N,removeTodo:p,updateTodoText:v,setTodoPriority:y,setFilter:f,toggleAll:C,clearCompleted:k,startEditing:w,stopEditing:T,toggleBulkSelectMode:S,toggleTodoSelection:E,toggleSelectAll:B,deleteSelected:P,markSelectedAsCompleted:A,sortByPriority:$,sortByCreatedTime:F,addBatchTodos:K,reset:D}=t(),[M,R]=e.useState(""),[q,z]=e.useState(""),[G,H]=e.useState(""),[I,J]=e.useState("normal"),[L,O]=e.useState("general"),Q=()=>{M.trim()&&(g({text:M.trim(),priority:I,category:L}),R(""))},U=e=>{w(e.id),z(e.text)},V=e=>{q.trim()&&v(e,q.trim()),T(),z("")},W=()=>{T(),z("")},X=e=>"btn "+(m===e?"btn-primary":"btn-outline"),Y=e=>{switch(e){case"high":return"var(--color-danger)";case"normal":return"var(--color-warning)";case"low":return"var(--color-success)";default:return"var(--color-secondary)"}},Z=e=>{switch(e){case"high":return"高";case"normal":return"中";case"low":return"低";default:return"未知"}};return s.jsxs("div",{className:"todos-management-page",children:[s.jsxs("div",{className:"card-header",children:[s.jsx("h1",{className:"card-title",children:"📝 Todos管理"}),s.jsx("p",{className:"card-description",children:"展示复杂的列表状态管理功能，包括过滤、排序、批量操作、编辑等"})]}),s.jsxs("div",{className:"card mb-lg",children:[s.jsx("h2",{className:"card-title",children:"📊 任务统计"}),s.jsxs("div",{className:"grid grid-4",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-primary",children:h.total}),s.jsx("div",{className:"text-sm text-secondary",children:"总任务"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-success",children:h.completed}),s.jsx("div",{className:"text-sm text-secondary",children:"已完成"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-warning",children:h.active}),s.jsx("div",{className:"text-sm text-secondary",children:"待完成"})]}),s.jsxs("div",{className:"text-center",children:[s.jsxs("div",{className:"text-2xl font-bold text-info",children:[h.completionRate,"%"]}),s.jsx("div",{className:"text-sm text-secondary",children:"完成率"})]})]})]}),s.jsxs("div",{className:"grid grid-2",children:[s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"card-title",children:"➕ 添加新任务"}),s.jsx("div",{className:"form-group",children:s.jsx("input",{type:"text",value:M,onChange:e=>R(e.target.value),onKeyPress:e=>"Enter"===e.key&&Q(),className:"form-control",placeholder:"输入任务内容"})}),s.jsxs("div",{className:"flex gap-sm mb-md",children:[s.jsxs("select",{value:I,onChange:e=>J(e.target.value),className:"form-control",children:[s.jsx("option",{value:"low",children:"低优先级"}),s.jsx("option",{value:"normal",children:"普通优先级"}),s.jsx("option",{value:"high",children:"高优先级"})]}),s.jsxs("select",{value:L,onChange:e=>O(e.target.value),className:"form-control",children:[s.jsx("option",{value:"general",children:"常规"}),s.jsx("option",{value:"work",children:"工作"}),s.jsx("option",{value:"personal",children:"个人"}),s.jsx("option",{value:"study",children:"学习"})]})]}),s.jsx("button",{onClick:Q,className:"btn btn-primary",children:"添加任务"})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"card-title",children:"📋 批量添加任务"}),s.jsx("div",{className:"form-group",children:s.jsx("textarea",{value:G,onChange:e=>H(e.target.value),className:"form-control",rows:"4",placeholder:"每行一个任务，支持批量添加"})}),s.jsx("button",{onClick:()=>{G.trim()&&(K(G),H(""))},className:"btn btn-success",children:"批量添加"})]})]}),s.jsxs("div",{className:"card mb-lg",children:[s.jsxs("div",{className:"flex justify-between items-center flex-wrap gap-md",children:[s.jsxs("div",{className:"flex gap-sm",children:[s.jsxs("button",{onClick:()=>f("all"),className:X("all"),children:["全部 (",h.total,")"]}),s.jsxs("button",{onClick:()=>f("active"),className:X("active"),children:["待完成 (",h.active,")"]}),s.jsxs("button",{onClick:()=>f("completed"),className:X("completed"),children:["已完成 (",h.completed,")"]})]}),s.jsxs("div",{className:"flex gap-sm flex-wrap",children:[s.jsx("button",{onClick:$,className:"btn btn-outline btn-sm",children:"按优先级排序"}),s.jsx("button",{onClick:()=>F(!0),className:"btn btn-outline btn-sm",children:"按时间排序"}),s.jsx("button",{onClick:S,className:"btn btn-sm "+(b?"btn-danger":"btn-secondary"),children:b?"退出批量选择":"批量选择"}),s.jsx("button",{onClick:C,className:"btn btn-info btn-sm",children:"全部切换"}),s.jsx("button",{onClick:k,className:"btn btn-warning btn-sm",children:"清除已完成"}),s.jsx("button",{onClick:D,className:"btn btn-danger btn-sm",children:"重置全部"})]})]}),b&&s.jsx("div",{className:"mt-md p-md bg-surface rounded",children:s.jsxs("div",{className:"flex justify-between items-center flex-wrap gap-md",children:[s.jsxs("div",{className:"flex items-center gap-md",children:[s.jsxs("span",{className:"text-sm",children:["已选择 ",u.length," 个任务"]}),s.jsx("button",{onClick:B,className:"btn btn-outline btn-sm",children:u.length===x.length?"取消全选":"全选"})]}),s.jsxs("div",{className:"flex gap-sm",children:[s.jsx("button",{onClick:()=>A(!0),className:"btn btn-success btn-sm",disabled:0===u.length,children:"标记为完成"}),s.jsx("button",{onClick:()=>A(!1),className:"btn btn-warning btn-sm",disabled:0===u.length,children:"标记为未完成"}),s.jsx("button",{onClick:P,className:"btn btn-danger btn-sm",disabled:0===u.length,children:"删除选中"})]})]})})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"card-title",children:"📋 任务列表"}),s.jsxs("div",{className:"space-y-2",children:[x.map(e=>s.jsxs("div",{className:`flex items-center gap-sm p-md border rounded transition-all ${e.completed?"opacity-60":""} ${u.includes(e.id)?"bg-primary":"bg-surface"}`,children:[b&&s.jsx("input",{type:"checkbox",checked:u.includes(e.id),onChange:()=>E(e.id),className:"form-control",style:{width:"auto"}}),s.jsx("input",{type:"checkbox",checked:e.completed,onChange:()=>N(e.id),className:"form-control",style:{width:"auto"}}),s.jsx("div",{className:"flex-1",children:j===e.id?s.jsxs("div",{className:"flex gap-sm",children:[s.jsx("input",{type:"text",value:q,onChange:e=>z(e.target.value),className:"form-control",autoFocus:!0,onKeyPress:s=>{"Enter"===s.key&&V(e.id),"Escape"===s.key&&W()}}),s.jsx("button",{onClick:()=>V(e.id),className:"btn btn-success btn-sm",children:"✓"}),s.jsx("button",{onClick:W,className:"btn btn-secondary btn-sm",children:"✗"})]}):s.jsxs("div",{children:[s.jsx("span",{className:e.completed?"line-through text-muted":"",onDoubleClick:()=>U(e),children:e.text}),s.jsxs("div",{className:"flex gap-xs mt-xs",children:[s.jsx("span",{className:"badge",style:{backgroundColor:Y(e.priority)},children:Z(e.priority)}),s.jsx("span",{className:"badge badge-secondary",children:e.category}),e.tags&&e.tags.map(e=>s.jsxs("span",{className:"badge",style:{backgroundColor:"var(--color-info)"},children:["#",e]},e))]})]})}),j!==e.id&&s.jsxs("div",{className:"flex gap-xs",children:[s.jsxs("select",{value:e.priority,onChange:s=>y(e.id,s.target.value),className:"form-control",style:{width:"auto"},children:[s.jsx("option",{value:"low",children:"低"}),s.jsx("option",{value:"normal",children:"中"}),s.jsx("option",{value:"high",children:"高"})]}),s.jsx("button",{onClick:()=>U(e),className:"btn btn-primary btn-sm",children:"✏️"}),s.jsx("button",{onClick:()=>p(e.id),className:"btn btn-danger btn-sm",children:"🗑️"})]})]},e.id)),0===x.length&&s.jsx("div",{className:"text-center text-muted py-xl",children:"all"===m?"暂无任务，请添加一些任务":`暂无${"active"===m?"待完成":"已完成"}的任务`})]})]}),h.categories&&h.categories.length>0&&s.jsxs("div",{className:"card mt-lg",children:[s.jsx("h2",{className:"card-title",children:"📊 分类统计"}),s.jsx("div",{className:"grid grid-4",children:h.categories.map(e=>s.jsxs("div",{className:"text-center p-md border rounded",children:[s.jsx("div",{className:"font-semibold",children:e}),s.jsxs("div",{className:"text-sm text-secondary",children:[o.filter(s=>s.category===e).length," 个任务"]})]},e))})]})]})}export{o as T};
//# sourceMappingURL=page-todosmanagement-DW3Rbh2M.js.map
