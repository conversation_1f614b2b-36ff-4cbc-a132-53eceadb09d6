{"version": 3, "file": "page-todosmanagement-DW3Rbh2M.js", "sources": ["../../src/pages/TodosManagement.jsx"], "sourcesContent": ["import React, { useState } from 'react'\r\nimport { useTodosStore, selectTodos, selectFilter, selectFilteredTodos, selectTodoStats, selectEditingId, selectBulkSelectMode, selectSelectedIds } from '@/stores/todosStore'\r\n\r\nfunction TodosManagement() {\r\n  // 使用Zustand获取状态\r\n  const todos = useTodosStore(selectTodos)\r\n  const filter = useTodosStore(selectFilter)\r\n  const filteredTodos = useTodosStore(selectFilteredTodos)\r\n  const todoStats = useTodosStore(selectTodoStats)\r\n  const editingId = useTodosStore(selectEditingId)\r\n  const bulkSelectMode = useTodosStore(selectBulkSelectMode)\r\n  const selectedIds = useTodosStore(selectSelectedIds)\r\n  \r\n  // 获取actions\r\n  const {\r\n    addTodo,\r\n    toggleTodo,\r\n    removeTodo,\r\n    updateTodoText,\r\n    setTodoPriority,\r\n    setFilter,\r\n    toggleAll,\r\n    clearCompleted,\r\n    startEditing,\r\n    stopEditing,\r\n    toggleBulkSelectMode,\r\n    toggleTodoSelection,\r\n    toggleSelectAll,\r\n    deleteSelected,\r\n    markSelectedAsCompleted,\r\n    sortByPriority,\r\n    sortByCreatedTime,\r\n    addBatchTodos,\r\n    reset\r\n  } = useTodosStore()\r\n  \r\n  // 本地状态\r\n  const [newTodoText, setNewTodoText] = useState('')\r\n  const [editingText, setEditingText] = useState('')\r\n  const [batchTodoText, setBatchTodoText] = useState('')\r\n  const [newTodoPriority, setNewTodoPriority] = useState('normal')\r\n  const [newTodoCategory, setNewTodoCategory] = useState('general')\r\n\r\n  // 添加Todo\r\n  const handleAddTodo = () => {\r\n    if (newTodoText.trim()) {\r\n      addTodo({\r\n        text: newTodoText.trim(),\r\n        priority: newTodoPriority,\r\n        category: newTodoCategory\r\n      })\r\n      setNewTodoText('')\r\n    }\r\n  }\r\n\r\n  // 批量添加Todos\r\n  const handleBatchAdd = () => {\r\n    if (batchTodoText.trim()) {\r\n      addBatchTodos(batchTodoText)\r\n      setBatchTodoText('')\r\n    }\r\n  }\r\n\r\n  // 开始编辑\r\n  const handleStartEdit = (todo) => {\r\n    startEditing(todo.id)\r\n    setEditingText(todo.text)\r\n  }\r\n\r\n  // 完成编辑\r\n  const handleFinishEdit = (id) => {\r\n    if (editingText.trim()) {\r\n      updateTodoText(id, editingText.trim())\r\n    }\r\n    stopEditing()\r\n    setEditingText('')\r\n  }\r\n\r\n  // 取消编辑\r\n  const handleCancelEdit = () => {\r\n    stopEditing()\r\n    setEditingText('')\r\n  }\r\n\r\n  // 过滤器按钮样式\r\n  const getFilterButtonClass = (filterType) => {\r\n    return `btn ${filter === filterType ? 'btn-primary' : 'btn-outline'}`\r\n  }\r\n\r\n  // 优先级颜色\r\n  const getPriorityColor = (priority) => {\r\n    switch (priority) {\r\n      case 'high': return 'var(--color-danger)'\r\n      case 'normal': return 'var(--color-warning)'\r\n      case 'low': return 'var(--color-success)'\r\n      default: return 'var(--color-secondary)'\r\n    }\r\n  }\r\n\r\n  // 优先级标签\r\n  const getPriorityLabel = (priority) => {\r\n    switch (priority) {\r\n      case 'high': return '高'\r\n      case 'normal': return '中'\r\n      case 'low': return '低'\r\n      default: return '未知'\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"todos-management-page\">\r\n      <div className=\"card-header\">\r\n        <h1 className=\"card-title\">📝 Todos管理</h1>\r\n        <p className=\"card-description\">\r\n          展示复杂的列表状态管理功能，包括过滤、排序、批量操作、编辑等\r\n        </p>\r\n      </div>\r\n\r\n      {/* 统计信息 */}\r\n      <div className=\"card mb-lg\">\r\n        <h2 className=\"card-title\">📊 任务统计</h2>\r\n        <div className=\"grid grid-4\">\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-primary\">{todoStats.total}</div>\r\n            <div className=\"text-sm text-secondary\">总任务</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-success\">{todoStats.completed}</div>\r\n            <div className=\"text-sm text-secondary\">已完成</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-warning\">{todoStats.active}</div>\r\n            <div className=\"text-sm text-secondary\">待完成</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-2xl font-bold text-info\">{todoStats.completionRate}%</div>\r\n            <div className=\"text-sm text-secondary\">完成率</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-2\">\r\n        {/* 添加新任务 */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">➕ 添加新任务</h2>\r\n          \r\n          <div className=\"form-group\">\r\n            <input\r\n              type=\"text\"\r\n              value={newTodoText}\r\n              onChange={(e) => setNewTodoText(e.target.value)}\r\n              onKeyPress={(e) => e.key === 'Enter' && handleAddTodo()}\r\n              className=\"form-control\"\r\n              placeholder=\"输入任务内容\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex gap-sm mb-md\">\r\n            <select\r\n              value={newTodoPriority}\r\n              onChange={(e) => setNewTodoPriority(e.target.value)}\r\n              className=\"form-control\"\r\n            >\r\n              <option value=\"low\">低优先级</option>\r\n              <option value=\"normal\">普通优先级</option>\r\n              <option value=\"high\">高优先级</option>\r\n            </select>\r\n            \r\n            <select\r\n              value={newTodoCategory}\r\n              onChange={(e) => setNewTodoCategory(e.target.value)}\r\n              className=\"form-control\"\r\n            >\r\n              <option value=\"general\">常规</option>\r\n              <option value=\"work\">工作</option>\r\n              <option value=\"personal\">个人</option>\r\n              <option value=\"study\">学习</option>\r\n            </select>\r\n          </div>\r\n\r\n          <button onClick={handleAddTodo} className=\"btn btn-primary\">\r\n            添加任务\r\n          </button>\r\n        </div>\r\n\r\n        {/* 批量添加 */}\r\n        <div className=\"card\">\r\n          <h2 className=\"card-title\">📋 批量添加任务</h2>\r\n          \r\n          <div className=\"form-group\">\r\n            <textarea\r\n              value={batchTodoText}\r\n              onChange={(e) => setBatchTodoText(e.target.value)}\r\n              className=\"form-control\"\r\n              rows=\"4\"\r\n              placeholder=\"每行一个任务，支持批量添加\"\r\n            />\r\n          </div>\r\n\r\n          <button onClick={handleBatchAdd} className=\"btn btn-success\">\r\n            批量添加\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 操作工具栏 */}\r\n      <div className=\"card mb-lg\">\r\n        <div className=\"flex justify-between items-center flex-wrap gap-md\">\r\n          {/* 过滤器 */}\r\n          <div className=\"flex gap-sm\">\r\n            <button \r\n              onClick={() => setFilter('all')} \r\n              className={getFilterButtonClass('all')}\r\n            >\r\n              全部 ({todoStats.total})\r\n            </button>\r\n            <button \r\n              onClick={() => setFilter('active')} \r\n              className={getFilterButtonClass('active')}\r\n            >\r\n              待完成 ({todoStats.active})\r\n            </button>\r\n            <button \r\n              onClick={() => setFilter('completed')} \r\n              className={getFilterButtonClass('completed')}\r\n            >\r\n              已完成 ({todoStats.completed})\r\n            </button>\r\n          </div>\r\n\r\n          {/* 排序和操作按钮 */}\r\n          <div className=\"flex gap-sm flex-wrap\">\r\n            <button onClick={sortByPriority} className=\"btn btn-outline btn-sm\">\r\n              按优先级排序\r\n            </button>\r\n            <button onClick={() => sortByCreatedTime(true)} className=\"btn btn-outline btn-sm\">\r\n              按时间排序\r\n            </button>\r\n            <button onClick={toggleBulkSelectMode} className={`btn btn-sm ${bulkSelectMode ? 'btn-danger' : 'btn-secondary'}`}>\r\n              {bulkSelectMode ? '退出批量选择' : '批量选择'}\r\n            </button>\r\n            <button onClick={toggleAll} className=\"btn btn-info btn-sm\">\r\n              全部切换\r\n            </button>\r\n            <button onClick={clearCompleted} className=\"btn btn-warning btn-sm\">\r\n              清除已完成\r\n            </button>\r\n            <button onClick={reset} className=\"btn btn-danger btn-sm\">\r\n              重置全部\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 批量操作面板 */}\r\n        {bulkSelectMode && (\r\n          <div className=\"mt-md p-md bg-surface rounded\">\r\n            <div className=\"flex justify-between items-center flex-wrap gap-md\">\r\n              <div className=\"flex items-center gap-md\">\r\n                <span className=\"text-sm\">\r\n                  已选择 {selectedIds.length} 个任务\r\n                </span>\r\n                <button onClick={toggleSelectAll} className=\"btn btn-outline btn-sm\">\r\n                  {selectedIds.length === filteredTodos.length ? '取消全选' : '全选'}\r\n                </button>\r\n              </div>\r\n              \r\n              <div className=\"flex gap-sm\">\r\n                <button \r\n                  onClick={() => markSelectedAsCompleted(true)} \r\n                  className=\"btn btn-success btn-sm\"\r\n                  disabled={selectedIds.length === 0}\r\n                >\r\n                  标记为完成\r\n                </button>\r\n                <button \r\n                  onClick={() => markSelectedAsCompleted(false)} \r\n                  className=\"btn btn-warning btn-sm\"\r\n                  disabled={selectedIds.length === 0}\r\n                >\r\n                  标记为未完成\r\n                </button>\r\n                <button \r\n                  onClick={deleteSelected} \r\n                  className=\"btn btn-danger btn-sm\"\r\n                  disabled={selectedIds.length === 0}\r\n                >\r\n                  删除选中\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* 任务列表 */}\r\n      <div className=\"card\">\r\n        <h2 className=\"card-title\">📋 任务列表</h2>\r\n        \r\n        <div className=\"space-y-2\">\r\n          {filteredTodos.map((todo) => (\r\n            <div \r\n              key={todo.id} \r\n              className={`flex items-center gap-sm p-md border rounded transition-all ${\r\n                todo.completed ? 'opacity-60' : ''\r\n              } ${selectedIds.includes(todo.id) ? 'bg-primary' : 'bg-surface'}`}\r\n            >\r\n              {/* 批量选择复选框 */}\r\n              {bulkSelectMode && (\r\n                <input\r\n                  type=\"checkbox\"\r\n                  checked={selectedIds.includes(todo.id)}\r\n                  onChange={() => toggleTodoSelection(todo.id)}\r\n                  className=\"form-control\"\r\n                  style={{ width: 'auto' }}\r\n                />\r\n              )}\r\n\r\n              {/* 完成状态复选框 */}\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={todo.completed}\r\n                onChange={() => toggleTodo(todo.id)}\r\n                className=\"form-control\"\r\n                style={{ width: 'auto' }}\r\n              />\r\n\r\n              {/* 任务内容 */}\r\n              <div className=\"flex-1\">\r\n                {editingId === todo.id ? (\r\n                  <div className=\"flex gap-sm\">\r\n                    <input\r\n                      type=\"text\"\r\n                      value={editingText}\r\n                      onChange={(e) => setEditingText(e.target.value)}\r\n                      className=\"form-control\"\r\n                      autoFocus\r\n                      onKeyPress={(e) => {\r\n                        if (e.key === 'Enter') handleFinishEdit(todo.id)\r\n                        if (e.key === 'Escape') handleCancelEdit()\r\n                      }}\r\n                    />\r\n                    <button onClick={() => handleFinishEdit(todo.id)} className=\"btn btn-success btn-sm\">\r\n                      ✓\r\n                    </button>\r\n                    <button onClick={handleCancelEdit} className=\"btn btn-secondary btn-sm\">\r\n                      ✗\r\n                    </button>\r\n                  </div>\r\n                ) : (\r\n                  <div>\r\n                    <span \r\n                      className={todo.completed ? 'line-through text-muted' : ''}\r\n                      onDoubleClick={() => handleStartEdit(todo)}\r\n                    >\r\n                      {todo.text}\r\n                    </span>\r\n                    <div className=\"flex gap-xs mt-xs\">\r\n                      <span \r\n                        className=\"badge\" \r\n                        style={{ backgroundColor: getPriorityColor(todo.priority) }}\r\n                      >\r\n                        {getPriorityLabel(todo.priority)}\r\n                      </span>\r\n                      <span className=\"badge badge-secondary\">\r\n                        {todo.category}\r\n                      </span>\r\n                      {todo.tags && todo.tags.map(tag => (\r\n                        <span key={tag} className=\"badge\" style={{ backgroundColor: 'var(--color-info)' }}>\r\n                          #{tag}\r\n                        </span>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* 操作按钮 */}\r\n              {editingId !== todo.id && (\r\n                <div className=\"flex gap-xs\">\r\n                  <select\r\n                    value={todo.priority}\r\n                    onChange={(e) => setTodoPriority(todo.id, e.target.value)}\r\n                    className=\"form-control\"\r\n                    style={{ width: 'auto' }}\r\n                  >\r\n                    <option value=\"low\">低</option>\r\n                    <option value=\"normal\">中</option>\r\n                    <option value=\"high\">高</option>\r\n                  </select>\r\n                  \r\n                  <button \r\n                    onClick={() => handleStartEdit(todo)} \r\n                    className=\"btn btn-primary btn-sm\"\r\n                  >\r\n                    ✏️\r\n                  </button>\r\n                  \r\n                  <button \r\n                    onClick={() => removeTodo(todo.id)} \r\n                    className=\"btn btn-danger btn-sm\"\r\n                  >\r\n                    🗑️\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ))}\r\n          \r\n          {filteredTodos.length === 0 && (\r\n            <div className=\"text-center text-muted py-xl\">\r\n              {filter === 'all' ? '暂无任务，请添加一些任务' : `暂无${filter === 'active' ? '待完成' : '已完成'}的任务`}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 分类统计 */}\r\n      {todoStats.categories && todoStats.categories.length > 0 && (\r\n        <div className=\"card mt-lg\">\r\n          <h2 className=\"card-title\">📊 分类统计</h2>\r\n          <div className=\"grid grid-4\">\r\n            {todoStats.categories.map(category => (\r\n              <div key={category} className=\"text-center p-md border rounded\">\r\n                <div className=\"font-semibold\">{category}</div>\r\n                <div className=\"text-sm text-secondary\">\r\n                  {todos.filter(todo => todo.category === category).length} 个任务\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default TodosManagement "], "names": ["TodosManagement", "todos", "useTodosStore", "selectTodos", "filter", "selectFilter", "filteredTodos", "selectFilteredTodos", "todoStats", "selectTodoStats", "editingId", "selectEditingId", "bulkSelectMode", "selectBulkSelectMode", "selectedIds", "selectSelectedIds", "addTodo", "toggleTodo", "removeTodo", "updateTodoText", "setTodoPriority", "setFilter", "toggleAll", "clearCompleted", "startEditing", "stopEditing", "toggleBulkSelectMode", "toggleTodoSelection", "toggleSelectAll", "deleteSelected", "markSelectedAsCompleted", "sortByPriority", "sortByCreatedTime", "addBatchTodos", "reset", "newTodoText", "setNewTodoText", "useState", "editingText", "setEditingText", "batchTodoText", "setBatchTodoText", "newTodoPriority", "setNewTodoPriority", "newTodoCategory", "setNewTodoCategory", "handleAddTodo", "trim", "text", "priority", "category", "handleStartEdit", "todo", "id", "handleFinishEdit", "handleCancelEdit", "getFilterButtonClass", "filterType", "getPriorityColor", "getPriorityLabel", "jsxs", "className", "children", "jsx", "total", "completed", "active", "completionRate", "type", "value", "onChange", "e", "target", "onKeyPress", "key", "placeholder", "onClick", "rows", "length", "disabled", "map", "includes", "checked", "style", "width", "autoFocus", "onDoubleClick", "backgroundColor", "tags", "tag", "categories"], "mappings": "0JAGA,SAASA,IAEP,MAAMC,EAAQC,EAAcC,GACtBC,EAASF,EAAcG,GACvBC,EAAgBJ,EAAcK,GAC9BC,EAAYN,EAAcO,GAC1BC,EAAYR,EAAcS,GAC1BC,EAAiBV,EAAcW,GAC/BC,EAAcZ,EAAca,IAG5BC,QACJA,EAAAC,WACAA,EAAAC,WACAA,EAAAC,eACAA,EAAAC,gBACAA,EAAAC,UACAA,EAAAC,UACAA,EAAAC,eACAA,EAAAC,aACAA,EAAAC,YACAA,EAAAC,qBACAA,EAAAC,oBACAA,EAAAC,gBACAA,EAAAC,eACAA,EAAAC,wBACAA,EAAAC,eACAA,EAAAC,kBACAA,EAAAC,cACAA,EAAAC,MACAA,GACEhC,KAGGiC,EAAaC,GAAkBC,EAAAA,SAAS,KACxCC,EAAaC,GAAkBF,EAAAA,SAAS,KACxCG,EAAeC,GAAoBJ,EAAAA,SAAS,KAC5CK,EAAiBC,GAAsBN,EAAAA,SAAS,WAChDO,EAAiBC,GAAsBR,EAAAA,SAAS,WAGjDS,EAAgB,KAChBX,EAAYY,SACd/B,EAAQ,CACNgC,KAAMb,EAAYY,OAClBE,SAAUP,EACVQ,SAAUN,IAEZR,EAAe,MAabe,EAAmBC,IACvB5B,EAAa4B,EAAKC,IAClBd,EAAea,EAAKJ,OAIhBM,EAAoBD,IACpBf,EAAYS,QACd5B,EAAekC,EAAIf,EAAYS,QAEjCtB,IACAc,EAAe,KAIXgB,EAAmB,KACvB9B,IACAc,EAAe,KAIXiB,EAAwBC,GACrB,QAAOrD,IAAWqD,EAAa,cAAgB,eAIlDC,EAAoBT,IACxB,OAAQA,GACN,IAAK,OAAQ,MAAO,sBACpB,IAAK,SAAU,MAAO,uBACtB,IAAK,MAAO,MAAO,uBACnB,QAAS,MAAO,2BAKdU,EAAoBV,IACxB,OAAQA,GACN,IAAK,OAAQ,MAAO,IACpB,IAAK,SAAU,MAAO,IACtB,IAAK,MAAO,MAAO,IACnB,QAAS,MAAO,OAIpB,SACEW,KAAC,MAAA,CAAIC,UAAU,wBACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,eAC3BC,EAAAA,IAAC,IAAA,CAAEF,UAAU,mBAAmBC,SAAA,wCAMlCF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAC3BF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,kCAAmCC,SAAAtD,EAAUwD,QAC5DD,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,aAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,kCAAmCC,SAAAtD,EAAUyD,YAC5DF,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,aAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,kCAAmCC,SAAAtD,EAAU0D,SAC5DH,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,aAE1CF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,+BAAgCC,SAAA,CAAAtD,EAAU2D,eAAe,OACxEJ,EAAAA,IAAC,MAAA,CAAIF,UAAU,yBAAyBC,SAAA,mBAK9CF,KAAC,MAAA,CAAIC,UAAU,cAEbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAE3BC,IAAC,MAAA,CAAIF,UAAU,aACbC,SAAAC,EAAAA,IAAC,QAAA,CACCK,KAAK,OACLC,MAAOlC,EACPmC,SAAWC,GAAMnC,EAAemC,EAAEC,OAAOH,OACzCI,WAAaF,GAAgB,UAAVA,EAAEG,KAAmB5B,IACxCe,UAAU,eACVc,YAAY,eAIhBf,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAF,EAAAA,KAAC,SAAA,CACCS,MAAO3B,EACP4B,SAAWC,GAAM5B,EAAmB4B,EAAEC,OAAOH,OAC7CR,UAAU,eAEVC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CAAOM,MAAM,MAAMP,SAAA,SACpBC,EAAAA,IAAC,SAAA,CAAOM,MAAM,SAASP,SAAA,UACvBC,EAAAA,IAAC,SAAA,CAAOM,MAAM,OAAOP,SAAA,YAGvBF,EAAAA,KAAC,SAAA,CACCS,MAAOzB,EACP0B,SAAWC,GAAM1B,EAAmB0B,EAAEC,OAAOH,OAC7CR,UAAU,eAEVC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CAAOM,MAAM,UAAUP,SAAA,OACxBC,EAAAA,IAAC,SAAA,CAAOM,MAAM,OAAOP,SAAA,OACrBC,EAAAA,IAAC,SAAA,CAAOM,MAAM,WAAWP,SAAA,OACzBC,EAAAA,IAAC,SAAA,CAAOM,MAAM,QAAQP,SAAA,mBAIzB,SAAA,CAAOc,QAAS9B,EAAee,UAAU,kBAAkBC,SAAA,cAM9DF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,gBAE3BC,IAAC,MAAA,CAAIF,UAAU,aACbC,SAAAC,EAAAA,IAAC,WAAA,CACCM,MAAO7B,EACP8B,SAAWC,GAAM9B,EAAiB8B,EAAEC,OAAOH,OAC3CR,UAAU,eACVgB,KAAK,IACLF,YAAY,0BAIf,SAAA,CAAOC,QA/IO,KACjBpC,EAAcO,SAChBd,EAAcO,GACdC,EAAiB,MA4IoBoB,UAAU,kBAAkBC,SAAA,iBAOjEF,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,qDAEbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,EAAAA,KAAC,SAAA,CACCgB,QAAS,IAAMvD,EAAU,OACzBwC,UAAWL,EAAqB,OACjCM,SAAA,CAAA,OACMtD,EAAUwD,MAAM,OAEvBJ,EAAAA,KAAC,SAAA,CACCgB,QAAS,IAAMvD,EAAU,UACzBwC,UAAWL,EAAqB,UACjCM,SAAA,CAAA,QACOtD,EAAU0D,OAAO,OAEzBN,EAAAA,KAAC,SAAA,CACCgB,QAAS,IAAMvD,EAAU,aACzBwC,UAAWL,EAAqB,aACjCM,SAAA,CAAA,QACOtD,EAAUyD,UAAU,YAK9BL,KAAC,MAAA,CAAIC,UAAU,wBACbC,SAAA,CAAAC,MAAC,SAAA,CAAOa,QAAS7C,EAAgB8B,UAAU,yBAAyBC,SAAA,WAGpEC,EAAAA,IAAC,UAAOa,QAAS,IAAM5C,GAAkB,GAAO6B,UAAU,yBAAyBC,SAAA,YAGnFC,IAAC,SAAA,CAAOa,QAASlD,EAAsBmC,UAAW,eAAcjD,EAAiB,aAAe,iBAC7FkD,SAAAlD,EAAiB,SAAW,eAE9B,SAAA,CAAOgE,QAAStD,EAAWuC,UAAU,sBAAsBC,SAAA,eAG3D,SAAA,CAAOc,QAASrD,EAAgBsC,UAAU,yBAAyBC,SAAA,gBAGnE,SAAA,CAAOc,QAAS1C,EAAO2B,UAAU,wBAAwBC,SAAA,eAO7DlD,SACE,MAAA,CAAIiD,UAAU,gCACbC,SAAAF,EAAAA,KAAC,MAAA,CAAIC,UAAU,qDACbC,SAAA,GAAAF,KAAC,MAAA,CAAIC,UAAU,2BACbC,SAAA,GAAAF,KAAC,OAAA,CAAKC,UAAU,UAAUC,SAAA,CAAA,OACnBhD,EAAYgE,OAAO,UAE1Bf,EAAAA,IAAC,SAAA,CAAOa,QAAShD,EAAiBiC,UAAU,yBACzCC,SAAAhD,EAAYgE,SAAWxE,EAAcwE,OAAS,OAAS,YAI5DlB,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,SAAA,CACCa,QAAS,IAAM9C,GAAwB,GACvC+B,UAAU,yBACVkB,SAAiC,IAAvBjE,EAAYgE,OACvBhB,SAAA,UAGDC,EAAAA,IAAC,SAAA,CACCa,QAAS,IAAM9C,GAAwB,GACvC+B,UAAU,yBACVkB,SAAiC,IAAvBjE,EAAYgE,OACvBhB,SAAA,WAGDC,EAAAA,IAAC,SAAA,CACCa,QAAS/C,EACTgC,UAAU,wBACVkB,SAAiC,IAAvBjE,EAAYgE,OACvBhB,SAAA,sBAUXF,KAAC,MAAA,CAAIC,UAAU,OACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAE3BF,KAAC,MAAA,CAAIC,UAAU,YACZC,SAAA,CAAAxD,EAAc0E,IAAK5B,GAClBQ,EAAAA,KAAC,MAAA,CAECC,UAAW,+DACTT,EAAKa,UAAY,aAAe,MAC9BnD,EAAYmE,SAAS7B,EAAKC,IAAM,aAAe,eAGlDS,SAAA,CAAAlD,GACCmD,EAAAA,IAAC,QAAA,CACCK,KAAK,WACLc,QAASpE,EAAYmE,SAAS7B,EAAKC,IACnCiB,SAAU,IAAM3C,EAAoByB,EAAKC,IACzCQ,UAAU,eACVsB,MAAO,CAAEC,MAAO,UAKpBrB,EAAAA,IAAC,QAAA,CACCK,KAAK,WACLc,QAAS9B,EAAKa,UACdK,SAAU,IAAMrD,EAAWmC,EAAKC,IAChCQ,UAAU,eACVsB,MAAO,CAAEC,MAAO,YAIlBrB,IAAC,MAAA,CAAIF,UAAU,SACZC,SAAApD,IAAc0C,EAAKC,KAClBO,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAC,EAAAA,IAAC,QAAA,CACCK,KAAK,OACLC,MAAO/B,EACPgC,SAAWC,GAAMhC,EAAegC,EAAEC,OAAOH,OACzCR,UAAU,eACVwB,WAAS,EACTZ,WAAaF,IACG,UAAVA,EAAEG,KAAiBpB,EAAiBF,EAAKC,IAC/B,WAAVkB,EAAEG,KAAkBnB,OAG5BQ,EAAAA,IAAC,SAAA,CAAOa,QAAS,IAAMtB,EAAiBF,EAAKC,IAAKQ,UAAU,yBAAyBC,SAAA,YAGpF,SAAA,CAAOc,QAASrB,EAAkBM,UAAU,2BAA2BC,SAAA,gBAKzE,MAAA,CACCA,SAAA,CAAAC,EAAAA,IAAC,OAAA,CACCF,UAAWT,EAAKa,UAAY,0BAA4B,GACxDqB,cAAe,IAAMnC,EAAgBC,GAEpCU,SAAAV,EAAKJ,SAERY,KAAC,MAAA,CAAIC,UAAU,oBACbC,SAAA,CAAAC,EAAAA,IAAC,OAAA,CACCF,UAAU,QACVsB,MAAO,CAAEI,gBAAiB7B,EAAiBN,EAAKH,WAE/Ca,SAAAH,EAAiBP,EAAKH,YAEzBc,EAAAA,IAAC,OAAA,CAAKF,UAAU,wBACbC,WAAKZ,WAEPE,EAAKoC,MAAQpC,EAAKoC,KAAKR,IAAIS,GAC1B7B,EAAAA,KAAC,OAAA,CAAeC,UAAU,QAAQsB,MAAO,CAAEI,gBAAiB,qBAAuBzB,SAAA,CAAA,IAC/E2B,IADOA,YAUpB/E,IAAc0C,EAAKC,IAClBO,EAAAA,KAAC,MAAA,CAAIC,UAAU,cACbC,SAAA,CAAAF,EAAAA,KAAC,SAAA,CACCS,MAAOjB,EAAKH,SACZqB,SAAWC,GAAMnD,EAAgBgC,EAAKC,GAAIkB,EAAEC,OAAOH,OACnDR,UAAU,eACVsB,MAAO,CAAEC,MAAO,QAEhBtB,SAAA,CAAAC,EAAAA,IAAC,SAAA,CAAOM,MAAM,MAAMP,SAAA,MACpBC,EAAAA,IAAC,SAAA,CAAOM,MAAM,SAASP,SAAA,MACvBC,EAAAA,IAAC,SAAA,CAAOM,MAAM,OAAOP,SAAA,SAGvBC,EAAAA,IAAC,SAAA,CACCa,QAAS,IAAMzB,EAAgBC,GAC/BS,UAAU,yBACXC,SAAA,OAIDC,EAAAA,IAAC,SAAA,CACCa,QAAS,IAAM1D,EAAWkC,EAAKC,IAC/BQ,UAAU,wBACXC,SAAA,aAnGAV,EAAKC,KA2GY,IAAzB/C,EAAcwE,UACbf,IAAC,OAAIF,UAAU,+BACZC,SAAW,QAAX1D,EAAmB,eAAiB,KAAgB,WAAXA,EAAsB,MAAQ,mBAO/EI,EAAUkF,YAAclF,EAAUkF,WAAWZ,OAAS,GACrDlB,EAAAA,KAAC,MAAA,CAAIC,UAAU,aACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,aAAaC,SAAA,cAC3BC,IAAC,MAAA,CAAIF,UAAU,cACZC,SAAAtD,EAAUkF,WAAWV,IAAI9B,GACxBU,EAAAA,KAAC,MAAA,CAAmBC,UAAU,kCAC5BC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIF,UAAU,gBAAiBC,SAAAZ,MAChCU,KAAC,MAAA,CAAIC,UAAU,yBACZC,SAAA,CAAA7D,EAAMG,OAAOgD,GAAQA,EAAKF,WAAaA,GAAU4B,OAAO,YAHnD5B,WAYxB"}