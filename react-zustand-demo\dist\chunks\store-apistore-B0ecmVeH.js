import{a as e,c as s,d as t,s as r,i as a}from"./react-vendor-CyNirxNk.js";const o=e.create({baseURL:"https://jsonplaceholder.typicode.com",timeout:1e4});o.interceptors.request.use(e=>e,e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>Promise.reject(e));const u=s(t(r(a((e,s)=>({users:[],currentUser:null,posts:[],comments:[],isLoading:!1,loadingStates:{},errors:{},requestLogs:[],cache:{},addRequestLog:s=>e(e=>{e.requestLogs.unshift({id:Date.now(),timestamp:(new Date).toISOString(),...s}),e.requestLogs.length>100&&(e.requestLogs=e.requestLogs.slice(0,100))}),setLoading:(s,t)=>e(e=>{e.loadingStates[s]=t,e.isLoading=Object.values(e.loadingStates).some(<PERSON><PERSON>an)}),setError:(s,t)=>e(e=>{t?e.errors[s]=t:delete e.errors[s]}),fetchUsers:async()=>{const t="users",r=s().cache[t];if(r&&Date.now()-r.timestamp<3e5)return e(e=>{e.users=r.data}),s().addRequestLog({method:"GET",url:"/users",status:"cached",message:"从缓存获取用户列表"}),r.data;s().setLoading("fetchUsers",!0),s().setError("fetchUsers",null);try{const r=await o.get("/users"),a=r.data;return e(e=>{e.users=a,e.cache[t]={data:a,timestamp:Date.now()}}),s().addRequestLog({method:"GET",url:"/users",status:r.status,message:`成功获取${a.length}个用户`}),a}catch(a){const e=a.response?.data?.message||a.message||"获取用户列表失败";throw s().setError("fetchUsers",e),s().addRequestLog({method:"GET",url:"/users",status:a.response?.status||"error",message:e}),a}finally{s().setLoading("fetchUsers",!1)}},fetchUserById:async t=>{const r=`user-${t}`,a=s().cache[r];if(a&&Date.now()-a.timestamp<3e5)return e(e=>{e.currentUser=a.data}),s().addRequestLog({method:"GET",url:`/users/${t}`,status:"cached",message:"从缓存获取用户详情"}),a.data;s().setLoading("fetchUser",!0),s().setError("fetchUser",null);try{const a=await o.get(`/users/${t}`),u=a.data;return e(e=>{e.currentUser=u,e.cache[r]={data:u,timestamp:Date.now()}}),s().addRequestLog({method:"GET",url:`/users/${t}`,status:a.status,message:`成功获取用户: ${u.name}`}),u}catch(u){const e=404===u.response?.status?"用户不存在":u.response?.data?.message||u.message||"获取用户详情失败";throw s().setError("fetchUser",e),s().addRequestLog({method:"GET",url:`/users/${t}`,status:u.response?.status||"error",message:e}),u}finally{s().setLoading("fetchUser",!1)}},createUser:async t=>{s().setLoading("createUser",!0),s().setError("createUser",null);try{const r=await o.post("/users",t),a={...r.data,id:Date.now()};return e(e=>{e.users.unshift(a)}),s().addRequestLog({method:"POST",url:"/users",status:r.status,message:`成功创建用户: ${a.name}`}),a}catch(r){const e=r.response?.data?.message||r.message||"创建用户失败";throw s().setError("createUser",e),s().addRequestLog({method:"POST",url:"/users",status:r.response?.status||"error",message:e}),r}finally{s().setLoading("createUser",!1)}},updateUser:async(t,r)=>{s().setLoading("updateUser",!0),s().setError("updateUser",null);try{const a=await o.put(`/users/${t}`,r),u=a.data;return e(e=>{const s=e.users.findIndex(e=>e.id===t);s>-1&&(e.users[s]={...e.users[s],...u}),e.currentUser&&e.currentUser.id===t&&(e.currentUser={...e.currentUser,...u}),delete e.cache[`user-${t}`]}),s().addRequestLog({method:"PUT",url:`/users/${t}`,status:a.status,message:`成功更新用户: ${u.name||t}`}),u}catch(a){const e=a.response?.data?.message||a.message||"更新用户失败";throw s().setError("updateUser",e),s().addRequestLog({method:"PUT",url:`/users/${t}`,status:a.response?.status||"error",message:e}),a}finally{s().setLoading("updateUser",!1)}},deleteUser:async t=>{s().setLoading("deleteUser",!0),s().setError("deleteUser",null);try{const r=await o.delete(`/users/${t}`);return e(e=>{e.users=e.users.filter(e=>e.id!==t),e.currentUser&&e.currentUser.id===t&&(e.currentUser=null),delete e.cache[`user-${t}`]}),s().addRequestLog({method:"DELETE",url:`/users/${t}`,status:r.status,message:`成功删除用户: ${t}`}),!0}catch(r){const e=r.response?.data?.message||r.message||"删除用户失败";throw s().setError("deleteUser",e),s().addRequestLog({method:"DELETE",url:`/users/${t}`,status:r.response?.status||"error",message:e}),r}finally{s().setLoading("deleteUser",!1)}},fetchPosts:async(t=null)=>{const r=t?`/users/${t}/posts`:"/posts",a=t?`posts-user-${t}`:"posts",u=s().cache[a];if(u&&Date.now()-u.timestamp<3e5)return e(e=>{e.posts=u.data}),s().addRequestLog({method:"GET",url:r,status:"cached",message:"从缓存获取文章列表"}),u.data;s().setLoading("fetchPosts",!0),s().setError("fetchPosts",null);try{const t=await o.get(r),u=t.data;return e(e=>{e.posts=u,e.cache[a]={data:u,timestamp:Date.now()}}),s().addRequestLog({method:"GET",url:r,status:t.status,message:`成功获取${u.length}篇文章`}),u}catch(n){const e=n.response?.data?.message||n.message||"获取文章列表失败";throw s().setError("fetchPosts",e),s().addRequestLog({method:"GET",url:r,status:n.response?.status||"error",message:e}),n}finally{s().setLoading("fetchPosts",!1)}},fetchComments:async t=>{const r=`/posts/${t}/comments`,a=`comments-${t}`,u=s().cache[a];if(u&&Date.now()-u.timestamp<3e5)return e(e=>{e.comments=u.data}),u.data;s().setLoading("fetchComments",!0),s().setError("fetchComments",null);try{const t=await o.get(r),u=t.data;return e(e=>{e.comments=u,e.cache[a]={data:u,timestamp:Date.now()}}),s().addRequestLog({method:"GET",url:r,status:t.status,message:`成功获取${u.length}条评论`}),u}catch(n){const e=n.response?.data?.message||n.message||"获取评论失败";throw s().setError("fetchComments",e),s().addRequestLog({method:"GET",url:r,status:n.response?.status||"error",message:e}),n}finally{s().setLoading("fetchComments",!1)}},setCurrentUser:s=>e(e=>{e.currentUser=s}),clearError:(s=null)=>e(e=>{s?delete e.errors[s]:e.errors={}}),clearRequestLogs:()=>e(e=>{e.requestLogs=[]}),clearCache:()=>e(e=>{e.cache={}}),reset:()=>e(e=>{e.users=[],e.currentUser=null,e.posts=[],e.comments=[],e.isLoading=!1,e.loadingStates={},e.errors={},e.requestLogs=[],e.cache={}}),getStats:()=>{const e=s();return{totalUsers:e.users.length,totalPosts:e.posts.length,totalComments:e.comments.length,totalRequests:e.requestLogs.length,cacheSize:Object.keys(e.cache).length,hasErrors:Object.keys(e.errors).length>0,isAnyLoading:e.isLoading}},getErrorSummary:()=>{const e=s().errors;return Object.keys(e).map(s=>({operation:s,error:e[s]}))},getRequestsByMethod:e=>s().requestLogs.filter(s=>s.method===e),getSuccessfulRequests:()=>s().requestLogs.filter(e=>"number"==typeof e.status&&e.status>=200&&e.status<300),getFailedRequests:()=>s().requestLogs.filter(e=>"error"===e.status||"number"==typeof e.status&&e.status>=400)}))))),n=e=>e.users,c=e=>e.currentUser,d=e=>e.posts,g=e=>e.loadingStates,m=e=>e.errors,l=e=>e.requestLogs,h=e=>e.getStats();export{h as a,c as b,d as c,g as d,m as e,l as f,n as s,u};
//# sourceMappingURL=store-apistore-B0ecmVeH.js.map
