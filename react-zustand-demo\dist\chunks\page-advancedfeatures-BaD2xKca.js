import{r as s,j as e}from"./react-vendor-CyNirxNk.js";import{u as t,s as a,a as n,b as c,c as i,d as l,e as d}from"./store-basicstore-DgVIqOAD.js";import{u as r,s as o,a as m,b as x,c as h}from"./store-todosstore-DE5vS_Wk.js";import{u as j,s as b,a as u}from"./store-settingsstore-CkjrIQl7.js";import{u as v,s as N,a as p}from"./store-apistore-B0ecmVeH.js";function g(){const g={basicStats:t(c),todosStats:r(x),apiStats:v(p),theme:j(u),language:j(b)},f=function(){const e=t(i),a=t(l),n=t(d),c=s.useMemo(()=>e*a*n+1e3*Math.random(),[e,a,n]);return{count:e,doubleCount:a,tripleCount:n,expensiveCalculation:c}}(),y=function(){const[e,a]=s.useState(!1),n=t(i),c=r(h).length;return s.useEffect(()=>{e&&Math.abs(n-c)},[e,n,c]),{syncEnabled:e,setSyncEnabled:a,count:n,todosCount:c}}(),S=t(a),C=t(n),w=r(o),k=r(m),T=v(N),{increment:M,decrement:D,setName:F,addItem:E,batchUpdate:I}=t(),{addTodo:O,setFilter:$}=r(),{setTheme:L}=j(),[R,A]=s.useState([]),[H,J]=s.useState(0),[U,q]=s.useState({});s.useEffect(()=>{J(s=>{const e=s+1,t=performance.now();return A(s=>[...s.slice(-9),{id:Date.now(),renderCount:e,timestamp:(new Date).toLocaleTimeString(),renderTime:t.toFixed(2)}]),e})});const z=s.useMemo(()=>{const s=performance.now(),e={totalItems:C.length+k.length+T.length,averageLength:((C.length+k.length+T.length)/3).toFixed(1),businessScore:(.3*g.basicStats.totalOperations+.5*g.todosStats.completedCount+.2*g.apiStats.totalRequests).toFixed(1),healthScore:Math.min(100,(g.basicStats.successRate+g.todosStats.completedCount/Math.max(1,g.todosStats.totalCount)*100+10*g.apiStats.totalUsers)/3).toFixed(1)},t=performance.now();return e.computeTime=(t-s).toFixed(2),e},[C.length,k.length,T.length,g]),B=s.useCallback(()=>{performance.now();I({count:f.count+5,name:`批量更新-${Date.now()}`,items:[...C,{id:Date.now(),name:"批量项目",completed:!1}]}),O(`批量Todo-${Date.now()}`);performance.now()},[I,f.count,C,O]);return e.jsxs("div",{className:"advanced-features-page",children:[e.jsxs("div",{className:"card-header",children:[e.jsx("h1",{className:"card-title",children:"🚀 高级特性"}),e.jsx("p",{className:"card-description",children:"展示Zustand的高级功能，包括性能优化、状态计算、跨组件通信等"})]}),e.jsxs("div",{className:"card mb-lg",children:[e.jsx("h2",{className:"card-title",children:"📊 性能监控"}),e.jsxs("div",{className:"grid grid-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary",children:H}),e.jsx("div",{className:"text-sm text-secondary",children:"组件渲染次数"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-success",children:[z.computeTime,"ms"]}),e.jsx("div",{className:"text-sm text-secondary",children:"计算耗时"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-warning",children:f.expensiveCalculation.toFixed(0)}),e.jsx("div",{className:"text-sm text-secondary",children:"复杂计算值"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-info",children:R.length}),e.jsx("div",{className:"text-sm text-secondary",children:"性能日志"})]})]}),e.jsxs("div",{className:"mt-md",children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"最近渲染日志"}),e.jsx("div",{className:"max-h-32 overflow-y-auto space-y-1",children:R.slice(-5).map(s=>e.jsxs("div",{className:"text-xs p-xs bg-surface rounded flex justify-between",children:[e.jsxs("span",{children:["渲染 #",s.renderCount]}),e.jsx("span",{children:s.timestamp}),e.jsxs("span",{children:[s.renderTime,"ms"]})]},s.id))})]})]}),e.jsxs("div",{className:"grid grid-2",children:[e.jsxs("div",{className:"card",children:[e.jsx("h2",{className:"card-title",children:"🎣 自定义Hooks"}),e.jsx("p",{className:"text-sm text-secondary mb-md",children:"封装复杂逻辑，提高代码复用性和可维护性"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"useMultiStoreData"}),e.jsx("pre",{className:"bg-surface p-sm rounded text-xs overflow-auto",children:JSON.stringify(g,null,2)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"useOptimizedSelector"}),e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsxs("div",{children:["基础计数: ",f.count]}),e.jsxs("div",{children:["双倍计数: ",f.doubleCount]}),e.jsxs("div",{children:["三倍计数: ",f.tripleCount]}),e.jsxs("div",{children:["优化计算: ",f.expensiveCalculation.toFixed(2)]})]})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("h2",{className:"card-title",children:"🧮 状态计算"}),e.jsx("p",{className:"text-sm text-secondary mb-md",children:"基于多个store的状态进行复杂计算和业务逻辑处理"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"总项目数:"}),e.jsx("span",{className:"badge badge-primary",children:z.totalItems})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"平均项目数:"}),e.jsx("span",{className:"badge badge-info",children:z.averageLength})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"业务评分:"}),e.jsx("span",{className:"badge badge-success",children:z.businessScore})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"健康度:"}),e.jsxs("span",{className:"badge badge-warning",children:[z.healthScore,"%"]})]})]}),e.jsx("button",{onClick:()=>{const s={timestamp:(new Date).toISOString(),basicStore:{count:f.count,name:S,itemsCount:C.length},todosStore:{filter:w,todosCount:k.length},settingsStore:{theme:g.theme,language:g.language}};q(e=>({...e,lastSnapshot:s}))},className:"btn btn-primary btn-sm w-full mt-md",children:"📸 创建状态快照"})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"🔗 跨Store通信"}),e.jsx("p",{className:"text-sm text-secondary mb-md",children:"演示不同store之间的状态同步和相互影响"}),e.jsxs("div",{className:"grid grid-3",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"状态同步"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{children:"同步开关:"}),e.jsx("input",{type:"checkbox",checked:y.syncEnabled,onChange:s=>y.setSyncEnabled(s.target.checked),className:"form-control w-auto"})]}),e.jsxs("div",{className:"text-sm",children:[e.jsxs("div",{children:["计数器: ",y.count]}),e.jsxs("div",{children:["Todos: ",y.todosCount]}),e.jsxs("div",{children:["差异: ",Math.abs(y.count-y.todosCount)]})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"批量操作"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("button",{onClick:B,className:"btn btn-success btn-sm w-full",children:"🔄 批量更新多Store"}),e.jsx("button",{onClick:()=>{f.count>10?(L("dark"),O(`高级Todo-${f.count}`)):(L("light"),F(`Count: ${f.count}`))},className:"btn btn-warning btn-sm w-full",children:"🔗 跨Store联动"})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"状态快照"}),U.lastSnapshot&&e.jsxs("div",{className:"text-xs",children:[e.jsxs("div",{children:["时间: ",new Date(U.lastSnapshot.timestamp).toLocaleTimeString()]}),e.jsxs("div",{children:["计数: ",U.lastSnapshot.basicStore.count]}),e.jsxs("div",{children:["主题: ",U.lastSnapshot.settingsStore.theme]})]})]})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"⚡ 选择器优化"}),e.jsxs("div",{className:"grid grid-2",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"派生状态示例"}),e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsxs("div",{children:["基础计数: ",f.count]}),e.jsxs("div",{children:["派生双倍: ",f.doubleCount]}),e.jsxs("div",{children:["派生三倍: ",f.tripleCount]}),e.jsxs("div",{children:["复合计算: ",f.expensiveCalculation.toFixed(2)]})]}),e.jsxs("div",{className:"mt-md space-y-1",children:[e.jsx("button",{onClick:M,className:"btn btn-primary btn-sm mr-xs",children:"+1"}),e.jsx("button",{onClick:D,className:"btn btn-secondary btn-sm mr-xs",children:"-1"}),e.jsx("button",{onClick:()=>E(`Item-${Date.now()}`),className:"btn btn-info btn-sm",children:"添加项目"})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"过滤器状态"}),e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsxs("div",{children:["当前过滤: ",w]}),e.jsxs("div",{children:["过滤结果: ",k.length," 项"]})]}),e.jsx("div",{className:"mt-md space-y-1",children:["all","active","completed"].map(s=>e.jsx("button",{onClick:()=>$(s),className:"btn btn-sm mr-xs "+(w===s?"btn-primary":"btn-secondary"),children:s},s))})]})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"🔬 高级模式"}),e.jsxs("div",{className:"grid grid-3",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"内存使用"}),e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsx("div",{children:"Store实例: 4个"}),e.jsx("div",{children:"选择器缓存: 活跃"}),e.jsxs("div",{children:["订阅数量: ",g.basicStats.totalOperations||0]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"热重载支持"}),e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsx("div",{children:"状态保持: ✅"}),e.jsx("div",{children:"开发模式: ✅"}),e.jsx("div",{children:"HMR兼容: ✅"})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-sm",children:"类型安全"}),e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsx("div",{children:"TypeScript: ⚠️ JS模式"}),e.jsx("div",{children:"类型推断: 部分支持"}),e.jsx("div",{children:"智能提示: 基础支持"})]})]})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"💡 最佳实践"}),e.jsxs("div",{className:"grid grid-2",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-md",children:"性能优化"}),e.jsxs("ul",{className:"text-sm text-secondary space-y-1",children:[e.jsx("li",{children:"• 使用选择器避免不必要的重渲染"}),e.jsx("li",{children:"• 合理拆分store避免单一职责过重"}),e.jsx("li",{children:"• 使用useMemo缓存昂贵计算"}),e.jsx("li",{children:"• 批量更新减少状态变更频率"}),e.jsx("li",{children:"• 使用subscribe精确监听状态变化"})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-md",children:"架构设计"}),e.jsxs("ul",{className:"text-sm text-secondary space-y-1",children:[e.jsx("li",{children:"• 按功能模块划分store"}),e.jsx("li",{children:"• 定义清晰的状态接口"}),e.jsx("li",{children:"• 使用中间件增强功能"}),e.jsx("li",{children:"• 封装自定义hooks复用逻辑"}),e.jsx("li",{children:"• 实现良好的错误边界处理"})]})]})]})]}),e.jsxs("div",{className:"card mt-lg",children:[e.jsx("h2",{className:"card-title",children:"🔍 调试工具"}),e.jsxs("div",{className:"flex gap-sm flex-wrap",children:[e.jsx("button",{onClick:()=>{},className:"btn btn-info btn-sm",children:"🐛 打印Basic状态"}),e.jsx("button",{onClick:()=>{},className:"btn btn-info btn-sm",children:"🐛 打印Todos状态"}),e.jsx("button",{onClick:()=>{},className:"btn btn-info btn-sm",children:"🐛 打印Settings状态"}),e.jsx("button",{onClick:()=>{},className:"btn btn-info btn-sm",children:"🐛 打印API状态"}),e.jsx("button",{onClick:()=>{A([]),q({}),J(0)},className:"btn btn-warning btn-sm",children:"🧹 清理调试数据"})]})]})]})}export{g as A};
//# sourceMappingURL=page-advancedfeatures-BaD2xKca.js.map
