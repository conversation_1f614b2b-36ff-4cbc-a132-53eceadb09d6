import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { useBasicStore, selectCount, selectName, selectItems, selectStats, selectDoubleCount, selectTripleCount } from '@/stores/basicStore'
import { useTodosStore, selectTodos, selectFilter, selectFilteredTodos, selectTodoStats } from '@/stores/todosStore'
import { useSettingsStore, selectTheme, selectLanguage } from '@/stores/settingsStore'
import { useApiStore, selectUsers, selectStats as selectApiStats } from '@/stores/apiStore'

// 自定义Hook示例
function useMultiStoreData() {
  return {
    basicStats: useBasicStore(selectStats),
    todosStats: useTodosStore(selectTodoStats),
    apiStats: useApiStore(selectApiStats),
    theme: useSettingsStore(selectTheme),
    language: useSettingsStore(selectLanguage)
  }
}

// 性能优化Hook
function useOptimizedSelector() {
  const count = useBasicStore(selectCount)
  const doubleCount = useBasicStore(selectDoubleCount)
  const tripleCount = useBasicStore(selectTripleCount)
  
  // 使用useMemo优化计算
  const expensiveCalculation = useMemo(() => {
    console.log('执行昂贵计算...')
    return count * doubleCount * tripleCount + Math.random() * 1000
  }, [count, doubleCount, tripleCount])
  
  return { count, doubleCount, tripleCount, expensiveCalculation }
}

// 状态同步Hook
function useStateSynchronization() {
  const [syncEnabled, setSyncEnabled] = useState(false)
  const count = useBasicStore(selectCount)
  const todosCount = useTodosStore(selectTodos).length
  
  useEffect(() => {
    if (syncEnabled) {
      // 同步计数器和todos数量
      const difference = Math.abs(count - todosCount)
      if (difference > 0) {
        console.log(`状态同步: 计数器=${count}, Todos=${todosCount}, 差异=${difference}`)
      }
    }
  }, [syncEnabled, count, todosCount])
  
  return { syncEnabled, setSyncEnabled, count, todosCount }
}

function AdvancedFeatures() {
  // 使用自定义hooks
  const multiStoreData = useMultiStoreData()
  const optimizedData = useOptimizedSelector()
  const syncData = useStateSynchronization()
  
  // 基础状态
  const name = useBasicStore(selectName)
  const items = useBasicStore(selectItems)
  const filter = useTodosStore(selectFilter)
  const filteredTodos = useTodosStore(selectFilteredTodos)
  const users = useApiStore(selectUsers)
  
  // 获取actions
  const { increment, decrement, setName, addItem, batchUpdate } = useBasicStore()
  const { addTodo, setFilter } = useTodosStore()
  const { setTheme } = useSettingsStore()
  
  // 本地状态
  const [performanceLog, setPerformanceLog] = useState([])
  const [renderCount, setRenderCount] = useState(0)
  const [computedValues, setComputedValues] = useState({})
  
  // 性能监控
  useEffect(() => {
    setRenderCount(prev => {
      const newCount = prev + 1
      const renderTime = performance.now()
      setPerformanceLog(prevLog => [...prevLog.slice(-9), {
        id: Date.now(),
        renderCount: newCount,
        timestamp: new Date().toLocaleTimeString(),
        renderTime: renderTime.toFixed(2)
      }])
      return newCount
    })
  })
  
  // 状态计算示例
  const complexCalculations = useMemo(() => {
    const startTime = performance.now()
    
    const calculations = {
      // 多Store聚合计算
      totalItems: items.length + filteredTodos.length + users.length,
      averageLength: ((items.length + filteredTodos.length + users.length) / 3).toFixed(1),
      
      // 复杂业务逻辑
      businessScore: (multiStoreData.basicStats.totalOperations * 0.3 + 
                     multiStoreData.todosStats.completedCount * 0.5 + 
                     multiStoreData.apiStats.totalRequests * 0.2).toFixed(1),
      
      // 状态健康度
      healthScore: Math.min(100, (
        (multiStoreData.basicStats.successRate + 
         (multiStoreData.todosStats.completedCount / Math.max(1, multiStoreData.todosStats.totalCount) * 100) +
         (multiStoreData.apiStats.totalUsers * 10)) / 3
      )).toFixed(1)
    }
    
    const endTime = performance.now()
    calculations.computeTime = (endTime - startTime).toFixed(2)
    
    return calculations
  }, [items.length, filteredTodos.length, users.length, multiStoreData])
  
  // 批量操作示例
  const handleBatchOperations = useCallback(() => {
    const startTime = performance.now()
    
    // 批量更新多个store
    batchUpdate({
      count: optimizedData.count + 5,
      name: `批量更新-${Date.now()}`,
      items: [...items, { id: Date.now(), name: '批量项目', completed: false }]
    })
    
    addTodo(`批量Todo-${Date.now()}`)
    
    const endTime = performance.now()
    console.log(`批量操作耗时: ${(endTime - startTime).toFixed(2)}ms`)
  }, [batchUpdate, optimizedData.count, items, addTodo])
  
  // 跨Store通信示例
  const handleCrossStoreAction = () => {
    // 基于一个store的状态更新另一个store
    if (optimizedData.count > 10) {
      setTheme('dark')
      addTodo(`高级Todo-${optimizedData.count}`)
    } else {
      setTheme('light')
      setName(`Count: ${optimizedData.count}`)
    }
  }
  
  // 状态快照功能
  const createStateSnapshot = () => {
    const snapshot = {
      timestamp: new Date().toISOString(),
      basicStore: {
        count: optimizedData.count,
        name,
        itemsCount: items.length
      },
      todosStore: {
        filter,
        todosCount: filteredTodos.length
      },
      settingsStore: {
        theme: multiStoreData.theme,
        language: multiStoreData.language
      }
    }
    
    setComputedValues(prev => ({
      ...prev,
      lastSnapshot: snapshot
    }))
  }
  
  return (
    <div className="advanced-features-page">
      <div className="card-header">
        <h1 className="card-title">🚀 高级特性</h1>
        <p className="card-description">
          展示Zustand的高级功能，包括性能优化、状态计算、跨组件通信等
        </p>
      </div>

      {/* 性能监控 */}
      <div className="card mb-lg">
        <h2 className="card-title">📊 性能监控</h2>
        <div className="grid grid-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{renderCount}</div>
            <div className="text-sm text-secondary">组件渲染次数</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-success">{complexCalculations.computeTime}ms</div>
            <div className="text-sm text-secondary">计算耗时</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-warning">{optimizedData.expensiveCalculation.toFixed(0)}</div>
            <div className="text-sm text-secondary">复杂计算值</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-info">{performanceLog.length}</div>
            <div className="text-sm text-secondary">性能日志</div>
          </div>
        </div>

        <div className="mt-md">
          <h3 className="font-semibold mb-sm">最近渲染日志</h3>
          <div className="max-h-32 overflow-y-auto space-y-1">
            {performanceLog.slice(-5).map((log) => (
              <div key={log.id} className="text-xs p-xs bg-surface rounded flex justify-between">
                <span>渲染 #{log.renderCount}</span>
                <span>{log.timestamp}</span>
                <span>{log.renderTime}ms</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-2">
        {/* 自定义Hooks */}
        <div className="card">
          <h2 className="card-title">🎣 自定义Hooks</h2>
          <p className="text-sm text-secondary mb-md">
            封装复杂逻辑，提高代码复用性和可维护性
          </p>
          
          <div className="space-y-2">
            <div>
              <h3 className="font-semibold mb-sm">useMultiStoreData</h3>
              <pre className="bg-surface p-sm rounded text-xs overflow-auto">
{JSON.stringify(multiStoreData, null, 2)}
              </pre>
            </div>
            
            <div>
              <h3 className="font-semibold mb-sm">useOptimizedSelector</h3>
              <div className="text-sm space-y-1">
                <div>基础计数: {optimizedData.count}</div>
                <div>双倍计数: {optimizedData.doubleCount}</div>
                <div>三倍计数: {optimizedData.tripleCount}</div>
                <div>优化计算: {optimizedData.expensiveCalculation.toFixed(2)}</div>
              </div>
            </div>
          </div>
        </div>

        {/* 状态计算 */}
        <div className="card">
          <h2 className="card-title">🧮 状态计算</h2>
          <p className="text-sm text-secondary mb-md">
            基于多个store的状态进行复杂计算和业务逻辑处理
          </p>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>总项目数:</span>
              <span className="badge badge-primary">{complexCalculations.totalItems}</span>
            </div>
            <div className="flex justify-between">
              <span>平均项目数:</span>
              <span className="badge badge-info">{complexCalculations.averageLength}</span>
            </div>
            <div className="flex justify-between">
              <span>业务评分:</span>
              <span className="badge badge-success">{complexCalculations.businessScore}</span>
            </div>
            <div className="flex justify-between">
              <span>健康度:</span>
              <span className="badge badge-warning">{complexCalculations.healthScore}%</span>
            </div>
          </div>
          
          <button
            onClick={createStateSnapshot}
            className="btn btn-primary btn-sm w-full mt-md"
          >
            📸 创建状态快照
          </button>
        </div>
      </div>

      {/* 跨Store通信 */}
      <div className="card mt-lg">
        <h2 className="card-title">🔗 跨Store通信</h2>
        <p className="text-sm text-secondary mb-md">
          演示不同store之间的状态同步和相互影响
        </p>
        
        <div className="grid grid-3">
          <div>
            <h3 className="font-semibold mb-sm">状态同步</h3>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span>同步开关:</span>
                <input
                  type="checkbox"
                  checked={syncData.syncEnabled}
                  onChange={(e) => syncData.setSyncEnabled(e.target.checked)}
                  className="form-control w-auto"
                />
              </div>
              <div className="text-sm">
                <div>计数器: {syncData.count}</div>
                <div>Todos: {syncData.todosCount}</div>
                <div>差异: {Math.abs(syncData.count - syncData.todosCount)}</div>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold mb-sm">批量操作</h3>
            <div className="space-y-2">
              <button
                onClick={handleBatchOperations}
                className="btn btn-success btn-sm w-full"
              >
                🔄 批量更新多Store
              </button>
              <button
                onClick={handleCrossStoreAction}
                className="btn btn-warning btn-sm w-full"
              >
                🔗 跨Store联动
              </button>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold mb-sm">状态快照</h3>
            {computedValues.lastSnapshot && (
              <div className="text-xs">
                <div>时间: {new Date(computedValues.lastSnapshot.timestamp).toLocaleTimeString()}</div>
                <div>计数: {computedValues.lastSnapshot.basicStore.count}</div>
                <div>主题: {computedValues.lastSnapshot.settingsStore.theme}</div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 选择器优化 */}
      <div className="card mt-lg">
        <h2 className="card-title">⚡ 选择器优化</h2>
        <div className="grid grid-2">
          <div>
            <h3 className="font-semibold mb-sm">派生状态示例</h3>
            <div className="text-sm space-y-1">
              <div>基础计数: {optimizedData.count}</div>
              <div>派生双倍: {optimizedData.doubleCount}</div>
              <div>派生三倍: {optimizedData.tripleCount}</div>
              <div>复合计算: {optimizedData.expensiveCalculation.toFixed(2)}</div>
            </div>
            
            <div className="mt-md space-y-1">
              <button onClick={increment} className="btn btn-primary btn-sm mr-xs">+1</button>
              <button onClick={decrement} className="btn btn-secondary btn-sm mr-xs">-1</button>
              <button onClick={() => addItem(`Item-${Date.now()}`)} className="btn btn-info btn-sm">
                添加项目
              </button>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold mb-sm">过滤器状态</h3>
            <div className="text-sm space-y-1">
              <div>当前过滤: {filter}</div>
              <div>过滤结果: {filteredTodos.length} 项</div>
            </div>
            
            <div className="mt-md space-y-1">
              {['all', 'active', 'completed'].map((filterType) => (
                <button
                  key={filterType}
                  onClick={() => setFilter(filterType)}
                  className={`btn btn-sm mr-xs ${filter === filterType ? 'btn-primary' : 'btn-secondary'}`}
                >
                  {filterType}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 高级模式 */}
      <div className="card mt-lg">
        <h2 className="card-title">🔬 高级模式</h2>
        <div className="grid grid-3">
          <div>
            <h3 className="font-semibold mb-sm">内存使用</h3>
            <div className="text-sm space-y-1">
              <div>Store实例: 4个</div>
              <div>选择器缓存: 活跃</div>
              <div>订阅数量: {multiStoreData.basicStats.totalOperations || 0}</div>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold mb-sm">热重载支持</h3>
            <div className="text-sm space-y-1">
              <div>状态保持: ✅</div>
              <div>开发模式: ✅</div>
              <div>HMR兼容: ✅</div>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold mb-sm">类型安全</h3>
            <div className="text-sm space-y-1">
              <div>TypeScript: ⚠️ JS模式</div>
              <div>类型推断: 部分支持</div>
              <div>智能提示: 基础支持</div>
            </div>
          </div>
        </div>
      </div>

      {/* 最佳实践 */}
      <div className="card mt-lg">
        <h2 className="card-title">💡 最佳实践</h2>
        <div className="grid grid-2">
          <div>
            <h3 className="font-semibold mb-md">性能优化</h3>
            <ul className="text-sm text-secondary space-y-1">
              <li>• 使用选择器避免不必要的重渲染</li>
              <li>• 合理拆分store避免单一职责过重</li>
              <li>• 使用useMemo缓存昂贵计算</li>
              <li>• 批量更新减少状态变更频率</li>
              <li>• 使用subscribe精确监听状态变化</li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-md">架构设计</h3>
            <ul className="text-sm text-secondary space-y-1">
              <li>• 按功能模块划分store</li>
              <li>• 定义清晰的状态接口</li>
              <li>• 使用中间件增强功能</li>
              <li>• 封装自定义hooks复用逻辑</li>
              <li>• 实现良好的错误边界处理</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 调试工具 */}
      <div className="card mt-lg">
        <h2 className="card-title">🔍 调试工具</h2>
        <div className="flex gap-sm flex-wrap">
          <button
            onClick={() => console.log('BasicStore状态:', useBasicStore.getState())}
            className="btn btn-info btn-sm"
          >
            🐛 打印Basic状态
          </button>
          <button
            onClick={() => console.log('TodosStore状态:', useTodosStore.getState())}
            className="btn btn-info btn-sm"
          >
            🐛 打印Todos状态
          </button>
          <button
            onClick={() => console.log('SettingsStore状态:', useSettingsStore.getState())}
            className="btn btn-info btn-sm"
          >
            🐛 打印Settings状态
          </button>
          <button
            onClick={() => console.log('ApiStore状态:', useApiStore.getState())}
            className="btn btn-info btn-sm"
          >
            🐛 打印API状态
          </button>
          <button
            onClick={() => {
              setPerformanceLog([])
              setComputedValues({})
              setRenderCount(0)
            }}
            className="btn btn-warning btn-sm"
          >
            🧹 清理调试数据
          </button>
        </div>
      </div>
    </div>
  )
}

export default AdvancedFeatures 