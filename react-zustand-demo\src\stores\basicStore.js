import { create } from 'zustand'
import { devtools, subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

// 基础store - 展示Zustand核心功能
export const useBasicStore = create(
  devtools(
    subscribeWithSelector(
      immer((set, get) => ({
        // 状态
        count: 0,
        name: '',
        items: [],
        user: { name: '', email: '' },
        isLoading: false,
        error: null,
        history: [],

        // 基础计数器操作
        increment: () => set((state) => {
          state.count += 1
          state.history.push({
            action: 'increment',
            timestamp: new Date().toISOString(),
            value: state.count
          })
        }),

        decrement: () => set((state) => {
          state.count -= 1
          state.history.push({
            action: 'decrement',
            timestamp: new Date().toISOString(),
            value: state.count
          })
        }),

        incrementByAmount: (amount) => set((state) => {
          state.count += amount
          state.history.push({
            action: 'incrementByAmount',
            timestamp: new Date().toISOString(),
            value: state.count,
            amount
          })
        }),

        // 重置计数器
        resetCount: () => set((state) => {
          state.count = 0
          state.history.push({
            action: 'resetCount',
            timestamp: new Date().toISOString(),
            value: 0
          })
        }),

        // 姓名管理
        setName: (name) => set((state) => {
          state.name = name
          state.history.push({
            action: 'setName',
            timestamp: new Date().toISOString(),
            value: name
          })
        }),

        // 项目列表管理
        addItem: (item) => set((state) => {
          const newItem = {
            id: Date.now(),
            text: item,
            createdAt: new Date().toISOString()
          }
          state.items.push(newItem)
          state.history.push({
            action: 'addItem',
            timestamp: new Date().toISOString(),
            value: item
          })
        }),

        removeItem: (id) => set((state) => {
          const index = state.items.findIndex(item => item.id === id)
          if (index > -1) {
            const removedItem = state.items[index]
            state.items.splice(index, 1)
            state.history.push({
              action: 'removeItem',
              timestamp: new Date().toISOString(),
              value: removedItem.text
            })
          }
        }),

        updateItem: (id, newText) => set((state) => {
          const item = state.items.find(item => item.id === id)
          if (item) {
            const oldText = item.text
            item.text = newText
            item.updatedAt = new Date().toISOString()
            state.history.push({
              action: 'updateItem',
              timestamp: new Date().toISOString(),
              value: `${oldText} → ${newText}`
            })
          }
        }),

        // 用户信息管理
        updateUser: (userData) => set((state) => {
          Object.assign(state.user, userData)
          state.history.push({
            action: 'updateUser',
            timestamp: new Date().toISOString(),
            value: userData
          })
        }),

        // 批量更新
        batchUpdate: (updates) => set((state) => {
          if (updates.count !== undefined) state.count = updates.count
          if (updates.name !== undefined) state.name = updates.name
          if (updates.user !== undefined) Object.assign(state.user, updates.user)
          
          state.history.push({
            action: 'batchUpdate',
            timestamp: new Date().toISOString(),
            value: updates
          })
        }),

        // 异步操作模拟
        simulateAsyncOperation: async (delay = 2000) => {
          set((state) => {
            state.isLoading = true
            state.error = null
          })

          try {
            await new Promise((resolve, reject) => {
              setTimeout(() => {
                // 随机决定成功或失败
                if (Math.random() > 0.8) {
                  reject(new Error('模拟的异步操作失败'))
                } else {
                  resolve()
                }
              }, delay)
            })

            set((state) => {
              state.isLoading = false
              state.count += 10
              state.history.push({
                action: 'simulateAsyncOperation',
                timestamp: new Date().toISOString(),
                value: '异步操作成功'
              })
            })
          } catch (error) {
            set((state) => {
              state.isLoading = false
              state.error = error.message
              state.history.push({
                action: 'simulateAsyncOperation',
                timestamp: new Date().toISOString(),
                value: `异步操作失败: ${error.message}`
              })
            })
          }
        },

        // 清除错误
        clearError: () => set((state) => {
          state.error = null
        }),

        // 清除历史记录
        clearHistory: () => set((state) => {
          state.history = []
        }),

        // 重置整个store
        reset: () => set((state) => {
          state.count = 0
          state.name = ''
          state.items = []
          state.user = { name: '', email: '' }
          state.isLoading = false
          state.error = null
          state.history = [{
            action: 'reset',
            timestamp: new Date().toISOString(),
            value: '状态已重置'
          }]
        }),

        // 计算属性 (使用selectors)
        getDoubleCount: () => get().count * 2,
        getTripleCount: () => get().count * 3,
        getFormattedUser: () => {
          const { user } = get()
          return user.name && user.email 
            ? `${user.name} (${user.email})`
            : '未设置用户信息'
        },
        getRecentHistory: () => get().history.slice(-10),
        getStats: () => {
          const state = get()
          return {
            totalItems: state.items.length,
            totalActions: state.history.length,
            lastAction: state.history[state.history.length - 1]?.action || 'none',
            hasError: !!state.error,
            isActive: state.count > 0 || state.items.length > 0
          }
        }
      })),
      { name: 'basic-store' }
    )
  )
)

// Selectors - 用于计算派生状态
export const selectCount = (state) => state.count
export const selectDoubleCount = (state) => state.count * 2
export const selectTripleCount = (state) => state.count * 3
export const selectName = (state) => state.name
export const selectItems = (state) => state.items
export const selectUser = (state) => state.user
export const selectFormattedUser = (state) => {
  const user = state.user
  return user.name && user.email 
    ? `${user.name} (${user.email})`
    : '未设置用户信息'
}
export const selectIsLoading = (state) => state.isLoading
export const selectError = (state) => state.error
export const selectHistory = (state) => state.history
export const selectRecentHistory = (state) => state.history.slice(-10)
export const selectStats = (state) => ({
  totalItems: state.items.length,
  totalActions: state.history.length,
  lastAction: state.history[state.history.length - 1]?.action || 'none',
  hasError: !!state.error,
  isActive: state.count > 0 || state.items.length > 0
})

// 订阅示例 - 监听特定状态变化
export const subscribeToCount = (callback) => {
  return useBasicStore.subscribe(
    (state) => state.count,
    callback
  )
}

export const subscribeToError = (callback) => {
  return useBasicStore.subscribe(
    (state) => state.error,
    callback
  )
} 