import{r as e,j as s}from"./react-vendor-CyNirxNk.js";import{u as a,c as t,d as n,e as l,s as c,a as r,f as i,g as m,h as d,i as x}from"./store-basicstore-DgVIqOAD.js";function o(){const o=a(t),h=a(n),j=a(l),b=a(c),N=a(r),u=a(i),p=a(m),v=a(d),g=a(x),{increment:f,decrement:y,incrementByAmount:C,resetCount:k,setName:S,addItem:w,removeItem:F,updateItem:E,updateUser:I,batchUpdate:A,simulateAsyncOperation:B,clearError:K,clearHistory:O,reset:P}=a(),[U,z]=e.useState(5),[D,H]=e.useState(""),[J,L]=e.useState(""),[T,Z]=e.useState(-1),[$,q]=e.useState(""),[G,M]=e.useState({name:"",email:""}),Q=()=>{D.trim()&&(S(D.trim()),H(""))},R=()=>{J.trim()&&(w(J.trim()),L(""))},V=()=>{$.trim()&&E(N[T].id,$.trim()),Z(-1),q("")},W=()=>{Z(-1),q("")};return s.jsxs("div",{className:"basic-store-page",children:[s.jsxs("div",{className:"card-header",children:[s.jsx("h1",{className:"card-title",children:"📦 基础Store演示"}),s.jsx("p",{className:"card-description",children:"展示Zustand的基础功能：状态管理、选择器、异步操作和历史记录"})]}),v&&s.jsxs("div",{className:"alert alert-danger",children:[s.jsx("strong",{children:"错误："})," ",v,s.jsx("button",{onClick:K,className:"btn btn-sm ml-auto",children:"清除"})]}),s.jsxs("div",{className:"grid grid-2",children:[s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"card-title",children:"🔢 计数器操作"}),s.jsxs("div",{className:"text-center mb-lg",children:[s.jsx("div",{className:"text-2xl font-bold text-primary mb-sm",children:o}),s.jsxs("div",{className:"text-sm text-muted",children:["双倍: ",h," | 三倍: ",j]})]}),s.jsxs("div",{className:"flex gap-sm mb-md",children:[s.jsx("button",{onClick:f,className:"btn btn-success flex-1",children:"➕ 增加"}),s.jsx("button",{onClick:y,className:"btn btn-danger flex-1",children:"➖ 减少"})]}),s.jsxs("div",{className:"flex gap-sm mb-md",children:[s.jsx("input",{type:"number",value:U,onChange:e=>z(Number(e.target.value)),className:"form-control",placeholder:"增加数量"}),s.jsxs("button",{onClick:()=>{C(U)},className:"btn btn-primary",children:["增加 ",U]})]}),s.jsx("button",{onClick:k,className:"btn btn-secondary btn-sm",children:"🔄 重置计数器"})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"card-title",children:"👤 姓名管理"}),s.jsx("div",{className:"mb-md",children:s.jsxs("div",{className:"text-lg font-semibold",children:["当前姓名: ",s.jsx("span",{className:"text-primary",children:b||"未设置"})]})}),s.jsxs("div",{className:"flex gap-sm",children:[s.jsx("input",{type:"text",value:D,onChange:e=>H(e.target.value),onKeyPress:e=>"Enter"===e.key&&Q(),className:"form-control",placeholder:"输入新姓名"}),s.jsx("button",{onClick:Q,className:"btn btn-primary",children:"设置"})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"card-title",children:"📝 项目列表管理"}),s.jsxs("div",{className:"flex gap-sm mb-md",children:[s.jsx("input",{type:"text",value:J,onChange:e=>L(e.target.value),onKeyPress:e=>"Enter"===e.key&&R(),className:"form-control",placeholder:"添加新项目"}),s.jsx("button",{onClick:R,className:"btn btn-success",children:"➕ 添加"})]}),s.jsxs("div",{className:"space-y-2",children:[N.map((e,a)=>s.jsx("div",{className:"flex items-center gap-sm p-sm border rounded",children:T===a?s.jsxs(s.Fragment,{children:[s.jsx("input",{type:"text",value:$,onChange:e=>q(e.target.value),className:"form-control flex-1",autoFocus:!0}),s.jsx("button",{onClick:V,className:"btn btn-success btn-sm",children:"✓"}),s.jsx("button",{onClick:W,className:"btn btn-secondary btn-sm",children:"✗"})]}):s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"flex-1",children:e.text}),s.jsx("button",{onClick:()=>(e=>{Z(e),q(N[e].text)})(a),className:"btn btn-primary btn-sm",children:"✏️"}),s.jsx("button",{onClick:()=>F(e.id),className:"btn btn-danger btn-sm",children:"🗑️"})]})},e.id)),0===N.length&&s.jsx("div",{className:"text-center text-muted py-lg",children:"暂无项目，请添加一些项目"})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"card-title",children:"👨‍💼 用户信息管理"}),s.jsx("div",{className:"mb-md",children:s.jsx("div",{className:"text-lg font-semibold",children:u})}),s.jsxs("div",{className:"space-y-2 mb-md",children:[s.jsx("input",{type:"text",value:G.name,onChange:e=>M({...G,name:e.target.value}),className:"form-control",placeholder:"用户姓名"}),s.jsx("input",{type:"email",value:G.email,onChange:e=>M({...G,email:e.target.value}),className:"form-control",placeholder:"用户邮箱"})]}),s.jsx("button",{onClick:()=>{(G.name.trim()||G.email.trim())&&(I(G),M({name:"",email:""}))},className:"btn btn-primary",children:"更新用户信息"})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"card-title",children:"⚡ 异步操作演示"}),s.jsx("div",{className:"mb-md",children:s.jsx("p",{className:"text-sm text-secondary",children:"模拟异步操作，随机成功或失败（80%成功率）"})}),s.jsx("div",{className:"flex gap-sm",children:s.jsx("button",{onClick:()=>B(1e3),disabled:p,className:"btn btn-warning flex-1",children:p?s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"spinner"}),"执行中..."]}):"🚀 执行异步操作"})})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"card-title",children:"📦 批量操作"}),s.jsxs("div",{className:"flex gap-sm mb-md",children:[s.jsx("button",{onClick:()=>{A({count:100,name:"批量更新用户",user:{name:"张三",email:"<EMAIL>"}})},className:"btn btn-info",children:"🔄 批量更新"}),s.jsx("button",{onClick:P,className:"btn btn-danger",children:"🗑️ 重置所有"})]})]})]}),s.jsxs("div",{className:"card mt-xl",children:[s.jsxs("div",{className:"flex justify-between items-center mb-md",children:[s.jsx("h2",{className:"card-title",children:"📜 操作历史（最近10条）"}),s.jsx("button",{onClick:O,className:"btn btn-secondary btn-sm",children:"清空历史"})]}),s.jsxs("div",{className:"space-y-1",children:[g.map((e,a)=>s.jsxs("div",{className:"flex justify-between items-center p-sm bg-surface rounded text-sm",children:[s.jsxs("span",{children:[s.jsx("strong",{children:e.action}),e.value&&`: ${JSON.stringify(e.value)}`]}),s.jsx("span",{className:"text-muted",children:new Date(e.timestamp).toLocaleTimeString()})]},a)),0===g.length&&s.jsx("div",{className:"text-center text-muted py-lg",children:"暂无操作历史"})]})]})]})}export{o as B};
//# sourceMappingURL=page-basicstore-ECzgDzuM.js.map
